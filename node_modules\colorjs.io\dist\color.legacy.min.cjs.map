{"version": 3, "file": "color.legacy.min.cjs", "sources": ["../node_modules/core-js/internals/global.js", "../node_modules/core-js/internals/fails.js", "../node_modules/core-js/internals/descriptors.js", "../node_modules/core-js/internals/function-bind-native.js", "../node_modules/core-js/internals/function-call.js", "../node_modules/core-js/internals/create-property-descriptor.js", "../node_modules/core-js/internals/function-uncurry-this.js", "../node_modules/core-js/internals/classof-raw.js", "../node_modules/core-js/internals/is-null-or-undefined.js", "../node_modules/core-js/internals/require-object-coercible.js", "../node_modules/core-js/internals/to-indexed-object.js", "../node_modules/core-js/internals/indexed-object.js", "../node_modules/core-js/internals/is-callable.js", "../node_modules/core-js/internals/is-object.js", "../node_modules/core-js/internals/get-built-in.js", "../node_modules/core-js/internals/object-is-prototype-of.js", "../node_modules/core-js/internals/engine-v8-version.js", "../node_modules/core-js/internals/engine-user-agent.js", "../node_modules/core-js/internals/symbol-constructor-detection.js", "../node_modules/core-js/internals/use-symbol-as-uid.js", "../node_modules/core-js/internals/is-symbol.js", "../node_modules/core-js/internals/try-to-string.js", "../node_modules/core-js/internals/a-callable.js", "../node_modules/core-js/internals/get-method.js", "../node_modules/core-js/internals/ordinary-to-primitive.js", "../node_modules/core-js/internals/is-pure.js", "../node_modules/core-js/internals/define-global-property.js", "../node_modules/core-js/internals/shared-store.js", "../node_modules/core-js/internals/shared.js", "../node_modules/core-js/internals/to-object.js", "../node_modules/core-js/internals/has-own-property.js", "../node_modules/core-js/internals/uid.js", "../node_modules/core-js/internals/well-known-symbol.js", "../node_modules/core-js/internals/to-primitive.js", "../node_modules/core-js/internals/to-property-key.js", "../node_modules/core-js/internals/ie8-dom-define.js", "../node_modules/core-js/internals/document-create-element.js", "../node_modules/core-js/internals/object-get-own-property-descriptor.js", "../node_modules/core-js/internals/object-property-is-enumerable.js", "../node_modules/core-js/internals/an-object.js", "../node_modules/core-js/internals/object-define-property.js", "../node_modules/core-js/internals/v8-prototype-define-bug.js", "../node_modules/core-js/internals/create-non-enumerable-property.js", "../node_modules/core-js/internals/inspect-source.js", "../node_modules/core-js/internals/shared-key.js", "../node_modules/core-js/internals/hidden-keys.js", "../node_modules/core-js/internals/internal-state.js", "../node_modules/core-js/internals/weak-map-basic-detection.js", "../node_modules/core-js/internals/make-built-in.js", "../node_modules/core-js/internals/function-name.js", "../node_modules/core-js/internals/define-built-in.js", "../node_modules/core-js/internals/to-integer-or-infinity.js", "../node_modules/core-js/internals/math-trunc.js", "../node_modules/core-js/internals/to-absolute-index.js", "../node_modules/core-js/internals/to-length.js", "../node_modules/core-js/internals/length-of-array-like.js", "../node_modules/core-js/internals/object-keys-internal.js", "../node_modules/core-js/internals/array-includes.js", "../node_modules/core-js/internals/own-keys.js", "../node_modules/core-js/internals/object-get-own-property-names.js", "../node_modules/core-js/internals/enum-bug-keys.js", "../node_modules/core-js/internals/object-get-own-property-symbols.js", "../node_modules/core-js/internals/copy-constructor-properties.js", "../node_modules/core-js/internals/export.js", "../node_modules/core-js/internals/is-forced.js", "../node_modules/core-js/internals/is-array.js", "../node_modules/core-js/internals/array-set-length.js", "../node_modules/core-js/internals/does-not-exceed-safe-integer.js", "../src/multiply-matrices.js", "../src/util.js", "../node_modules/core-js/modules/es.array.push.js", "../src/hooks.js", "../src/defaults.js", "../node_modules/core-js/internals/is-possible-prototype.js", "../node_modules/core-js/internals/a-possible-prototype.js", "../node_modules/core-js/internals/object-set-prototype-of.js", "../node_modules/core-js/internals/function-uncurry-this-accessor.js", "../node_modules/core-js/internals/proxy-accessor.js", "../node_modules/core-js/internals/inherit-if-required.js", "../node_modules/core-js/internals/classof.js", "../node_modules/core-js/internals/to-string-tag-support.js", "../node_modules/core-js/internals/to-string.js", "../node_modules/core-js/internals/normalize-string-argument.js", "../node_modules/core-js/internals/install-error-cause.js", "../node_modules/core-js/internals/error-stack-install.js", "../node_modules/core-js/internals/error-stack-clear.js", "../node_modules/core-js/internals/error-stack-installable.js", "../node_modules/core-js/internals/wrap-error-constructor-with-cause.js", "../node_modules/core-js/modules/es.error.cause.js", "../node_modules/core-js/internals/function-apply.js", "../src/adapt.js", "../src/parse.js", "../src/getColor.js", "../src/space.js", "../src/spaces/xyz-d65.js", "../src/rgbspace.js", "../src/getAll.js", "../src/get.js", "../src/setAll.js", "../src/set.js", "../src/spaces/xyz-d50.js", "../src/spaces/lab.js", "../src/angles.js", "../src/spaces/lch.js", "../src/deltaE/deltaE2000.js", "../src/spaces/oklab.js", "../src/deltaE/deltaEOK.js", "../src/inGamut.js", "../src/clone.js", "../src/distance.js", "../src/deltaE/deltaECMC.js", "../src/spaces/xyz-abs-d65.js", "../src/spaces/jzazbz.js", "../src/spaces/jzczhz.js", "../src/spaces/ictcp.js", "../src/spaces/cam16.js", "../src/spaces/hct.js", "../src/deltaE/deltaEHCT.js", "../src/deltaE/index.js", "../src/deltaE/deltaE76.js", "../src/deltaE/deltaEJz.js", "../src/deltaE/deltaEITP.js", "../src/toGamut.js", "../src/to.js", "../node_modules/core-js/internals/delete-property-or-throw.js", "../src/serialize.js", "../node_modules/core-js/modules/es.array.unshift.js", "../src/spaces/rec2020-linear.js", "../src/spaces/rec2020.js", "../src/spaces/p3-linear.js", "../src/spaces/srgb-linear.js", "../src/keywords.js", "../src/spaces/srgb.js", "../src/spaces/p3.js", "../src/display.js", "../src/luminance.js", "../src/contrast/APCA.js", "../src/spaces/lab-d65.js", "../src/contrast/deltaPhi.js", "../src/contrast/Lstar.js", "../src/contrast/Michelson.js", "../src/contrast/WCAG21.js", "../src/contrast/Weber.js", "../src/chromaticity.js", "../src/deltaE.js", "../src/variations.js", "../src/interpolation.js", "../src/spaces/hsl.js", "../src/spaces/hsv.js", "../src/spaces/hwb.js", "../src/spaces/a98rgb-linear.js", "../src/spaces/a98rgb.js", "../src/spaces/prophoto-linear.js", "../src/spaces/prophoto.js", "../src/spaces/oklch.js", "../src/spaces/luv.js", "../src/spaces/lchuv.js", "../src/spaces/hsluv.js", "../src/spaces/hpluv.js", "../src/spaces/rec2100-pq.js", "../src/spaces/rec2100-hlg.js", "../src/CATs.js", "../src/spaces/acescg.js", "../src/spaces/acescc.js", "../src/color.js", "../src/equals.js", "../src/spaces/index.js", "../node_modules/core-js/internals/set-to-string-tag.js", "../node_modules/core-js/modules/es.reflect.to-string-tag.js", "../src/space-accessors.js", "../src/index.js", "../src/contrast.js"], "sourcesContent": ["'use strict';\nvar check = function (it) {\n  return it && it.Math === Math && it;\n};\n\n// https://github.com/zloirock/core-js/issues/86#issuecomment-115759028\nmodule.exports =\n  // eslint-disable-next-line es/no-global-this -- safe\n  check(typeof globalThis == 'object' && globalThis) ||\n  check(typeof window == 'object' && window) ||\n  // eslint-disable-next-line no-restricted-globals -- safe\n  check(typeof self == 'object' && self) ||\n  check(typeof global == 'object' && global) ||\n  check(typeof this == 'object' && this) ||\n  // eslint-disable-next-line no-new-func -- fallback\n  (function () { return this; })() || Function('return this')();\n", "'use strict';\nmodule.exports = function (exec) {\n  try {\n    return !!exec();\n  } catch (error) {\n    return true;\n  }\n};\n", "'use strict';\nvar fails = require('../internals/fails');\n\n// Detect IE8's incomplete defineProperty implementation\nmodule.exports = !fails(function () {\n  // eslint-disable-next-line es/no-object-defineproperty -- required for testing\n  return Object.defineProperty({}, 1, { get: function () { return 7; } })[1] !== 7;\n});\n", "'use strict';\nvar fails = require('../internals/fails');\n\nmodule.exports = !fails(function () {\n  // eslint-disable-next-line es/no-function-prototype-bind -- safe\n  var test = (function () { /* empty */ }).bind();\n  // eslint-disable-next-line no-prototype-builtins -- safe\n  return typeof test != 'function' || test.hasOwnProperty('prototype');\n});\n", "'use strict';\nvar NATIVE_BIND = require('../internals/function-bind-native');\n\nvar call = Function.prototype.call;\n\nmodule.exports = NATIVE_BIND ? call.bind(call) : function () {\n  return call.apply(call, arguments);\n};\n", "'use strict';\nmodule.exports = function (bitmap, value) {\n  return {\n    enumerable: !(bitmap & 1),\n    configurable: !(bitmap & 2),\n    writable: !(bitmap & 4),\n    value: value\n  };\n};\n", "'use strict';\nvar NATIVE_BIND = require('../internals/function-bind-native');\n\nvar FunctionPrototype = Function.prototype;\nvar call = FunctionPrototype.call;\nvar uncurryThisWithBind = NATIVE_BIND && FunctionPrototype.bind.bind(call, call);\n\nmodule.exports = NATIVE_BIND ? uncurryThisWithBind : function (fn) {\n  return function () {\n    return call.apply(fn, arguments);\n  };\n};\n", "'use strict';\nvar uncurryThis = require('../internals/function-uncurry-this');\n\nvar toString = uncurryThis({}.toString);\nvar stringSlice = uncurryThis(''.slice);\n\nmodule.exports = function (it) {\n  return stringSlice(toString(it), 8, -1);\n};\n", "'use strict';\n// we can't use just `it == null` since of `document.all` special case\n// https://tc39.es/ecma262/#sec-IsHTMLDDA-internal-slot-aec\nmodule.exports = function (it) {\n  return it === null || it === undefined;\n};\n", "'use strict';\nvar isNullOrUndefined = require('../internals/is-null-or-undefined');\n\nvar $TypeError = TypeError;\n\n// `RequireObjectCoercible` abstract operation\n// https://tc39.es/ecma262/#sec-requireobjectcoercible\nmodule.exports = function (it) {\n  if (isNullOrUndefined(it)) throw new $TypeError(\"Can't call method on \" + it);\n  return it;\n};\n", "'use strict';\n// toObject with fallback for non-array-like ES3 strings\nvar IndexedObject = require('../internals/indexed-object');\nvar requireObjectCoercible = require('../internals/require-object-coercible');\n\nmodule.exports = function (it) {\n  return IndexedObject(requireObjectCoercible(it));\n};\n", "'use strict';\nvar uncurryThis = require('../internals/function-uncurry-this');\nvar fails = require('../internals/fails');\nvar classof = require('../internals/classof-raw');\n\nvar $Object = Object;\nvar split = uncurryThis(''.split);\n\n// fallback for non-array-like ES3 and non-enumerable old V8 strings\nmodule.exports = fails(function () {\n  // throws an error in rhino, see https://github.com/mozilla/rhino/issues/346\n  // eslint-disable-next-line no-prototype-builtins -- safe\n  return !$Object('z').propertyIsEnumerable(0);\n}) ? function (it) {\n  return classof(it) === 'String' ? split(it, '') : $Object(it);\n} : $Object;\n", "'use strict';\n// https://tc39.es/ecma262/#sec-IsHTMLDDA-internal-slot\nvar documentAll = typeof document == 'object' && document.all;\n\n// `IsCallable` abstract operation\n// https://tc39.es/ecma262/#sec-iscallable\n// eslint-disable-next-line unicorn/no-typeof-undefined -- required for testing\nmodule.exports = typeof documentAll == 'undefined' && documentAll !== undefined ? function (argument) {\n  return typeof argument == 'function' || argument === documentAll;\n} : function (argument) {\n  return typeof argument == 'function';\n};\n", "'use strict';\nvar isCallable = require('../internals/is-callable');\n\nmodule.exports = function (it) {\n  return typeof it == 'object' ? it !== null : isCallable(it);\n};\n", "'use strict';\nvar global = require('../internals/global');\nvar isCallable = require('../internals/is-callable');\n\nvar aFunction = function (argument) {\n  return isCallable(argument) ? argument : undefined;\n};\n\nmodule.exports = function (namespace, method) {\n  return arguments.length < 2 ? aFunction(global[namespace]) : global[namespace] && global[namespace][method];\n};\n", "'use strict';\nvar uncurryThis = require('../internals/function-uncurry-this');\n\nmodule.exports = uncurryThis({}.isPrototypeOf);\n", "'use strict';\nvar global = require('../internals/global');\nvar userAgent = require('../internals/engine-user-agent');\n\nvar process = global.process;\nvar Deno = global.Deno;\nvar versions = process && process.versions || Deno && Deno.version;\nvar v8 = versions && versions.v8;\nvar match, version;\n\nif (v8) {\n  match = v8.split('.');\n  // in old Chrome, versions of V8 isn't V8 = Chrome / 10\n  // but their correct versions are not interesting for us\n  version = match[0] > 0 && match[0] < 4 ? 1 : +(match[0] + match[1]);\n}\n\n// BrowserFS NodeJS `process` polyfill incorrectly set `.v8` to `0.0`\n// so check `userAgent` even if `.v8` exists, but 0\nif (!version && userAgent) {\n  match = userAgent.match(/Edge\\/(\\d+)/);\n  if (!match || match[1] >= 74) {\n    match = userAgent.match(/Chrome\\/(\\d+)/);\n    if (match) version = +match[1];\n  }\n}\n\nmodule.exports = version;\n", "'use strict';\nmodule.exports = typeof navigator != 'undefined' && String(navigator.userAgent) || '';\n", "'use strict';\n/* eslint-disable es/no-symbol -- required for testing */\nvar V8_VERSION = require('../internals/engine-v8-version');\nvar fails = require('../internals/fails');\nvar global = require('../internals/global');\n\nvar $String = global.String;\n\n// eslint-disable-next-line es/no-object-getownpropertysymbols -- required for testing\nmodule.exports = !!Object.getOwnPropertySymbols && !fails(function () {\n  var symbol = Symbol('symbol detection');\n  // Chrome 38 Symbol has incorrect toString conversion\n  // `get-own-property-symbols` polyfill symbols converted to object are not Symbol instances\n  // nb: Do not call `String` directly to avoid this being optimized out to `symbol+''` which will,\n  // of course, fail.\n  return !$String(symbol) || !(Object(symbol) instanceof Symbol) ||\n    // Chrome 38-40 symbols are not inherited from DOM collections prototypes to instances\n    !Symbol.sham && V8_VERSION && V8_VERSION < 41;\n});\n", "'use strict';\n/* eslint-disable es/no-symbol -- required for testing */\nvar NATIVE_SYMBOL = require('../internals/symbol-constructor-detection');\n\nmodule.exports = NATIVE_SYMBOL\n  && !Symbol.sham\n  && typeof Symbol.iterator == 'symbol';\n", "'use strict';\nvar getBuiltIn = require('../internals/get-built-in');\nvar isCallable = require('../internals/is-callable');\nvar isPrototypeOf = require('../internals/object-is-prototype-of');\nvar USE_SYMBOL_AS_UID = require('../internals/use-symbol-as-uid');\n\nvar $Object = Object;\n\nmodule.exports = USE_SYMBOL_AS_UID ? function (it) {\n  return typeof it == 'symbol';\n} : function (it) {\n  var $Symbol = getBuiltIn('Symbol');\n  return isCallable($Symbol) && isPrototypeOf($Symbol.prototype, $Object(it));\n};\n", "'use strict';\nvar $String = String;\n\nmodule.exports = function (argument) {\n  try {\n    return $String(argument);\n  } catch (error) {\n    return 'Object';\n  }\n};\n", "'use strict';\nvar isCallable = require('../internals/is-callable');\nvar tryToString = require('../internals/try-to-string');\n\nvar $TypeError = TypeError;\n\n// `Assert: IsCallable(argument) is true`\nmodule.exports = function (argument) {\n  if (isCallable(argument)) return argument;\n  throw new $TypeError(tryToString(argument) + ' is not a function');\n};\n", "'use strict';\nvar aCallable = require('../internals/a-callable');\nvar isNullOrUndefined = require('../internals/is-null-or-undefined');\n\n// `GetMethod` abstract operation\n// https://tc39.es/ecma262/#sec-getmethod\nmodule.exports = function (V, P) {\n  var func = V[P];\n  return isNullOrUndefined(func) ? undefined : aCallable(func);\n};\n", "'use strict';\nvar call = require('../internals/function-call');\nvar isCallable = require('../internals/is-callable');\nvar isObject = require('../internals/is-object');\n\nvar $TypeError = TypeError;\n\n// `OrdinaryToPrimitive` abstract operation\n// https://tc39.es/ecma262/#sec-ordinarytoprimitive\nmodule.exports = function (input, pref) {\n  var fn, val;\n  if (pref === 'string' && isCallable(fn = input.toString) && !isObject(val = call(fn, input))) return val;\n  if (isCallable(fn = input.valueOf) && !isObject(val = call(fn, input))) return val;\n  if (pref !== 'string' && isCallable(fn = input.toString) && !isObject(val = call(fn, input))) return val;\n  throw new $TypeError(\"Can't convert object to primitive value\");\n};\n", "'use strict';\nmodule.exports = false;\n", "'use strict';\nvar global = require('../internals/global');\n\n// eslint-disable-next-line es/no-object-defineproperty -- safe\nvar defineProperty = Object.defineProperty;\n\nmodule.exports = function (key, value) {\n  try {\n    defineProperty(global, key, { value: value, configurable: true, writable: true });\n  } catch (error) {\n    global[key] = value;\n  } return value;\n};\n", "'use strict';\nvar IS_PURE = require('../internals/is-pure');\nvar globalThis = require('../internals/global');\nvar defineGlobalProperty = require('../internals/define-global-property');\n\nvar SHARED = '__core-js_shared__';\nvar store = module.exports = globalThis[SHARED] || defineGlobalProperty(SHARED, {});\n\n(store.versions || (store.versions = [])).push({\n  version: '3.36.0',\n  mode: IS_PURE ? 'pure' : 'global',\n  copyright: '© 2014-2024 <PERSON> (zloirock.ru)',\n  license: 'https://github.com/zloirock/core-js/blob/v3.36.0/LICENSE',\n  source: 'https://github.com/zloirock/core-js'\n});\n", "'use strict';\nvar store = require('../internals/shared-store');\n\nmodule.exports = function (key, value) {\n  return store[key] || (store[key] = value || {});\n};\n", "'use strict';\nvar requireObjectCoercible = require('../internals/require-object-coercible');\n\nvar $Object = Object;\n\n// `ToObject` abstract operation\n// https://tc39.es/ecma262/#sec-toobject\nmodule.exports = function (argument) {\n  return $Object(requireObjectCoercible(argument));\n};\n", "'use strict';\nvar uncurryThis = require('../internals/function-uncurry-this');\nvar toObject = require('../internals/to-object');\n\nvar hasOwnProperty = uncurryThis({}.hasOwnProperty);\n\n// `HasOwnProperty` abstract operation\n// https://tc39.es/ecma262/#sec-hasownproperty\n// eslint-disable-next-line es/no-object-hasown -- safe\nmodule.exports = Object.hasOwn || function hasOwn(it, key) {\n  return hasOwnProperty(toObject(it), key);\n};\n", "'use strict';\nvar uncurryThis = require('../internals/function-uncurry-this');\n\nvar id = 0;\nvar postfix = Math.random();\nvar toString = uncurryThis(1.0.toString);\n\nmodule.exports = function (key) {\n  return 'Symbol(' + (key === undefined ? '' : key) + ')_' + toString(++id + postfix, 36);\n};\n", "'use strict';\nvar global = require('../internals/global');\nvar shared = require('../internals/shared');\nvar hasOwn = require('../internals/has-own-property');\nvar uid = require('../internals/uid');\nvar NATIVE_SYMBOL = require('../internals/symbol-constructor-detection');\nvar USE_SYMBOL_AS_UID = require('../internals/use-symbol-as-uid');\n\nvar Symbol = global.Symbol;\nvar WellKnownSymbolsStore = shared('wks');\nvar createWellKnownSymbol = USE_SYMBOL_AS_UID ? Symbol['for'] || Symbol : Symbol && Symbol.withoutSetter || uid;\n\nmodule.exports = function (name) {\n  if (!hasOwn(WellKnownSymbolsStore, name)) {\n    WellKnownSymbolsStore[name] = NATIVE_SYMBOL && hasOwn(Symbol, name)\n      ? Symbol[name]\n      : createWellKnownSymbol('Symbol.' + name);\n  } return WellKnownSymbolsStore[name];\n};\n", "'use strict';\nvar call = require('../internals/function-call');\nvar isObject = require('../internals/is-object');\nvar isSymbol = require('../internals/is-symbol');\nvar getMethod = require('../internals/get-method');\nvar ordinaryToPrimitive = require('../internals/ordinary-to-primitive');\nvar wellKnownSymbol = require('../internals/well-known-symbol');\n\nvar $TypeError = TypeError;\nvar TO_PRIMITIVE = wellKnownSymbol('toPrimitive');\n\n// `ToPrimitive` abstract operation\n// https://tc39.es/ecma262/#sec-toprimitive\nmodule.exports = function (input, pref) {\n  if (!isObject(input) || isSymbol(input)) return input;\n  var exoticToPrim = getMethod(input, TO_PRIMITIVE);\n  var result;\n  if (exoticToPrim) {\n    if (pref === undefined) pref = 'default';\n    result = call(exoticToPrim, input, pref);\n    if (!isObject(result) || isSymbol(result)) return result;\n    throw new $TypeError(\"Can't convert object to primitive value\");\n  }\n  if (pref === undefined) pref = 'number';\n  return ordinaryToPrimitive(input, pref);\n};\n", "'use strict';\nvar toPrimitive = require('../internals/to-primitive');\nvar isSymbol = require('../internals/is-symbol');\n\n// `ToPropertyKey` abstract operation\n// https://tc39.es/ecma262/#sec-topropertykey\nmodule.exports = function (argument) {\n  var key = toPrimitive(argument, 'string');\n  return isSymbol(key) ? key : key + '';\n};\n", "'use strict';\nvar DESCRIPTORS = require('../internals/descriptors');\nvar fails = require('../internals/fails');\nvar createElement = require('../internals/document-create-element');\n\n// Thanks to IE8 for its funny defineProperty\nmodule.exports = !DESCRIPTORS && !fails(function () {\n  // eslint-disable-next-line es/no-object-defineproperty -- required for testing\n  return Object.defineProperty(createElement('div'), 'a', {\n    get: function () { return 7; }\n  }).a !== 7;\n});\n", "'use strict';\nvar global = require('../internals/global');\nvar isObject = require('../internals/is-object');\n\nvar document = global.document;\n// typeof document.createElement is 'object' in old IE\nvar EXISTS = isObject(document) && isObject(document.createElement);\n\nmodule.exports = function (it) {\n  return EXISTS ? document.createElement(it) : {};\n};\n", "'use strict';\nvar DESCRIPTORS = require('../internals/descriptors');\nvar call = require('../internals/function-call');\nvar propertyIsEnumerableModule = require('../internals/object-property-is-enumerable');\nvar createPropertyDescriptor = require('../internals/create-property-descriptor');\nvar toIndexedObject = require('../internals/to-indexed-object');\nvar toPropertyKey = require('../internals/to-property-key');\nvar hasOwn = require('../internals/has-own-property');\nvar IE8_DOM_DEFINE = require('../internals/ie8-dom-define');\n\n// eslint-disable-next-line es/no-object-getownpropertydescriptor -- safe\nvar $getOwnPropertyDescriptor = Object.getOwnPropertyDescriptor;\n\n// `Object.getOwnPropertyDescriptor` method\n// https://tc39.es/ecma262/#sec-object.getownpropertydescriptor\nexports.f = DESCRIPTORS ? $getOwnPropertyDescriptor : function getOwnPropertyDescriptor(O, P) {\n  O = toIndexedObject(O);\n  P = toPropertyKey(P);\n  if (IE8_DOM_DEFINE) try {\n    return $getOwnPropertyDescriptor(O, P);\n  } catch (error) { /* empty */ }\n  if (hasOwn(O, P)) return createPropertyDescriptor(!call(propertyIsEnumerableModule.f, O, P), O[P]);\n};\n", "'use strict';\nvar $propertyIsEnumerable = {}.propertyIsEnumerable;\n// eslint-disable-next-line es/no-object-getownpropertydescriptor -- safe\nvar getOwnPropertyDescriptor = Object.getOwnPropertyDescriptor;\n\n// Nashorn ~ JDK8 bug\nvar NASHORN_BUG = getOwnPropertyDescriptor && !$propertyIsEnumerable.call({ 1: 2 }, 1);\n\n// `Object.prototype.propertyIsEnumerable` method implementation\n// https://tc39.es/ecma262/#sec-object.prototype.propertyisenumerable\nexports.f = NASHORN_BUG ? function propertyIsEnumerable(V) {\n  var descriptor = getOwnPropertyDescriptor(this, V);\n  return !!descriptor && descriptor.enumerable;\n} : $propertyIsEnumerable;\n", "'use strict';\nvar isObject = require('../internals/is-object');\n\nvar $String = String;\nvar $TypeError = TypeError;\n\n// `Assert: Type(argument) is Object`\nmodule.exports = function (argument) {\n  if (isObject(argument)) return argument;\n  throw new $TypeError($String(argument) + ' is not an object');\n};\n", "'use strict';\nvar DESCRIPTORS = require('../internals/descriptors');\nvar IE8_DOM_DEFINE = require('../internals/ie8-dom-define');\nvar V8_PROTOTYPE_DEFINE_BUG = require('../internals/v8-prototype-define-bug');\nvar anObject = require('../internals/an-object');\nvar toPropertyKey = require('../internals/to-property-key');\n\nvar $TypeError = TypeError;\n// eslint-disable-next-line es/no-object-defineproperty -- safe\nvar $defineProperty = Object.defineProperty;\n// eslint-disable-next-line es/no-object-getownpropertydescriptor -- safe\nvar $getOwnPropertyDescriptor = Object.getOwnPropertyDescriptor;\nvar ENUMERABLE = 'enumerable';\nvar CONFIGURABLE = 'configurable';\nvar WRITABLE = 'writable';\n\n// `Object.defineProperty` method\n// https://tc39.es/ecma262/#sec-object.defineproperty\nexports.f = DESCRIPTORS ? V8_PROTOTYPE_DEFINE_BUG ? function defineProperty(O, P, Attributes) {\n  anObject(O);\n  P = toPropertyKey(P);\n  anObject(Attributes);\n  if (typeof O === 'function' && P === 'prototype' && 'value' in Attributes && WRITABLE in Attributes && !Attributes[WRITABLE]) {\n    var current = $getOwnPropertyDescriptor(O, P);\n    if (current && current[WRITABLE]) {\n      O[P] = Attributes.value;\n      Attributes = {\n        configurable: CONFIGURABLE in Attributes ? Attributes[CONFIGURABLE] : current[CONFIGURABLE],\n        enumerable: ENUMERABLE in Attributes ? Attributes[ENUMERABLE] : current[ENUMERABLE],\n        writable: false\n      };\n    }\n  } return $defineProperty(O, P, Attributes);\n} : $defineProperty : function defineProperty(O, P, Attributes) {\n  anObject(O);\n  P = toPropertyKey(P);\n  anObject(Attributes);\n  if (IE8_DOM_DEFINE) try {\n    return $defineProperty(O, P, Attributes);\n  } catch (error) { /* empty */ }\n  if ('get' in Attributes || 'set' in Attributes) throw new $TypeError('Accessors not supported');\n  if ('value' in Attributes) O[P] = Attributes.value;\n  return O;\n};\n", "'use strict';\nvar DESCRIPTORS = require('../internals/descriptors');\nvar fails = require('../internals/fails');\n\n// V8 ~ Chrome 36-\n// https://bugs.chromium.org/p/v8/issues/detail?id=3334\nmodule.exports = DESCRIPTORS && fails(function () {\n  // eslint-disable-next-line es/no-object-defineproperty -- required for testing\n  return Object.defineProperty(function () { /* empty */ }, 'prototype', {\n    value: 42,\n    writable: false\n  }).prototype !== 42;\n});\n", "'use strict';\nvar DESCRIPTORS = require('../internals/descriptors');\nvar definePropertyModule = require('../internals/object-define-property');\nvar createPropertyDescriptor = require('../internals/create-property-descriptor');\n\nmodule.exports = DESCRIPTORS ? function (object, key, value) {\n  return definePropertyModule.f(object, key, createPropertyDescriptor(1, value));\n} : function (object, key, value) {\n  object[key] = value;\n  return object;\n};\n", "'use strict';\nvar uncurryThis = require('../internals/function-uncurry-this');\nvar isCallable = require('../internals/is-callable');\nvar store = require('../internals/shared-store');\n\nvar functionToString = uncurryThis(Function.toString);\n\n// this helper broken in `core-js@3.4.1-3.4.4`, so we can't use `shared` helper\nif (!isCallable(store.inspectSource)) {\n  store.inspectSource = function (it) {\n    return functionToString(it);\n  };\n}\n\nmodule.exports = store.inspectSource;\n", "'use strict';\nvar shared = require('../internals/shared');\nvar uid = require('../internals/uid');\n\nvar keys = shared('keys');\n\nmodule.exports = function (key) {\n  return keys[key] || (keys[key] = uid(key));\n};\n", "'use strict';\nmodule.exports = {};\n", "'use strict';\nvar NATIVE_WEAK_MAP = require('../internals/weak-map-basic-detection');\nvar global = require('../internals/global');\nvar isObject = require('../internals/is-object');\nvar createNonEnumerableProperty = require('../internals/create-non-enumerable-property');\nvar hasOwn = require('../internals/has-own-property');\nvar shared = require('../internals/shared-store');\nvar sharedKey = require('../internals/shared-key');\nvar hiddenKeys = require('../internals/hidden-keys');\n\nvar OBJECT_ALREADY_INITIALIZED = 'Object already initialized';\nvar TypeError = global.TypeError;\nvar WeakMap = global.WeakMap;\nvar set, get, has;\n\nvar enforce = function (it) {\n  return has(it) ? get(it) : set(it, {});\n};\n\nvar getterFor = function (TYPE) {\n  return function (it) {\n    var state;\n    if (!isObject(it) || (state = get(it)).type !== TYPE) {\n      throw new TypeError('Incompatible receiver, ' + TYPE + ' required');\n    } return state;\n  };\n};\n\nif (NATIVE_WEAK_MAP || shared.state) {\n  var store = shared.state || (shared.state = new WeakMap());\n  /* eslint-disable no-self-assign -- prototype methods protection */\n  store.get = store.get;\n  store.has = store.has;\n  store.set = store.set;\n  /* eslint-enable no-self-assign -- prototype methods protection */\n  set = function (it, metadata) {\n    if (store.has(it)) throw new TypeError(OBJECT_ALREADY_INITIALIZED);\n    metadata.facade = it;\n    store.set(it, metadata);\n    return metadata;\n  };\n  get = function (it) {\n    return store.get(it) || {};\n  };\n  has = function (it) {\n    return store.has(it);\n  };\n} else {\n  var STATE = sharedKey('state');\n  hiddenKeys[STATE] = true;\n  set = function (it, metadata) {\n    if (hasOwn(it, STATE)) throw new TypeError(OBJECT_ALREADY_INITIALIZED);\n    metadata.facade = it;\n    createNonEnumerableProperty(it, STATE, metadata);\n    return metadata;\n  };\n  get = function (it) {\n    return hasOwn(it, STATE) ? it[STATE] : {};\n  };\n  has = function (it) {\n    return hasOwn(it, STATE);\n  };\n}\n\nmodule.exports = {\n  set: set,\n  get: get,\n  has: has,\n  enforce: enforce,\n  getterFor: getterFor\n};\n", "'use strict';\nvar global = require('../internals/global');\nvar isCallable = require('../internals/is-callable');\n\nvar WeakMap = global.WeakMap;\n\nmodule.exports = isCallable(WeakMap) && /native code/.test(String(WeakMap));\n", "'use strict';\nvar uncurryThis = require('../internals/function-uncurry-this');\nvar fails = require('../internals/fails');\nvar isCallable = require('../internals/is-callable');\nvar hasOwn = require('../internals/has-own-property');\nvar DESCRIPTORS = require('../internals/descriptors');\nvar CONFIGURABLE_FUNCTION_NAME = require('../internals/function-name').CONFIGURABLE;\nvar inspectSource = require('../internals/inspect-source');\nvar InternalStateModule = require('../internals/internal-state');\n\nvar enforceInternalState = InternalStateModule.enforce;\nvar getInternalState = InternalStateModule.get;\nvar $String = String;\n// eslint-disable-next-line es/no-object-defineproperty -- safe\nvar defineProperty = Object.defineProperty;\nvar stringSlice = uncurryThis(''.slice);\nvar replace = uncurryThis(''.replace);\nvar join = uncurryThis([].join);\n\nvar CONFIGURABLE_LENGTH = DESCRIPTORS && !fails(function () {\n  return defineProperty(function () { /* empty */ }, 'length', { value: 8 }).length !== 8;\n});\n\nvar TEMPLATE = String(String).split('String');\n\nvar makeBuiltIn = module.exports = function (value, name, options) {\n  if (stringSlice($String(name), 0, 7) === 'Symbol(') {\n    name = '[' + replace($String(name), /^Symbol\\(([^)]*)\\).*$/, '$1') + ']';\n  }\n  if (options && options.getter) name = 'get ' + name;\n  if (options && options.setter) name = 'set ' + name;\n  if (!hasOwn(value, 'name') || (CONFIGURABLE_FUNCTION_NAME && value.name !== name)) {\n    if (DESCRIPTORS) defineProperty(value, 'name', { value: name, configurable: true });\n    else value.name = name;\n  }\n  if (CONFIGURABLE_LENGTH && options && hasOwn(options, 'arity') && value.length !== options.arity) {\n    defineProperty(value, 'length', { value: options.arity });\n  }\n  try {\n    if (options && hasOwn(options, 'constructor') && options.constructor) {\n      if (DESCRIPTORS) defineProperty(value, 'prototype', { writable: false });\n    // in V8 ~ Chrome 53, prototypes of some methods, like `Array.prototype.values`, are non-writable\n    } else if (value.prototype) value.prototype = undefined;\n  } catch (error) { /* empty */ }\n  var state = enforceInternalState(value);\n  if (!hasOwn(state, 'source')) {\n    state.source = join(TEMPLATE, typeof name == 'string' ? name : '');\n  } return value;\n};\n\n// add fake Function#toString for correct work wrapped methods / constructors with methods like LoDash isNative\n// eslint-disable-next-line no-extend-native -- required\nFunction.prototype.toString = makeBuiltIn(function toString() {\n  return isCallable(this) && getInternalState(this).source || inspectSource(this);\n}, 'toString');\n", "'use strict';\nvar DESCRIPTORS = require('../internals/descriptors');\nvar hasOwn = require('../internals/has-own-property');\n\nvar FunctionPrototype = Function.prototype;\n// eslint-disable-next-line es/no-object-getownpropertydescriptor -- safe\nvar getDescriptor = DESCRIPTORS && Object.getOwnPropertyDescriptor;\n\nvar EXISTS = hasOwn(FunctionPrototype, 'name');\n// additional protection from minified / mangled / dropped function names\nvar PROPER = EXISTS && (function something() { /* empty */ }).name === 'something';\nvar CONFIGURABLE = EXISTS && (!DESCRIPTORS || (DESCRIPTORS && getDescriptor(FunctionPrototype, 'name').configurable));\n\nmodule.exports = {\n  EXISTS: EXISTS,\n  PROPER: PROPER,\n  CONFIGURABLE: CONFIGURABLE\n};\n", "'use strict';\nvar isCallable = require('../internals/is-callable');\nvar definePropertyModule = require('../internals/object-define-property');\nvar makeBuiltIn = require('../internals/make-built-in');\nvar defineGlobalProperty = require('../internals/define-global-property');\n\nmodule.exports = function (O, key, value, options) {\n  if (!options) options = {};\n  var simple = options.enumerable;\n  var name = options.name !== undefined ? options.name : key;\n  if (isCallable(value)) makeBuiltIn(value, name, options);\n  if (options.global) {\n    if (simple) O[key] = value;\n    else defineGlobalProperty(key, value);\n  } else {\n    try {\n      if (!options.unsafe) delete O[key];\n      else if (O[key]) simple = true;\n    } catch (error) { /* empty */ }\n    if (simple) O[key] = value;\n    else definePropertyModule.f(O, key, {\n      value: value,\n      enumerable: false,\n      configurable: !options.nonConfigurable,\n      writable: !options.nonWritable\n    });\n  } return O;\n};\n", "'use strict';\nvar trunc = require('../internals/math-trunc');\n\n// `ToIntegerOrInfinity` abstract operation\n// https://tc39.es/ecma262/#sec-tointegerorinfinity\nmodule.exports = function (argument) {\n  var number = +argument;\n  // eslint-disable-next-line no-self-compare -- NaN check\n  return number !== number || number === 0 ? 0 : trunc(number);\n};\n", "'use strict';\nvar ceil = Math.ceil;\nvar floor = Math.floor;\n\n// `Math.trunc` method\n// https://tc39.es/ecma262/#sec-math.trunc\n// eslint-disable-next-line es/no-math-trunc -- safe\nmodule.exports = Math.trunc || function trunc(x) {\n  var n = +x;\n  return (n > 0 ? floor : ceil)(n);\n};\n", "'use strict';\nvar toIntegerOrInfinity = require('../internals/to-integer-or-infinity');\n\nvar max = Math.max;\nvar min = Math.min;\n\n// Helper for a popular repeating case of the spec:\n// Let integer be ? ToInteger(index).\n// If integer < 0, let result be max((length + integer), 0); else let result be min(integer, length).\nmodule.exports = function (index, length) {\n  var integer = toIntegerOrInfinity(index);\n  return integer < 0 ? max(integer + length, 0) : min(integer, length);\n};\n", "'use strict';\nvar toIntegerOrInfinity = require('../internals/to-integer-or-infinity');\n\nvar min = Math.min;\n\n// `ToLength` abstract operation\n// https://tc39.es/ecma262/#sec-tolength\nmodule.exports = function (argument) {\n  var len = toIntegerOrInfinity(argument);\n  return len > 0 ? min(len, 0x1FFFFFFFFFFFFF) : 0; // 2 ** 53 - 1 == 9007199254740991\n};\n", "'use strict';\nvar toLength = require('../internals/to-length');\n\n// `LengthOfArrayLike` abstract operation\n// https://tc39.es/ecma262/#sec-lengthofarraylike\nmodule.exports = function (obj) {\n  return toLength(obj.length);\n};\n", "'use strict';\nvar uncurryThis = require('../internals/function-uncurry-this');\nvar hasOwn = require('../internals/has-own-property');\nvar toIndexedObject = require('../internals/to-indexed-object');\nvar indexOf = require('../internals/array-includes').indexOf;\nvar hiddenKeys = require('../internals/hidden-keys');\n\nvar push = uncurryThis([].push);\n\nmodule.exports = function (object, names) {\n  var O = toIndexedObject(object);\n  var i = 0;\n  var result = [];\n  var key;\n  for (key in O) !hasOwn(hiddenKeys, key) && hasOwn(O, key) && push(result, key);\n  // Don't enum bug & hidden keys\n  while (names.length > i) if (hasOwn(O, key = names[i++])) {\n    ~indexOf(result, key) || push(result, key);\n  }\n  return result;\n};\n", "'use strict';\nvar toIndexedObject = require('../internals/to-indexed-object');\nvar toAbsoluteIndex = require('../internals/to-absolute-index');\nvar lengthOfArrayLike = require('../internals/length-of-array-like');\n\n// `Array.prototype.{ indexOf, includes }` methods implementation\nvar createMethod = function (IS_INCLUDES) {\n  return function ($this, el, fromIndex) {\n    var O = toIndexedObject($this);\n    var length = lengthOfArrayLike(O);\n    if (length === 0) return !IS_INCLUDES && -1;\n    var index = toAbsoluteIndex(fromIndex, length);\n    var value;\n    // Array#includes uses SameValueZero equality algorithm\n    // eslint-disable-next-line no-self-compare -- NaN check\n    if (IS_INCLUDES && el !== el) while (length > index) {\n      value = O[index++];\n      // eslint-disable-next-line no-self-compare -- NaN check\n      if (value !== value) return true;\n    // Array#indexOf ignores holes, Array#includes - not\n    } else for (;length > index; index++) {\n      if ((IS_INCLUDES || index in O) && O[index] === el) return IS_INCLUDES || index || 0;\n    } return !IS_INCLUDES && -1;\n  };\n};\n\nmodule.exports = {\n  // `Array.prototype.includes` method\n  // https://tc39.es/ecma262/#sec-array.prototype.includes\n  includes: createMethod(true),\n  // `Array.prototype.indexOf` method\n  // https://tc39.es/ecma262/#sec-array.prototype.indexof\n  indexOf: createMethod(false)\n};\n", "'use strict';\nvar getBuiltIn = require('../internals/get-built-in');\nvar uncurryThis = require('../internals/function-uncurry-this');\nvar getOwnPropertyNamesModule = require('../internals/object-get-own-property-names');\nvar getOwnPropertySymbolsModule = require('../internals/object-get-own-property-symbols');\nvar anObject = require('../internals/an-object');\n\nvar concat = uncurryThis([].concat);\n\n// all object keys, includes non-enumerable and symbols\nmodule.exports = getBuiltIn('Reflect', 'ownKeys') || function ownKeys(it) {\n  var keys = getOwnPropertyNamesModule.f(anObject(it));\n  var getOwnPropertySymbols = getOwnPropertySymbolsModule.f;\n  return getOwnPropertySymbols ? concat(keys, getOwnPropertySymbols(it)) : keys;\n};\n", "'use strict';\nvar internalObjectKeys = require('../internals/object-keys-internal');\nvar enumBugKeys = require('../internals/enum-bug-keys');\n\nvar hiddenKeys = enumBugKeys.concat('length', 'prototype');\n\n// `Object.getOwnPropertyNames` method\n// https://tc39.es/ecma262/#sec-object.getownpropertynames\n// eslint-disable-next-line es/no-object-getownpropertynames -- safe\nexports.f = Object.getOwnPropertyNames || function getOwnPropertyNames(O) {\n  return internalObjectKeys(O, hiddenKeys);\n};\n", "'use strict';\n// IE8- don't enum bug keys\nmodule.exports = [\n  'constructor',\n  'hasOwnProperty',\n  'isPrototypeOf',\n  'propertyIsEnumerable',\n  'toLocaleString',\n  'toString',\n  'valueOf'\n];\n", "'use strict';\n// eslint-disable-next-line es/no-object-getownpropertysymbols -- safe\nexports.f = Object.getOwnPropertySymbols;\n", "'use strict';\nvar hasOwn = require('../internals/has-own-property');\nvar ownKeys = require('../internals/own-keys');\nvar getOwnPropertyDescriptorModule = require('../internals/object-get-own-property-descriptor');\nvar definePropertyModule = require('../internals/object-define-property');\n\nmodule.exports = function (target, source, exceptions) {\n  var keys = ownKeys(source);\n  var defineProperty = definePropertyModule.f;\n  var getOwnPropertyDescriptor = getOwnPropertyDescriptorModule.f;\n  for (var i = 0; i < keys.length; i++) {\n    var key = keys[i];\n    if (!hasOwn(target, key) && !(exceptions && hasOwn(exceptions, key))) {\n      defineProperty(target, key, getOwnPropertyDescriptor(source, key));\n    }\n  }\n};\n", "'use strict';\nvar global = require('../internals/global');\nvar getOwnPropertyDescriptor = require('../internals/object-get-own-property-descriptor').f;\nvar createNonEnumerableProperty = require('../internals/create-non-enumerable-property');\nvar defineBuiltIn = require('../internals/define-built-in');\nvar defineGlobalProperty = require('../internals/define-global-property');\nvar copyConstructorProperties = require('../internals/copy-constructor-properties');\nvar isForced = require('../internals/is-forced');\n\n/*\n  options.target         - name of the target object\n  options.global         - target is the global object\n  options.stat           - export as static methods of target\n  options.proto          - export as prototype methods of target\n  options.real           - real prototype method for the `pure` version\n  options.forced         - export even if the native feature is available\n  options.bind           - bind methods to the target, required for the `pure` version\n  options.wrap           - wrap constructors to preventing global pollution, required for the `pure` version\n  options.unsafe         - use the simple assignment of property instead of delete + defineProperty\n  options.sham           - add a flag to not completely full polyfills\n  options.enumerable     - export as enumerable property\n  options.dontCallGetSet - prevent calling a getter on target\n  options.name           - the .name of the function if it does not match the key\n*/\nmodule.exports = function (options, source) {\n  var TARGET = options.target;\n  var GLOBAL = options.global;\n  var STATIC = options.stat;\n  var FORCED, target, key, targetProperty, sourceProperty, descriptor;\n  if (GLOBAL) {\n    target = global;\n  } else if (STATIC) {\n    target = global[TARGET] || defineGlobalProperty(TARGET, {});\n  } else {\n    target = global[TARGET] && global[TARGET].prototype;\n  }\n  if (target) for (key in source) {\n    sourceProperty = source[key];\n    if (options.dontCallGetSet) {\n      descriptor = getOwnPropertyDescriptor(target, key);\n      targetProperty = descriptor && descriptor.value;\n    } else targetProperty = target[key];\n    FORCED = isForced(GLOBAL ? key : TARGET + (STATIC ? '.' : '#') + key, options.forced);\n    // contained in target\n    if (!FORCED && targetProperty !== undefined) {\n      if (typeof sourceProperty == typeof targetProperty) continue;\n      copyConstructorProperties(sourceProperty, targetProperty);\n    }\n    // add a flag to not completely full polyfills\n    if (options.sham || (targetProperty && targetProperty.sham)) {\n      createNonEnumerableProperty(sourceProperty, 'sham', true);\n    }\n    defineBuiltIn(target, key, sourceProperty, options);\n  }\n};\n", "'use strict';\nvar fails = require('../internals/fails');\nvar isCallable = require('../internals/is-callable');\n\nvar replacement = /#|\\.prototype\\./;\n\nvar isForced = function (feature, detection) {\n  var value = data[normalize(feature)];\n  return value === POLYFILL ? true\n    : value === NATIVE ? false\n    : isCallable(detection) ? fails(detection)\n    : !!detection;\n};\n\nvar normalize = isForced.normalize = function (string) {\n  return String(string).replace(replacement, '.').toLowerCase();\n};\n\nvar data = isForced.data = {};\nvar NATIVE = isForced.NATIVE = 'N';\nvar POLYFILL = isForced.POLYFILL = 'P';\n\nmodule.exports = isForced;\n", "'use strict';\nvar classof = require('../internals/classof-raw');\n\n// `IsArray` abstract operation\n// https://tc39.es/ecma262/#sec-isarray\n// eslint-disable-next-line es/no-array-isarray -- safe\nmodule.exports = Array.isArray || function isArray(argument) {\n  return classof(argument) === 'Array';\n};\n", "'use strict';\nvar DESCRIPTORS = require('../internals/descriptors');\nvar isArray = require('../internals/is-array');\n\nvar $TypeError = TypeError;\n// eslint-disable-next-line es/no-object-getownpropertydescriptor -- safe\nvar getOwnPropertyDescriptor = Object.getOwnPropertyDescriptor;\n\n// Safari < 13 does not throw an error in this case\nvar SILENT_ON_NON_WRITABLE_LENGTH_SET = DESCRIPTORS && !function () {\n  // makes no sense without proper strict mode support\n  if (this !== undefined) return true;\n  try {\n    // eslint-disable-next-line es/no-object-defineproperty -- safe\n    Object.defineProperty([], 'length', { writable: false }).length = 1;\n  } catch (error) {\n    return error instanceof TypeError;\n  }\n}();\n\nmodule.exports = SILENT_ON_NON_WRITABLE_LENGTH_SET ? function (O, length) {\n  if (isArray(O) && !getOwnPropertyDescriptor(O, 'length').writable) {\n    throw new $TypeError('Cannot set read only .length');\n  } return O.length = length;\n} : function (O, length) {\n  return O.length = length;\n};\n", "'use strict';\nvar $TypeError = TypeError;\nvar MAX_SAFE_INTEGER = 0x1FFFFFFFFFFFFF; // 2 ** 53 - 1 == 9007199254740991\n\nmodule.exports = function (it) {\n  if (it > MAX_SAFE_INTEGER) throw $TypeError('Maximum allowed index exceeded');\n  return it;\n};\n", "// A is m x n. B is n x p. product is m x p.\nexport default function multiplyMatrices (A, B) {\n\tlet m = A.length;\n\n\tif (!Array.isArray(A[0])) {\n\t\t// A is vector, convert to [[a, b, c, ...]]\n\t\tA = [A];\n\t}\n\n\tif (!Array.isArray(B[0])) {\n\t\t// B is vector, convert to [[a], [b], [c], ...]]\n\t\tB = B.map(x => [x]);\n\t}\n\n\tlet p = B[0].length;\n\tlet B_cols = B[0].map((_, i) => B.map(x => x[i])); // transpose B\n\tlet product = A.map(row => B_cols.map(col => {\n\t\tlet ret = 0;\n\n\t\tif (!Array.isArray(row)) {\n\t\t\tfor (let c of col) {\n\t\t\t\tret += row * c;\n\t\t\t}\n\n\t\t\treturn ret;\n\t\t}\n\n\t\tfor (let i = 0; i < row.length; i++) {\n\t\t\tret += row[i] * (col[i] || 0);\n\t\t}\n\n\t\treturn ret;\n\t}));\n\n\tif (m === 1) {\n\t\tproduct = product[0]; // Avoid [[a, b, c, ...]]\n\t}\n\n\tif (p === 1) {\n\t\treturn product.map(x => x[0]); // Avoid [[a], [b], [c], ...]]\n\t}\n\n\treturn product;\n}\n", "/**\n * Various utility functions\n */\n\nexport {default as multiplyMatrices} from \"./multiply-matrices.js\";\n\n/**\n * Check if a value is a string (including a String object)\n * @param {*} str - Value to check\n * @returns {boolean}\n */\nexport function isString (str) {\n\treturn type(str) === \"string\";\n}\n\n/**\n * Determine the internal JavaScript [[Class]] of an object.\n * @param {*} o - Value to check\n * @returns {string}\n */\nexport function type (o) {\n\tlet str = Object.prototype.toString.call(o);\n\n\treturn (str.match(/^\\[object\\s+(.*?)\\]$/)[1] || \"\").toLowerCase();\n}\n\nexport function serializeNumber (n, {precision, unit }) {\n\tif (isNone(n)) {\n\t\treturn \"none\";\n\t}\n\n\treturn toPrecision(n, precision) + (unit ?? \"\");\n}\n\n/**\n * Check if a value corresponds to a none argument\n * @param {*} n - Value to check\n * @returns {boolean}\n */\nexport function isNone (n) {\n\treturn Number.isNaN(n) || (n instanceof Number && n?.none);\n}\n\n/**\n * Replace none values with 0\n */\nexport function skipNone (n) {\n\treturn isNone(n) ? 0 : n;\n}\n\n/**\n * Round a number to a certain number of significant digits\n * @param {number} n - The number to round\n * @param {number} precision - Number of significant digits\n */\nexport function toPrecision (n, precision) {\n\tif (n === 0) {\n\t\treturn 0;\n\t}\n\tlet integer = ~~n;\n\tlet digits = 0;\n\tif (integer && precision) {\n\t\tdigits = ~~Math.log10(Math.abs(integer)) + 1;\n\t}\n\tconst multiplier = 10.0 ** (precision - digits);\n\treturn Math.floor(n * multiplier + 0.5) / multiplier;\n}\n\nconst angleFactor = {\n\tdeg: 1,\n\tgrad: 0.9,\n\trad: 180 / Math.PI,\n\tturn: 360,\n};\n\n/**\n* Parse a CSS function, regardless of its name and arguments\n* @param String str String to parse\n* @return {{name, args, rawArgs}}\n*/\nexport function parseFunction (str) {\n\tif (!str) {\n\t\treturn;\n\t}\n\n\tstr = str.trim();\n\n\tconst isFunctionRegex = /^([a-z]+)\\((.+?)\\)$/i;\n\tconst isNumberRegex = /^-?[\\d.]+$/;\n\tconst unitValueRegex = /%|deg|g?rad|turn$/;\n\tconst singleArgument = /\\/?\\s*(none|[-\\w.]+(?:%|deg|g?rad|turn)?)/g;\n\tlet parts = str.match(isFunctionRegex);\n\n\tif (parts) {\n\t\t// It is a function, parse args\n\t\tlet args = [];\n\t\tparts[2].replace(singleArgument, ($0, rawArg) => {\n\t\t\tlet match = rawArg.match(unitValueRegex);\n\t\t\tlet arg = rawArg;\n\n\t\t\tif (match) {\n\t\t\t\tlet unit = match[0];\n\t\t\t\t// Drop unit from value\n\t\t\t\tlet unitlessArg = arg.slice(0, -unit.length);\n\n\t\t\t\tif (unit === \"%\") {\n\t\t\t\t\t// Convert percentages to 0-1 numbers\n\t\t\t\t\targ = new Number(unitlessArg / 100);\n\t\t\t\t\targ.type = \"<percentage>\";\n\t\t\t\t}\n\t\t\t\telse {\n\t\t\t\t\t// Multiply angle by appropriate factor for its unit\n\t\t\t\t\targ = new Number(unitlessArg * angleFactor[unit]);\n\t\t\t\t\targ.type = \"<angle>\";\n\t\t\t\t\targ.unit = unit;\n\t\t\t\t}\n\t\t\t}\n\t\t\telse if (isNumberRegex.test(arg)) {\n\t\t\t\t// Convert numerical args to numbers\n\t\t\t\targ = new Number(arg);\n\t\t\t\targ.type = \"<number>\";\n\t\t\t}\n\t\t\telse if (arg === \"none\") {\n\t\t\t\targ = new Number(NaN);\n\t\t\t\targ.none = true;\n\t\t\t}\n\n\t\t\tif ($0.startsWith(\"/\")) {\n\t\t\t\t// It's alpha\n\t\t\t\targ = arg instanceof Number ? arg : new Number(arg);\n\t\t\t\targ.alpha = true;\n\t\t\t}\n\n\t\t\tif (typeof arg === \"object\" && arg instanceof Number) {\n\t\t\t\targ.raw = rawArg;\n\t\t\t}\n\n\t\t\targs.push(arg);\n\t\t});\n\n\t\treturn {\n\t\t\tname: parts[1].toLowerCase(),\n\t\t\trawName: parts[1],\n\t\t\trawArgs: parts[2],\n\t\t\t// An argument could be (as of css-color-4):\n\t\t\t// a number, percentage, degrees (hue), ident (in color())\n\t\t\targs,\n\t\t};\n\t}\n}\n\nexport function last (arr) {\n\treturn arr[arr.length - 1];\n}\n\nexport function interpolate (start, end, p) {\n\tif (isNaN(start)) {\n\t\treturn end;\n\t}\n\n\tif (isNaN(end)) {\n\t\treturn start;\n\t}\n\n\treturn start + (end - start) * p;\n}\n\nexport function interpolateInv (start, end, value) {\n\treturn (value - start) / (end - start);\n}\n\nexport function mapRange (from, to, value) {\n\treturn interpolate(to[0], to[1], interpolateInv(from[0], from[1], value));\n}\n\nexport function parseCoordGrammar (coordGrammars) {\n\treturn coordGrammars.map(coordGrammar => {\n\t\treturn coordGrammar.split(\"|\").map(type => {\n\t\t\ttype = type.trim();\n\t\t\tlet range = type.match(/^(<[a-z]+>)\\[(-?[.\\d]+),\\s*(-?[.\\d]+)\\]?$/);\n\n\t\t\tif (range) {\n\t\t\t\tlet ret = new String(range[1]);\n\t\t\t\tret.range = [+range[2], +range[3]];\n\t\t\t\treturn ret;\n\t\t\t}\n\n\t\t\treturn type;\n\t\t});\n\t});\n}\n\n/**\n * Clamp value between the minimum and maximum\n * @param {number} min minimum value to return\n * @param {number} val the value to return if it is between min and max\n * @param {number} max maximum value to return\n * @returns number\n */\nexport function clamp (min, val, max) {\n\treturn Math.max(Math.min(max, val), min);\n}\n\n/**\n * Copy sign of one value to another.\n * @param {number} - to number to copy sign to\n * @param {number} - from number to copy sign from\n * @returns number\n */\nexport function copySign (to, from) {\n\treturn Math.sign(to) === Math.sign(from) ? to : -to;\n}\n\n/**\n * Perform pow on a signed number and copy sign to result\n * @param {number} - base the base number\n * @param {number} - exp the exponent\n * @returns number\n */\nexport function spow (base, exp) {\n\treturn copySign(Math.abs(base) ** exp, base);\n}\n\n/**\n * Perform a divide, but return zero if the numerator is zero\n * @param {number} n - the numerator\n * @param {number} d - the denominator\n * @returns number\n */\nexport function zdiv (n, d) {\n\treturn (d === 0) ? 0 : n / d;\n}\n\n/**\n * Perform a bisect on a sorted list and locate the insertion point for\n * a value in arr to maintain sorted order.\n * @param {number[]} arr - array of sorted numbers\n * @param {number} value - value to find insertion point for\n * @param {number} lo - used to specify a the low end of a subset of the list\n * @param {number} hi - used to specify a the high end of a subset of the list\n * @returns number\n */\nexport function bisectLeft (arr, value, lo = 0, hi = arr.length) {\n\twhile (lo < hi) {\n\t\tconst mid = (lo + hi) >> 1;\n\t\tif (arr[mid] < value) {\n\t\t\tlo = mid + 1;\n\t\t}\n\t\telse {\n\t\t\thi = mid;\n\t\t}\n\t}\n\treturn lo;\n}\n", "'use strict';\nvar $ = require('../internals/export');\nvar toObject = require('../internals/to-object');\nvar lengthOfArrayLike = require('../internals/length-of-array-like');\nvar setArrayLength = require('../internals/array-set-length');\nvar doesNotExceedSafeInteger = require('../internals/does-not-exceed-safe-integer');\nvar fails = require('../internals/fails');\n\nvar INCORRECT_TO_LENGTH = fails(function () {\n  return [].push.call({ length: 0x100000000 }, 1) !== 4294967297;\n});\n\n// V8 <= 121 and Safari <= 15.4; FF < 23 throws InternalError\n// https://bugs.chromium.org/p/v8/issues/detail?id=12681\nvar properErrorOnNonWritableLength = function () {\n  try {\n    // eslint-disable-next-line es/no-object-defineproperty -- safe\n    Object.defineProperty([], 'length', { writable: false }).push();\n  } catch (error) {\n    return error instanceof TypeError;\n  }\n};\n\nvar FORCED = INCORRECT_TO_LENGTH || !properErrorOnNonWritableLength();\n\n// `Array.prototype.push` method\n// https://tc39.es/ecma262/#sec-array.prototype.push\n$({ target: 'Array', proto: true, arity: 1, forced: FORCED }, {\n  // eslint-disable-next-line no-unused-vars -- required for `.length`\n  push: function push(item) {\n    var O = toObject(this);\n    var len = lengthOfArrayLike(O);\n    var argCount = arguments.length;\n    doesNotExceedSafeInteger(len + argCount);\n    for (var i = 0; i < argCount; i++) {\n      O[len] = arguments[i];\n      len++;\n    }\n    setArrayLength(O, len);\n    return len;\n  }\n});\n", "/**\n * A class for adding deep extensibility to any piece of JS code\n */\nexport class Hooks {\n\tadd (name, callback, first) {\n\t\tif (typeof arguments[0] != \"string\") {\n\t\t\t// Multiple hooks\n\t\t\tfor (var name in arguments[0]) {\n\t\t\t\tthis.add(name, arguments[0][name], arguments[1]);\n\t\t\t}\n\n\t\t\treturn;\n\t\t}\n\n\t\t(Array.isArray(name) ? name : [name]).forEach(function (name) {\n\t\t\tthis[name] = this[name] || [];\n\n\t\t\tif (callback) {\n\t\t\t\tthis[name][first ? \"unshift\" : \"push\"](callback);\n\t\t\t}\n\t\t}, this);\n\t}\n\n\trun (name, env) {\n\t\tthis[name] = this[name] || [];\n\t\tthis[name].forEach(function (callback) {\n\t\t\tcallback.call(env && env.context ? env.context : env, env);\n\t\t});\n\t}\n}\n\n/**\n * The instance of {@link Hooks} used throughout Color.js\n */\nconst hooks = new Hooks();\n\nexport default hooks;\n", "// Global defaults one may want to configure\nexport default {\n\tgamut_mapping: \"css\",\n\tprecision: 5,\n\tdeltaE: \"76\", // Default deltaE method\n\tverbose: globalThis?.process?.env?.NODE_ENV?.toLowerCase() !== \"test\",\n\twarn: function warn (msg) {\n\t\tif (this.verbose) {\n\t\t\tglobalThis?.console?.warn?.(msg);\n\t\t}\n\t},\n};\n", "'use strict';\nvar isObject = require('../internals/is-object');\n\nmodule.exports = function (argument) {\n  return isObject(argument) || argument === null;\n};\n", "'use strict';\nvar isPossiblePrototype = require('../internals/is-possible-prototype');\n\nvar $String = String;\nvar $TypeError = TypeError;\n\nmodule.exports = function (argument) {\n  if (isPossiblePrototype(argument)) return argument;\n  throw new $TypeError(\"Can't set \" + $String(argument) + ' as a prototype');\n};\n", "'use strict';\n/* eslint-disable no-proto -- safe */\nvar uncurryThisAccessor = require('../internals/function-uncurry-this-accessor');\nvar anObject = require('../internals/an-object');\nvar aPossiblePrototype = require('../internals/a-possible-prototype');\n\n// `Object.setPrototypeOf` method\n// https://tc39.es/ecma262/#sec-object.setprototypeof\n// Works with __proto__ only. Old v8 can't work with null proto objects.\n// eslint-disable-next-line es/no-object-setprototypeof -- safe\nmodule.exports = Object.setPrototypeOf || ('__proto__' in {} ? function () {\n  var CORRECT_SETTER = false;\n  var test = {};\n  var setter;\n  try {\n    setter = uncurryThisAccessor(Object.prototype, '__proto__', 'set');\n    setter(test, []);\n    CORRECT_SETTER = test instanceof Array;\n  } catch (error) { /* empty */ }\n  return function setPrototypeOf(O, proto) {\n    anObject(O);\n    aPossiblePrototype(proto);\n    if (CORRECT_SETTER) setter(O, proto);\n    else O.__proto__ = proto;\n    return O;\n  };\n}() : undefined);\n", "'use strict';\nvar uncurryThis = require('../internals/function-uncurry-this');\nvar aCallable = require('../internals/a-callable');\n\nmodule.exports = function (object, key, method) {\n  try {\n    // eslint-disable-next-line es/no-object-getownpropertydescriptor -- safe\n    return uncurryThis(aCallable(Object.getOwnPropertyDescriptor(object, key)[method]));\n  } catch (error) { /* empty */ }\n};\n", "'use strict';\nvar defineProperty = require('../internals/object-define-property').f;\n\nmodule.exports = function (Target, Source, key) {\n  key in Target || defineProperty(Target, key, {\n    configurable: true,\n    get: function () { return Source[key]; },\n    set: function (it) { Source[key] = it; }\n  });\n};\n", "'use strict';\nvar isCallable = require('../internals/is-callable');\nvar isObject = require('../internals/is-object');\nvar setPrototypeOf = require('../internals/object-set-prototype-of');\n\n// makes subclassing work correct for wrapped built-ins\nmodule.exports = function ($this, dummy, Wrapper) {\n  var NewTarget, NewTargetPrototype;\n  if (\n    // it can work only with native `setPrototypeOf`\n    setPrototypeOf &&\n    // we haven't completely correct pre-ES6 way for getting `new.target`, so use this\n    isCallable(NewTarget = dummy.constructor) &&\n    NewTarget !== Wrapper &&\n    isObject(NewTargetPrototype = NewTarget.prototype) &&\n    NewTargetPrototype !== Wrapper.prototype\n  ) setPrototypeOf($this, NewTargetPrototype);\n  return $this;\n};\n", "'use strict';\nvar TO_STRING_TAG_SUPPORT = require('../internals/to-string-tag-support');\nvar isCallable = require('../internals/is-callable');\nvar classofRaw = require('../internals/classof-raw');\nvar wellKnownSymbol = require('../internals/well-known-symbol');\n\nvar TO_STRING_TAG = wellKnownSymbol('toStringTag');\nvar $Object = Object;\n\n// ES3 wrong here\nvar CORRECT_ARGUMENTS = classofRaw(function () { return arguments; }()) === 'Arguments';\n\n// fallback for IE11 Script Access Denied error\nvar tryGet = function (it, key) {\n  try {\n    return it[key];\n  } catch (error) { /* empty */ }\n};\n\n// getting tag from ES6+ `Object.prototype.toString`\nmodule.exports = TO_STRING_TAG_SUPPORT ? classofRaw : function (it) {\n  var O, tag, result;\n  return it === undefined ? 'Undefined' : it === null ? 'Null'\n    // @@toStringTag case\n    : typeof (tag = tryGet(O = $Object(it), TO_STRING_TAG)) == 'string' ? tag\n    // builtinTag case\n    : CORRECT_ARGUMENTS ? classofRaw(O)\n    // ES3 arguments fallback\n    : (result = classofRaw(O)) === 'Object' && isCallable(O.callee) ? 'Arguments' : result;\n};\n", "'use strict';\nvar wellKnownSymbol = require('../internals/well-known-symbol');\n\nvar TO_STRING_TAG = wellKnownSymbol('toStringTag');\nvar test = {};\n\ntest[TO_STRING_TAG] = 'z';\n\nmodule.exports = String(test) === '[object z]';\n", "'use strict';\nvar classof = require('../internals/classof');\n\nvar $String = String;\n\nmodule.exports = function (argument) {\n  if (classof(argument) === 'Symbol') throw new TypeError('Cannot convert a Symbol value to a string');\n  return $String(argument);\n};\n", "'use strict';\nvar toString = require('../internals/to-string');\n\nmodule.exports = function (argument, $default) {\n  return argument === undefined ? arguments.length < 2 ? '' : $default : toString(argument);\n};\n", "'use strict';\nvar isObject = require('../internals/is-object');\nvar createNonEnumerableProperty = require('../internals/create-non-enumerable-property');\n\n// `InstallErrorCause` abstract operation\n// https://tc39.es/proposal-error-cause/#sec-errorobjects-install-error-cause\nmodule.exports = function (O, options) {\n  if (isObject(options) && 'cause' in options) {\n    createNonEnumerableProperty(O, 'cause', options.cause);\n  }\n};\n", "'use strict';\nvar createNonEnumerableProperty = require('../internals/create-non-enumerable-property');\nvar clearErrorStack = require('../internals/error-stack-clear');\nvar ERROR_STACK_INSTALLABLE = require('../internals/error-stack-installable');\n\n// non-standard V8\nvar captureStackTrace = Error.captureStackTrace;\n\nmodule.exports = function (error, C, stack, dropEntries) {\n  if (ERROR_STACK_INSTALLABLE) {\n    if (captureStackTrace) captureStackTrace(error, C);\n    else createNonEnumerableProperty(error, 'stack', clearErrorStack(stack, dropEntries));\n  }\n};\n", "'use strict';\nvar uncurryThis = require('../internals/function-uncurry-this');\n\nvar $Error = Error;\nvar replace = uncurryThis(''.replace);\n\nvar TEST = (function (arg) { return String(new $Error(arg).stack); })('zxcasd');\n// eslint-disable-next-line redos/no-vulnerable -- safe\nvar V8_OR_CHAKRA_STACK_ENTRY = /\\n\\s*at [^:]*:[^\\n]*/;\nvar IS_V8_OR_CHAKRA_STACK = V8_OR_CHAKRA_STACK_ENTRY.test(TEST);\n\nmodule.exports = function (stack, dropEntries) {\n  if (IS_V8_OR_CHAKRA_STACK && typeof stack == 'string' && !$Error.prepareStackTrace) {\n    while (dropEntries--) stack = replace(stack, V8_OR_CHAKRA_STACK_ENTRY, '');\n  } return stack;\n};\n", "'use strict';\nvar fails = require('../internals/fails');\nvar createPropertyDescriptor = require('../internals/create-property-descriptor');\n\nmodule.exports = !fails(function () {\n  var error = new Error('a');\n  if (!('stack' in error)) return true;\n  // eslint-disable-next-line es/no-object-defineproperty -- safe\n  Object.defineProperty(error, 'stack', createPropertyDescriptor(1, 7));\n  return error.stack !== 7;\n});\n", "'use strict';\nvar getBuiltIn = require('../internals/get-built-in');\nvar hasOwn = require('../internals/has-own-property');\nvar createNonEnumerableProperty = require('../internals/create-non-enumerable-property');\nvar isPrototypeOf = require('../internals/object-is-prototype-of');\nvar setPrototypeOf = require('../internals/object-set-prototype-of');\nvar copyConstructorProperties = require('../internals/copy-constructor-properties');\nvar proxyAccessor = require('../internals/proxy-accessor');\nvar inheritIfRequired = require('../internals/inherit-if-required');\nvar normalizeStringArgument = require('../internals/normalize-string-argument');\nvar installErrorCause = require('../internals/install-error-cause');\nvar installErrorStack = require('../internals/error-stack-install');\nvar DESCRIPTORS = require('../internals/descriptors');\nvar IS_PURE = require('../internals/is-pure');\n\nmodule.exports = function (FULL_NAME, wrapper, FORCED, IS_AGGREGATE_ERROR) {\n  var STACK_TRACE_LIMIT = 'stackTraceLimit';\n  var OPTIONS_POSITION = IS_AGGREGATE_ERROR ? 2 : 1;\n  var path = FULL_NAME.split('.');\n  var ERROR_NAME = path[path.length - 1];\n  var OriginalError = getBuiltIn.apply(null, path);\n\n  if (!OriginalError) return;\n\n  var OriginalErrorPrototype = OriginalError.prototype;\n\n  // V8 9.3- bug https://bugs.chromium.org/p/v8/issues/detail?id=12006\n  if (!IS_PURE && hasOwn(OriginalErrorPrototype, 'cause')) delete OriginalErrorPrototype.cause;\n\n  if (!FORCED) return OriginalError;\n\n  var BaseError = getBuiltIn('Error');\n\n  var WrappedError = wrapper(function (a, b) {\n    var message = normalizeStringArgument(IS_AGGREGATE_ERROR ? b : a, undefined);\n    var result = IS_AGGREGATE_ERROR ? new OriginalError(a) : new OriginalError();\n    if (message !== undefined) createNonEnumerableProperty(result, 'message', message);\n    installErrorStack(result, WrappedError, result.stack, 2);\n    if (this && isPrototypeOf(OriginalErrorPrototype, this)) inheritIfRequired(result, this, WrappedError);\n    if (arguments.length > OPTIONS_POSITION) installErrorCause(result, arguments[OPTIONS_POSITION]);\n    return result;\n  });\n\n  WrappedError.prototype = OriginalErrorPrototype;\n\n  if (ERROR_NAME !== 'Error') {\n    if (setPrototypeOf) setPrototypeOf(WrappedError, BaseError);\n    else copyConstructorProperties(WrappedError, BaseError, { name: true });\n  } else if (DESCRIPTORS && STACK_TRACE_LIMIT in OriginalError) {\n    proxyAccessor(WrappedError, OriginalError, STACK_TRACE_LIMIT);\n    proxyAccessor(WrappedError, OriginalError, 'prepareStackTrace');\n  }\n\n  copyConstructorProperties(WrappedError, OriginalError);\n\n  if (!IS_PURE) try {\n    // Safari 13- bug: WebAssembly errors does not have a proper `.name`\n    if (OriginalErrorPrototype.name !== ERROR_NAME) {\n      createNonEnumerableProperty(OriginalErrorPrototype, 'name', ERROR_NAME);\n    }\n    OriginalErrorPrototype.constructor = WrappedError;\n  } catch (error) { /* empty */ }\n\n  return WrappedError;\n};\n", "'use strict';\n/* eslint-disable no-unused-vars -- required for functions `.length` */\nvar $ = require('../internals/export');\nvar global = require('../internals/global');\nvar apply = require('../internals/function-apply');\nvar wrapErrorConstructorWithCause = require('../internals/wrap-error-constructor-with-cause');\n\nvar WEB_ASSEMBLY = 'WebAssembly';\nvar WebAssembly = global[WEB_ASSEMBLY];\n\n// eslint-disable-next-line es/no-error-cause -- feature detection\nvar FORCED = new Error('e', { cause: 7 }).cause !== 7;\n\nvar exportGlobalErrorCauseWrapper = function (ERROR_NAME, wrapper) {\n  var O = {};\n  O[ERROR_NAME] = wrapErrorConstructorWithCause(ERROR_NAME, wrapper, FORCED);\n  $({ global: true, constructor: true, arity: 1, forced: FORCED }, O);\n};\n\nvar exportWebAssemblyErrorCauseWrapper = function (ERROR_NAME, wrapper) {\n  if (WebAssembly && WebAssembly[ERROR_NAME]) {\n    var O = {};\n    O[ERROR_NAME] = wrapErrorConstructorWithCause(WEB_ASSEMBLY + '.' + ERROR_NAME, wrapper, FORCED);\n    $({ target: WEB_ASSEMBLY, stat: true, constructor: true, arity: 1, forced: FORCED }, O);\n  }\n};\n\n// https://tc39.es/ecma262/#sec-nativeerror\nexportGlobalErrorCauseWrapper('Error', function (init) {\n  return function Error(message) { return apply(init, this, arguments); };\n});\nexportGlobalErrorCauseWrapper('EvalError', function (init) {\n  return function EvalError(message) { return apply(init, this, arguments); };\n});\nexportGlobalErrorCauseWrapper('RangeError', function (init) {\n  return function RangeError(message) { return apply(init, this, arguments); };\n});\nexportGlobalErrorCauseWrapper('ReferenceError', function (init) {\n  return function ReferenceError(message) { return apply(init, this, arguments); };\n});\nexportGlobalErrorCauseWrapper('SyntaxError', function (init) {\n  return function SyntaxError(message) { return apply(init, this, arguments); };\n});\nexportGlobalErrorCauseWrapper('TypeError', function (init) {\n  return function TypeError(message) { return apply(init, this, arguments); };\n});\nexportGlobalErrorCauseWrapper('URIError', function (init) {\n  return function URIError(message) { return apply(init, this, arguments); };\n});\nexportWebAssemblyErrorCauseWrapper('CompileError', function (init) {\n  return function CompileError(message) { return apply(init, this, arguments); };\n});\nexportWebAssemblyErrorCauseWrapper('LinkError', function (init) {\n  return function LinkError(message) { return apply(init, this, arguments); };\n});\nexportWebAssemblyErrorCauseWrapper('RuntimeError', function (init) {\n  return function RuntimeError(message) { return apply(init, this, arguments); };\n});\n", "'use strict';\nvar NATIVE_BIND = require('../internals/function-bind-native');\n\nvar FunctionPrototype = Function.prototype;\nvar apply = FunctionPrototype.apply;\nvar call = FunctionPrototype.call;\n\n// eslint-disable-next-line es/no-reflect -- safe\nmodule.exports = typeof Reflect == 'object' && Reflect.apply || (NATIVE_BIND ? call.bind(apply) : function () {\n  return call.apply(apply, arguments);\n});\n", "import hooks from \"./hooks.js\";\nimport {multiplyMatrices} from \"./util.js\";\n\nexport const WHITES = {\n\t// for compatibility, the four-digit chromaticity-derived ones everyone else uses\n\tD50: [0.3457 / 0.3585, 1.00000, (1.0 - 0.3457 - 0.3585) / 0.3585],\n\tD65: [0.3127 / 0.3290, 1.00000, (1.0 - 0.3127 - 0.3290) / 0.3290],\n};\n\nexport function getWhite (name) {\n\tif (Array.isArray(name)) {\n\t\treturn name;\n\t}\n\n\treturn WHITES[name];\n}\n\n// Adapt XYZ from white point W1 to W2\nexport default function adapt (W1, W2, XYZ, options = {}) {\n\tW1 = getWhite(W1);\n\tW2 = getWhite(W2);\n\n\tif (!W1 || !W2) {\n\t\tthrow new TypeError(`Missing white point to convert ${!W1 ? \"from\" : \"\"}${!W1 && !W2 ? \"/\" : \"\"}${!W2 ? \"to\" : \"\"}`);\n\t}\n\n\tif (W1 === W2) {\n\t\t// Same whitepoints, no conversion needed\n\t\treturn XYZ;\n\t}\n\n\tlet env = {W1, W2, XYZ, options};\n\n\thooks.run(\"chromatic-adaptation-start\", env);\n\n\tif (!env.M) {\n\t\tif (env.W1 === WHITES.D65 && env.W2 === WHITES.D50) {\n\t\t\tenv.M = [\n\t\t\t\t[ 1.0479297925449969, 0.022946870601609652, -0.05019226628920524 ],\n\t\t\t\t[ 0.02962780877005599, 0.9904344267538799, -0.017073799063418826 ],\n\t\t\t\t[ -0.009243040646204504, 0.015055191490298152, 0.7518742814281371 ],\n\t\t\t];\n\t\t}\n\t\telse if (env.W1 === WHITES.D50 && env.W2 === WHITES.D65) {\n\n\t\t\tenv.M = [\n\t\t\t\t[ 0.955473421488075, -0.02309845494876471, 0.06325924320057072 ],\n\t\t\t\t[ -0.0283697093338637, 1.0099953980813041, 0.021041441191917323 ],\n\t\t\t\t[ 0.012314014864481998, -0.020507649298898964, 1.330365926242124 ],\n\t\t\t];\n\t\t}\n\t}\n\n\thooks.run(\"chromatic-adaptation-end\", env);\n\n\tif (env.M) {\n\t\treturn multiplyMatrices(env.M, env.XYZ);\n\t}\n\telse {\n\t\tthrow new TypeError(\"Only Bradford CAT with white points D50 and D65 supported for now.\");\n\t}\n}\n", "import * as util from \"./util.js\";\nimport hooks from \"./hooks.js\";\nimport ColorSpace from \"./space.js\";\nimport defaults from \"./defaults.js\";\n\nconst noneTypes = new Set([\"<number>\", \"<percentage>\", \"<angle>\"]);\n\n/**\n * Validates the coordinates of a color against a format's coord grammar and\n * maps the coordinates to the range or refRange of the coordinates.\n * @param {ColorSpace} space - Colorspace the coords are in\n * @param {object} format - the format object to validate against\n * @param {string} name - the name of the color function. e.g. \"oklab\" or \"color\"\n * @returns {object[]} - an array of type metadata for each coordinate\n */\nfunction coerceCoords (space, format, name, coords) {\n\tlet types = Object.entries(space.coords).map(([id, coordMeta], i) => {\n\t\tlet coordGrammar = format.coordGrammar[i];\n\t\tlet arg = coords[i];\n\t\tlet providedType = arg?.type;\n\n\t\t// Find grammar alternative that matches the provided type\n\t\t// Non-strict equals is intentional because we are comparing w/ string objects\n\t\tlet type;\n\t\tif (arg.none) {\n\t\t\ttype = coordGrammar.find(c => noneTypes.has(c));\n\t\t}\n\t\telse {\n\t\t\ttype = coordGrammar.find(c => c == providedType);\n\t\t}\n\n\t\t// Check that each coord conforms to its grammar\n\t\tif (!type) {\n\t\t\t// Type does not exist in the grammar, throw\n\t\t\tlet coordName = coordMeta.name || id;\n\t\t\tthrow new TypeError(`${providedType ?? arg.raw} not allowed for ${coordName} in ${name}()`);\n\t\t}\n\n\t\tlet fromRange = type.range;\n\n\t\tif (providedType === \"<percentage>\") {\n\t\t\tfromRange ||= [0, 1];\n\t\t}\n\n\t\tlet toRange = coordMeta.range || coordMeta.refRange;\n\n\t\tif (fromRange && toRange) {\n\t\t\tcoords[i] = util.mapRange(fromRange, toRange, coords[i]);\n\t\t}\n\n\t\treturn type;\n\t});\n\n\treturn types;\n}\n\n\n/**\n * Convert a CSS Color string to a color object\n * @param {string} str\n * @param {object} [options]\n * @param {object} [options.meta] - Object for additional information about the parsing\n * @returns {Color}\n */\nexport default function parse (str, {meta} = {}) {\n\tlet env = {\"str\": String(str)?.trim()};\n\thooks.run(\"parse-start\", env);\n\n\tif (env.color) {\n\t\treturn env.color;\n\t}\n\n\tenv.parsed = util.parseFunction(env.str);\n\n\tif (env.parsed) {\n\t\t// Is a functional syntax\n\t\tlet name = env.parsed.name;\n\n\t\tif (name === \"color\") {\n\t\t\t// color() function\n\t\t\tlet id = env.parsed.args.shift();\n\t\t\t// Check against both <dashed-ident> and <ident> versions\n\t\t\tlet alternateId = id.startsWith(\"--\") ? id.substring(2) : `--${id}`;\n\t\t\tlet ids = [id, alternateId];\n\t\t\tlet alpha = env.parsed.rawArgs.indexOf(\"/\") > 0 ? env.parsed.args.pop() : 1;\n\n\t\t\tfor (let space of ColorSpace.all) {\n\t\t\t\tlet colorSpec = space.getFormat(\"color\");\n\n\t\t\t\tif (colorSpec) {\n\t\t\t\t\tif (ids.includes(colorSpec.id) || colorSpec.ids?.filter((specId) => ids.includes(specId)).length) {\n\t\t\t\t\t\t// From https://drafts.csswg.org/css-color-4/#color-function\n\t\t\t\t\t\t// If more <number>s or <percentage>s are provided than parameters that the colorspace takes, the excess <number>s at the end are ignored.\n\t\t\t\t\t\t// If less <number>s or <percentage>s are provided than parameters that the colorspace takes, the missing parameters default to 0. (This is particularly convenient for multichannel printers where the additional inks are spot colors or varnishes that most colors on the page won’t use.)\n\t\t\t\t\t\tconst coords = Object.keys(space.coords).map((_, i) => env.parsed.args[i] || 0);\n\n\t\t\t\t\t\tlet types;\n\n\t\t\t\t\t\tif (colorSpec.coordGrammar) {\n\t\t\t\t\t\t\ttypes = coerceCoords(space, colorSpec, \"color\", coords);\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tif (meta) {\n\t\t\t\t\t\t\tObject.assign(meta, {formatId: \"color\", types});\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tif (colorSpec.id.startsWith(\"--\") && !id.startsWith(\"--\")) {\n\t\t\t\t\t\t\tdefaults.warn(`${space.name} is a non-standard space and not currently supported in the CSS spec. ` +\n\t\t\t\t\t\t\t              `Use prefixed color(${colorSpec.id}) instead of color(${id}).`);\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (id.startsWith(\"--\") && !colorSpec.id.startsWith(\"--\")) {\n\t\t\t\t\t\t\tdefaults.warn(`${space.name} is a standard space and supported in the CSS spec. ` +\n\t\t\t\t\t\t\t              `Use color(${colorSpec.id}) instead of prefixed color(${id}).`);\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\treturn {spaceId: space.id, coords, alpha};\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t// Not found\n\t\t\tlet didYouMean = \"\";\n\t\t\tlet registryId = id in ColorSpace.registry ? id : alternateId;\n\t\t\tif (registryId in ColorSpace.registry) {\n\t\t\t\t// Used color space id instead of color() id, these are often different\n\t\t\t\tlet cssId = ColorSpace.registry[registryId].formats?.color?.id;\n\n\t\t\t\tif (cssId) {\n\t\t\t\t\tdidYouMean = `Did you mean color(${cssId})?`;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tthrow new TypeError(`Cannot parse color(${id}). ` + (didYouMean || \"Missing a plugin?\"));\n\t\t}\n\t\telse {\n\t\t\tfor (let space of ColorSpace.all) {\n\t\t\t\t// color space specific function\n\t\t\t\tlet format = space.getFormat(name);\n\t\t\t\tif (format && format.type === \"function\") {\n\t\t\t\t\tlet alpha = 1;\n\n\t\t\t\t\tif (format.lastAlpha || util.last(env.parsed.args).alpha) {\n\t\t\t\t\t\talpha = env.parsed.args.pop();\n\t\t\t\t\t}\n\n\t\t\t\t\tlet coords = env.parsed.args;\n\n\t\t\t\t\tlet types;\n\n\t\t\t\t\tif (format.coordGrammar) {\n\t\t\t\t\t\ttypes = coerceCoords(space, format, name, coords);\n\t\t\t\t\t}\n\n\t\t\t\t\tif (meta) {\n\t\t\t\t\t\tObject.assign(meta, {formatId: format.name, types});\n\t\t\t\t\t}\n\n\t\t\t\t\treturn {\n\t\t\t\t\t\tspaceId: space.id,\n\t\t\t\t\t\tcoords, alpha,\n\t\t\t\t\t};\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\telse {\n\t\t// Custom, colorspace-specific format\n\t\tfor (let space of ColorSpace.all) {\n\t\t\tfor (let formatId in space.formats) {\n\t\t\t\tlet format = space.formats[formatId];\n\n\t\t\t\tif (format.type !== \"custom\") {\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\n\t\t\t\tif (format.test && !format.test(env.str)) {\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\n\t\t\t\tlet color = format.parse(env.str);\n\n\t\t\t\tif (color) {\n\t\t\t\t\tcolor.alpha ??= 1;\n\n\t\t\t\t\tif (meta) {\n\t\t\t\t\t\tmeta.formatId = formatId;\n\t\t\t\t\t}\n\n\t\t\t\t\treturn color;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\n\t// If we're here, we couldn't parse\n\tthrow new TypeError(`Could not parse ${str} as a color. Missing a plugin?`);\n}\n", "import ColorSpace from \"./space.js\";\nimport {isString} from \"./util.js\";\nimport parse from \"./parse.js\";\n\n/**\n * Resolves a color reference (object or string) to a plain color object\n * @param {Color | {space, coords, alpha} | string | Array<Color | {space, coords, alpha} | string> } color\n * @returns {{space, coords, alpha} | Array<{space, coords, alpha}}>\n */\nexport default function getColor (color) {\n\tif (Array.isArray(color)) {\n\t\treturn color.map(getColor);\n\t}\n\n\tif (!color) {\n\t\tthrow new TypeError(\"Empty color reference\");\n\t}\n\n\tif (isString(color)) {\n\t\tcolor = parse(color);\n\t}\n\n\t// Object fixup\n\tlet space = color.space || color.spaceId;\n\n\tif (!(space instanceof ColorSpace)) {\n\t\t// Convert string id to color space object\n\t\tcolor.space = ColorSpace.get(space);\n\t}\n\n\tif (color.alpha === undefined) {\n\t\tcolor.alpha = 1;\n\t}\n\n\treturn color;\n}\n", "import {type, parseCoordGrammar, serialize<PERSON><PERSON>ber, mapRange} from \"./util.js\";\nimport {getWhite} from \"./adapt.js\";\nimport hooks from \"./hooks.js\";\nimport getColor from \"./getColor.js\";\n\nconst ε = .000075;\n\n/**\n * Class to represent a color space\n */\nexport default class ColorSpace {\n\tconstructor (options) {\n\t\tthis.id = options.id;\n\t\tthis.name = options.name;\n\t\tthis.base = options.base ? ColorSpace.get(options.base) : null;\n\t\tthis.aliases = options.aliases;\n\n\t\tif (this.base) {\n\t\t\tthis.fromBase = options.fromBase;\n\t\t\tthis.toBase = options.toBase;\n\t\t}\n\n\t\t// Coordinate metadata\n\n\t\tlet coords = options.coords ?? this.base.coords;\n\n\t\tfor (let name in coords) {\n\t\t\tif (!(\"name\" in coords[name])) {\n\t\t\t\tcoords[name].name = name;\n\t\t\t}\n\t\t}\n\t\tthis.coords = coords;\n\n\t\t// White point\n\n\t\tlet white = options.white ?? this.base.white ?? \"D65\";\n\t\tthis.white = getWhite(white);\n\n\t\t// Sort out formats\n\n\t\tthis.formats = options.formats ?? {};\n\n\t\tfor (let name in this.formats) {\n\t\t\tlet format = this.formats[name];\n\t\t\tformat.type ||= \"function\";\n\t\t\tformat.name ||= name;\n\t\t}\n\n\t\tif (!this.formats.color?.id) {\n\t\t\tthis.formats.color = {\n\t\t\t\t...this.formats.color ?? {},\n\t\t\t\tid: options.cssId || this.id,\n\t\t\t};\n\t\t}\n\n\t\t// Gamut space\n\n\t\tif (options.gamutSpace) {\n\t\t\t// Gamut space explicitly specified\n\t\t\tthis.gamutSpace = options.gamutSpace === \"self\" ? this : ColorSpace.get(options.gamutSpace);\n\t\t}\n\t\telse {\n\t\t\t// No gamut space specified, calculate a sensible default\n\t\t\tif (this.isPolar) {\n\t\t\t\t// Do not check gamut through polar coordinates\n\t\t\t\tthis.gamutSpace = this.base;\n\t\t\t}\n\t\t\telse {\n\t\t\t\tthis.gamutSpace =  this;\n\t\t\t}\n\t\t}\n\n\t\t// Optimize inGamut for unbounded spaces\n\t\tif (this.gamutSpace.isUnbounded) {\n\t\t\tthis.inGamut = (coords, options) => {\n\t\t\t\treturn true;\n\t\t\t};\n\t\t}\n\n\t\t// Other stuff\n\t\tthis.referred = options.referred;\n\n\t\t// Compute ancestors and store them, since they will never change\n\t\tObject.defineProperty(this, \"path\", {\n\t\t\tvalue: getPath(this).reverse(),\n\t\t\twritable: false,\n\t\t\tenumerable: true,\n\t\t\tconfigurable: true,\n\t\t});\n\n\t\thooks.run(\"colorspace-init-end\", this);\n\t}\n\n\tinGamut (coords, {epsilon = ε} = {}) {\n\t\tif (!this.equals(this.gamutSpace)) {\n\t\t\tcoords = this.to(this.gamutSpace, coords);\n\t\t\treturn this.gamutSpace.inGamut(coords, {epsilon});\n\t\t}\n\n\t\tlet coordMeta = Object.values(this.coords);\n\n\t\treturn coords.every((c, i) => {\n\t\t\tlet meta = coordMeta[i];\n\n\t\t\tif (meta.type !== \"angle\" && meta.range) {\n\t\t\t\tif (Number.isNaN(c)) {\n\t\t\t\t\t// NaN is always in gamut\n\t\t\t\t\treturn true;\n\t\t\t\t}\n\n\t\t\t\tlet [min, max] = meta.range;\n\t\t\t\treturn (min === undefined || c >= min - epsilon)\n\t\t\t\t    && (max === undefined || c <= max + epsilon);\n\t\t\t}\n\n\t\t\treturn true;\n\t\t});\n\t}\n\n\tget isUnbounded () {\n\t\treturn Object.values(this.coords).every(coord => !(\"range\" in coord));\n\t}\n\n\tget cssId () {\n\t\treturn this.formats?.color?.id || this.id;\n\t}\n\n\tget isPolar () {\n\t\tfor (let id in this.coords) {\n\t\t\tif (this.coords[id].type === \"angle\") {\n\t\t\t\treturn true;\n\t\t\t}\n\t\t}\n\n\t\treturn false;\n\t}\n\n\tgetFormat (format) {\n\t\tif (typeof format === \"object\") {\n\t\t\tformat = processFormat(format, this);\n\t\t\treturn format;\n\t\t}\n\n\t\tlet ret;\n\t\tif (format === \"default\") {\n\t\t\t// Get first format\n\t\t\tret = Object.values(this.formats)[0];\n\t\t}\n\t\telse {\n\t\t\tret = this.formats[format];\n\t\t}\n\n\t\tif (ret) {\n\t\t\tret = processFormat(ret, this);\n\t\t\treturn ret;\n\t\t}\n\n\t\treturn null;\n\t}\n\n\t/**\n\t * Check if this color space is the same as another color space reference.\n\t * Allows proxying color space objects and comparing color spaces with ids.\n\t * @param {string | ColorSpace} space ColorSpace object or id to compare to\n\t * @returns {boolean}\n\t */\n\tequals (space) {\n\t\tif (!space) {\n\t\t\treturn false;\n\t\t}\n\n\t\treturn this === space || this.id === space || this.id === space.id;\n\t}\n\n\tto (space, coords) {\n\t\tif (arguments.length === 1) {\n\t\t\tconst color = getColor(space);\n\t\t\t[space, coords] = [color.space, color.coords];\n\t\t}\n\n\t\tspace = ColorSpace.get(space);\n\n\t\tif (this.equals(space)) {\n\t\t\t// Same space, no change needed\n\t\t\treturn coords;\n\t\t}\n\n\t\t// Convert NaN to 0, which seems to be valid in every coordinate of every color space\n\t\tcoords = coords.map(c => Number.isNaN(c) ? 0 : c);\n\n\t\t// Find connection space = lowest common ancestor in the base tree\n\t\tlet myPath = this.path;\n\t\tlet otherPath = space.path;\n\n\t\tlet connectionSpace, connectionSpaceIndex;\n\n\t\tfor (let i = 0; i < myPath.length; i++) {\n\t\t\tif (myPath[i].equals(otherPath[i])) {\n\t\t\t\tconnectionSpace = myPath[i];\n\t\t\t\tconnectionSpaceIndex = i;\n\t\t\t}\n\t\t\telse {\n\t\t\t\tbreak;\n\t\t\t}\n\t\t}\n\n\t\tif (!connectionSpace) {\n\t\t\t// This should never happen\n\t\t\tthrow new Error(`Cannot convert between color spaces ${this} and ${space}: no connection space was found`);\n\t\t}\n\n\t\t// Go up from current space to connection space\n\t\tfor (let i = myPath.length - 1; i > connectionSpaceIndex; i--) {\n\t\t\tcoords = myPath[i].toBase(coords);\n\t\t}\n\n\t\t// Go down from connection space to target space\n\t\tfor (let i = connectionSpaceIndex + 1; i < otherPath.length; i++) {\n\t\t\tcoords = otherPath[i].fromBase(coords);\n\t\t}\n\n\t\treturn coords;\n\t}\n\n\tfrom (space, coords) {\n\t\tif (arguments.length === 1) {\n\t\t\tconst color = getColor(space);\n\t\t\t[space, coords] = [color.space, color.coords];\n\t\t}\n\n\t\tspace = ColorSpace.get(space);\n\n\t\treturn space.to(this, coords);\n\t}\n\n\ttoString () {\n\t\treturn `${this.name} (${this.id})`;\n\t}\n\n\tgetMinCoords () {\n\t\tlet ret = [];\n\n\t\tfor (let id in this.coords) {\n\t\t\tlet meta = this.coords[id];\n\t\t\tlet range = meta.range || meta.refRange;\n\t\t\tret.push(range?.min ?? 0);\n\t\t}\n\n\t\treturn ret;\n\t}\n\n\tstatic registry = {};\n\n\t// Returns array of unique color spaces\n\tstatic get all () {\n\t\treturn [...new Set(Object.values(ColorSpace.registry))];\n\t}\n\n\tstatic register (id, space) {\n\t\tif (arguments.length === 1) {\n\t\t\tspace = arguments[0];\n\t\t\tid = space.id;\n\t\t}\n\n\t\tspace = this.get(space);\n\n\t\tif (this.registry[id] && this.registry[id] !== space) {\n\t\t\tthrow new Error(`Duplicate color space registration: '${id}'`);\n\t\t}\n\t\tthis.registry[id] = space;\n\n\t\t// Register aliases when called without an explicit ID.\n\t\tif (arguments.length === 1 && space.aliases) {\n\t\t\tfor (let alias of space.aliases) {\n\t\t\t\tthis.register(alias, space);\n\t\t\t}\n\t\t}\n\n\t\treturn space;\n\t}\n\n\t/**\n\t * Lookup ColorSpace object by name\n\t * @param {ColorSpace | string} name\n\t */\n\tstatic get (space, ...alternatives) {\n\t\tif (!space || space instanceof ColorSpace) {\n\t\t\treturn space;\n\t\t}\n\n\t\tlet argType = type(space);\n\n\t\tif (argType === \"string\") {\n\t\t\t// It's a color space id\n\t\t\tlet ret = ColorSpace.registry[space.toLowerCase()];\n\n\t\t\tif (!ret) {\n\t\t\t\tthrow new TypeError(`No color space found with id = \"${space}\"`);\n\t\t\t}\n\n\t\t\treturn ret;\n\t\t}\n\n\t\tif (alternatives.length) {\n\t\t\treturn ColorSpace.get(...alternatives);\n\t\t}\n\n\t\tthrow new TypeError(`${space} is not a valid color space`);\n\t}\n\n\t/**\n\t * Get metadata about a coordinate of a color space\n\t *\n\t * @static\n\t * @param {Array | string} ref\n\t * @param {ColorSpace | string} [workingSpace]\n\t * @return {Object}\n\t */\n\tstatic resolveCoord (ref, workingSpace) {\n\t\tlet coordType = type(ref);\n\t\tlet space, coord;\n\n\t\tif (coordType === \"string\") {\n\t\t\tif (ref.includes(\".\")) {\n\t\t\t\t// Absolute coordinate\n\t\t\t\t[space, coord] = ref.split(\".\");\n\t\t\t}\n\t\t\telse {\n\t\t\t\t// Relative coordinate\n\t\t\t\t[space, coord] = [, ref];\n\t\t\t}\n\t\t}\n\t\telse if (Array.isArray(ref)) {\n\t\t\t[space, coord] = ref;\n\t\t}\n\t\telse {\n\t\t\t// Object\n\t\t\tspace = ref.space;\n\t\t\tcoord = ref.coordId;\n\t\t}\n\n\t\tspace = ColorSpace.get(space);\n\n\t\tif (!space) {\n\t\t\tspace = workingSpace;\n\t\t}\n\n\t\tif (!space) {\n\t\t\tthrow new TypeError(`Cannot resolve coordinate reference ${ref}: No color space specified and relative references are not allowed here`);\n\t\t}\n\n\t\tcoordType = type(coord);\n\n\t\tif (coordType === \"number\" || coordType === \"string\" && coord >= 0) {\n\t\t\t// Resolve numerical coord\n\t\t\tlet meta = Object.entries(space.coords)[coord];\n\n\t\t\tif (meta) {\n\t\t\t\treturn {space, id: meta[0], index: coord, ...meta[1]};\n\t\t\t}\n\t\t}\n\n\t\tspace = ColorSpace.get(space);\n\n\t\tlet normalizedCoord = coord.toLowerCase();\n\n\t\tlet i = 0;\n\t\tfor (let id in space.coords) {\n\t\t\tlet meta = space.coords[id];\n\n\t\t\tif (id.toLowerCase() === normalizedCoord || meta.name?.toLowerCase() === normalizedCoord) {\n\t\t\t\treturn {space, id, index: i, ...meta};\n\t\t\t}\n\n\t\t\ti++;\n\t\t}\n\n\t\tthrow new TypeError(`No \"${coord}\" coordinate found in ${space.name}. Its coordinates are: ${Object.keys(space.coords).join(\", \")}`);\n\t}\n\n\tstatic DEFAULT_FORMAT = {\n\t\ttype: \"functions\",\n\t\tname: \"color\",\n\t};\n}\n\nfunction getPath (space) {\n\tlet ret = [space];\n\n\tfor (let s = space; s = s.base;) {\n\t\tret.push(s);\n\t}\n\n\treturn ret;\n}\n\nfunction processFormat (format, {coords} = {}) {\n\tif (format.coords && !format.coordGrammar) {\n\t\tformat.type ||= \"function\";\n\t\tformat.name ||= \"color\";\n\n\t\t// Format has not been processed\n\t\tformat.coordGrammar = parseCoordGrammar(format.coords);\n\n\t\tlet coordFormats = Object.entries(coords).map(([id, coordMeta], i) => {\n\t\t\t// Preferred format for each coord is the first one\n\t\t\tlet outputType = format.coordGrammar[i][0];\n\n\t\t\tlet fromRange = coordMeta.range || coordMeta.refRange;\n\t\t\tlet toRange = outputType.range, suffix = \"\";\n\n\t\t\t// Non-strict equals intentional since outputType could be a string object\n\t\t\tif (outputType == \"<percentage>\") {\n\t\t\t\ttoRange = [0, 100];\n\t\t\t\tsuffix = \"%\";\n\t\t\t}\n\t\t\telse if (outputType == \"<angle>\") {\n\t\t\t\tsuffix = \"deg\";\n\t\t\t}\n\n\t\t\treturn  {fromRange, toRange, suffix};\n\t\t});\n\n\t\tformat.serializeCoords = (coords, precision) => {\n\t\t\treturn coords.map((c, i) => {\n\t\t\t\tlet {fromRange, toRange, suffix} = coordFormats[i];\n\n\t\t\t\tif (fromRange && toRange) {\n\t\t\t\t\tc = mapRange(fromRange, toRange, c);\n\t\t\t\t}\n\n\t\t\t\tc = serializeNumber(c, {precision, unit: suffix});\n\n\t\t\t\treturn c;\n\t\t\t});\n\t\t};\n\t}\n\n\treturn format;\n}\n", "import ColorSpace from \"../space.js\";\n\nexport default new ColorSpace({\n\tid: \"xyz-d65\",\n\tname: \"XYZ D65\",\n\tcoords: {\n\t\tx: {name: \"X\"},\n\t\ty: {name: \"Y\"},\n\t\tz: {name: \"Z\"},\n\t},\n\twhite: \"D65\",\n\tformats: {\n\t\tcolor: {\n\t\t\tids: [\"xyz-d65\", \"xyz\"],\n\t\t},\n\t},\n\taliases: [\"xyz\"],\n});\n", "import ColorSpace from \"./space.js\";\nimport {multiplyMatrices} from \"./util.js\";\nimport adapt from \"./adapt.js\";\nimport XYZ_D65 from \"./spaces/xyz-d65.js\";\n\n/**\n * Convenience class for RGB color spaces\n * @extends {ColorSpace}\n */\nexport default class RGBColorSpace extends ColorSpace {\n\t/**\n\t * Creates a new RGB ColorSpace.\n\t * If coords are not specified, they will use the default RGB coords.\n\t * Instead of `fromBase()` and `toBase()` functions,\n\t * you can specify to/from XYZ matrices and have `toBase()` and `fromBase()` automatically generated.\n\t * @param {*} options - Same options as {@link ColorSpace} plus:\n\t * @param {number[][]} options.toXYZ_M - Matrix to convert to XYZ\n\t * @param {number[][]} options.fromXYZ_M - Matrix to convert from XYZ\n\t */\n\tconstructor (options) {\n\t\tif (!options.coords) {\n\t\t\toptions.coords = {\n\t\t\t\tr: {\n\t\t\t\t\trange: [0, 1],\n\t\t\t\t\tname: \"Red\",\n\t\t\t\t},\n\t\t\t\tg: {\n\t\t\t\t\trange: [0, 1],\n\t\t\t\t\tname: \"<PERSON>\",\n\t\t\t\t},\n\t\t\t\tb: {\n\t\t\t\t\trange: [0, 1],\n\t\t\t\t\tname: \"<PERSON>\",\n\t\t\t\t},\n\t\t\t};\n\t\t}\n\n\t\tif (!options.base) {\n\t\t\toptions.base = XYZ_D65;\n\t\t}\n\n\t\tif (options.toXYZ_M && options.fromXYZ_M) {\n\t\t\toptions.toBase ??= rgb => {\n\t\t\t\tlet xyz = multiplyMatrices(options.toXYZ_M, rgb);\n\n\t\t\t\tif (this.white !== this.base.white) {\n\t\t\t\t\t// Perform chromatic adaptation\n\t\t\t\t\txyz = adapt(this.white, this.base.white, xyz);\n\t\t\t\t}\n\n\t\t\t\treturn xyz;\n\t\t\t};\n\n\t\t\toptions.fromBase ??= xyz => {\n\t\t\t\txyz = adapt(this.base.white, this.white, xyz);\n\t\t\t\treturn multiplyMatrices(options.fromXYZ_M, xyz);\n\t\t\t};\n\t\t}\n\n\t\toptions.referred ??= \"display\";\n\n\t\tsuper(options);\n\t}\n}\n", "import ColorSpace from \"./space.js\";\nimport getColor from \"./getColor.js\";\n\n/**\n * Get the coordinates of a color in any color space\n * @param {Color} color\n * @param {string | ColorSpace} [space = color.space] The color space to convert to. Defaults to the color's current space\n * @returns {number[]} The color coordinates in the given color space\n */\nexport default function getAll (color, space) {\n\tcolor = getColor(color);\n\n\tif (!space || color.space.equals(space)) {\n\t\t// No conversion needed\n\t\treturn color.coords.slice();\n\t}\n\n\tspace = ColorSpace.get(space);\n\treturn space.from(color);\n}\n", "import ColorSpace from \"./space.js\";\nimport getAll from \"./getAll.js\";\nimport getColor from \"./getColor.js\";\n\nexport default function get (color, prop) {\n\tcolor = getColor(color);\n\n\tlet {space, index} = ColorSpace.resolveCoord(prop, color.space);\n\tlet coords = getAll(color, space);\n\treturn coords[index];\n}\n", "import ColorSpace from \"./space.js\";\nimport getColor from \"./getColor.js\";\n\nexport default function setAll (color, space, coords) {\n\tcolor = getColor(color);\n\n\tspace = ColorSpace.get(space);\n\tcolor.coords = space.to(color.space, coords);\n\treturn color;\n}\n\nsetAll.returns = \"color\";\n", "import ColorSpace from \"./space.js\";\nimport getColor from \"./getColor.js\";\nimport get from \"./get.js\";\nimport getAll from \"./getAll.js\";\nimport setAll from \"./setAll.js\";\nimport {type} from \"./util.js\";\n\n// Set properties and return current instance\nexport default function set (color, prop, value) {\n\tcolor = getColor(color);\n\n\tif (arguments.length === 2 && type(arguments[1]) === \"object\") {\n\t\t// Argument is an object literal\n\t\tlet object = arguments[1];\n\t\tfor (let p in object) {\n\t\t\tset(color, p, object[p]);\n\t\t}\n\t}\n\telse {\n\t\tif (typeof value === \"function\") {\n\t\t\tvalue = value(get(color, prop));\n\t\t}\n\n\t\tlet {space, index} = ColorSpace.resolveCoord(prop, color.space);\n\t\tlet coords = getAll(color, space);\n\t\tcoords[index] = value;\n\t\tsetAll(color, space, coords);\n\t}\n\n\treturn color;\n}\n\nset.returns = \"color\";\n", "import ColorSpace from \"../space.js\";\nimport adapt from \"../adapt.js\";\nimport XYZ_D65 from \"./xyz-d65.js\";\n\nexport default new ColorSpace({\n\tid: \"xyz-d50\",\n\tname: \"XYZ D50\",\n\twhite: \"D50\",\n\tbase: XYZ_D65,\n\tfromBase: coords => adapt(XYZ_D65.white, \"D50\", coords),\n\ttoBase: coords => adapt(\"D50\", XYZ_D65.white, coords),\n});\n", "import ColorSpace from \"../space.js\";\nimport {WHITES} from \"../adapt.js\";\nimport xyz_d50 from \"./xyz-d50.js\";\n\n// κ * ε  = 2^3 = 8\nconst ε = 216 / 24389;  // 6^3/29^3 == (24/116)^3\nconst ε3 = 24 / 116;\nconst κ = 24389 / 27;   // 29^3/3^3\n\nlet white = WHITES.D50;\n\nexport default new ColorSpace({\n\tid: \"lab\",\n\tname: \"Lab\",\n\tcoords: {\n\t\tl: {\n\t\t\trefRange: [0, 100],\n\t\t\tname: \"Lightness\",\n\t\t},\n\t\ta: {\n\t\t\trefRange: [-125, 125],\n\t\t},\n\t\tb: {\n\t\t\trefRange: [-125, 125],\n\t\t},\n\t},\n\n\t// Assuming XYZ is relative to D50, convert to CIE Lab\n\t// from CIE standard, which now defines these as a rational fraction\n\twhite,\n\n\tbase: xyz_d50,\n\t// Convert D50-adapted XYX to Lab\n\t//  CIE 15.3:2004 section 8.2.1.1\n\tfromBase (XYZ) {\n\t\t// compute xyz, which is XYZ scaled relative to reference white\n\t\tlet xyz = XYZ.map((value, i) => value / white[i]);\n\n\t\t// now compute f\n\t\tlet f = xyz.map(value => value > ε ? Math.cbrt(value) : (κ * value + 16) / 116);\n\n\t\treturn [\n\t\t\t(116 * f[1]) - 16,   // L\n\t\t\t500 * (f[0] - f[1]), // a\n\t\t\t200 * (f[1] - f[2]),  // b\n\t\t];\n\t},\n\t// Convert Lab to D50-adapted XYZ\n\t// Same result as CIE 15.3:2004 Appendix D although the derivation is different\n\t// http://www.brucelindbloom.com/index.html?Eqn_RGB_XYZ_Matrix.html\n\ttoBase (Lab) {\n\t\t// compute f, starting with the luminance-related term\n\t\tlet f = [];\n\t\tf[1] = (Lab[0] + 16) / 116;\n\t\tf[0] = Lab[1] / 500 + f[1];\n\t\tf[2] = f[1] - Lab[2] / 200;\n\n\t\t// compute xyz\n\t\tlet xyz = [\n\t\t\tf[0]   > ε3 ? Math.pow(f[0], 3)                : (116 * f[0] - 16) / κ,\n\t\t\tLab[0] > 8  ? Math.pow((Lab[0] + 16) / 116, 3) : Lab[0] / κ,\n\t\t\tf[2]   > ε3 ? Math.pow(f[2], 3)                : (116 * f[2] - 16) / κ,\n\t\t];\n\n\t\t// Compute XYZ by scaling xyz by reference white\n\t\treturn xyz.map((value, i) => value * white[i]);\n\t},\n\n\tformats: {\n\t\t\"lab\": {\n\t\t\tcoords: [\"<number> | <percentage>\", \"<number> | <percentage>[-1,1]\", \"<number> | <percentage>[-1,1]\"],\n\t\t},\n\t},\n});\n", "export function constrain (angle) {\n\treturn ((angle % 360) + 360) % 360;\n}\n\nexport function adjust (arc, angles) {\n\tif (arc === \"raw\") {\n\t\treturn angles;\n\t}\n\n\tlet [a1, a2] = angles.map(constrain);\n\n\tlet angleDiff = a2 - a1;\n\n\tif (arc === \"increasing\") {\n\t\tif (angleDiff < 0) {\n\t\t\ta2 += 360;\n\t\t}\n\t}\n\telse if (arc === \"decreasing\") {\n\t\tif (angleDiff > 0) {\n\t\t\ta1 += 360;\n\t\t}\n\t}\n\telse if (arc === \"longer\") {\n\t\tif (-180 < angleDiff && angleDiff < 180) {\n\t\t\tif (angleDiff > 0) {\n\t\t\t\ta1 += 360;\n\t\t\t}\n\t\t\telse {\n\t\t\t\ta2 += 360;\n\t\t\t}\n\t\t}\n\t}\n\telse if (arc === \"shorter\") {\n\t\tif (angleDiff > 180) {\n\t\t\ta1 += 360;\n\t\t}\n\t\telse if (angleDiff < -180) {\n\t\t\ta2 += 360;\n\t\t}\n\t}\n\n\treturn [a1, a2];\n}\n", "import ColorSpace from \"../space.js\";\nimport Lab from \"./lab.js\";\nimport {constrain as constrainAngle} from \"../angles.js\";\n\nexport default new ColorSpace({\n\tid: \"lch\",\n\tname: \"<PERSON><PERSON>\",\n\tcoords: {\n\t\tl: {\n\t\t\trefRange: [0, 100],\n\t\t\tname: \"Lightness\",\n\t\t},\n\t\tc: {\n\t\t\trefRange: [0, 150],\n\t\t\tname: \"Chroma\",\n\t\t},\n\t\th: {\n\t\t\trefRange: [0, 360],\n\t\t\ttype: \"angle\",\n\t\t\tname: \"<PERSON><PERSON>\",\n\t\t},\n\t},\n\n\tbase: Lab,\n\tfromBase (Lab) {\n\t\t// Convert to polar form\n\t\tlet [L, a, b] = Lab;\n\t\tlet hue;\n\t\tconst ε = 0.02;\n\n\t\tif (Math.abs(a) < ε && Math.abs(b) < ε) {\n\t\t\thue = NaN;\n\t\t}\n\t\telse {\n\t\t\thue = Math.atan2(b, a) * 180 / Math.PI;\n\t\t}\n\n\t\treturn [\n\t\t\tL, // L is still L\n\t\t\tMath.sqrt(a ** 2 + b ** 2), // Chroma\n\t\t\tconstrainAngle(hue), // Hue, in degrees [0 to 360)\n\t\t];\n\t},\n\ttoBase (LCH) {\n\t\t// Convert from polar form\n\t\tlet [Lightness, Chroma, Hue] = LCH;\n\t\t// Clamp any negative Chroma\n\t\tif (Chroma < 0) {\n\t\t\tChroma = 0;\n\t\t}\n\t\t// Deal with NaN Hue\n\t\tif (isNaN(Hue)) {\n\t\t\tHue = 0;\n\t\t}\n\t\treturn [\n\t\t\tLightness, // L is still L\n\t\t\tChroma * Math.cos(Hue * Math.PI / 180), // a\n\t\t\tChroma * Math.sin(Hue * Math.PI / 180),  // b\n\t\t];\n\t},\n\n\tformats: {\n\t\t\"lch\": {\n\t\t\tcoords: [\"<number> | <percentage>\", \"<number> | <percentage>\", \"<number> | <angle>\"],\n\t\t},\n\t},\n});\n", "import defaults from \"../defaults.js\";\nimport lab from \"../spaces/lab.js\";\nimport lch from \"../spaces/lch.js\";\nimport getColor from \"../getColor.js\";\n\n// deltaE2000 is a statistically significant improvement\n// and is recommended by the CIE and Idealliance\n// especially for color differences less than 10 deltaE76\n// but is wicked complicated\n// and many implementations have small errors!\n// DeltaE2000 is also discontinuous; in case this\n// matters to you, use deltaECMC instead.\n\nconst Gfactor = 25 ** 7;\nconst π = Math.PI;\nconst r2d = 180 / π;\nconst d2r = π / 180;\n\nfunction pow7 (x) {\n\t// Faster than x ** 7 or Math.pow(x, 7)\n\n\tconst x2 = x * x;\n\tconst x7 = x2 * x2 * x2 * x;\n\n\treturn x7;\n}\n\nexport default function (color, sample, {kL = 1, kC = 1, kH = 1} = {}) {\n\t[color, sample] = getColor([color, sample]);\n\n\t// Given this color as the reference\n\t// and the function parameter as the sample,\n\t// calculate deltaE 2000.\n\n\t// This implementation assumes the parametric\n\t// weighting factors kL, kC and kH\n\t// for the influence of viewing conditions\n\t// are all 1, as sadly seems typical.\n\t// kL should be increased for lightness texture or noise\n\t// and kC increased for chroma noise\n\n\tlet [L1, a1, b1] = lab.from(color);\n\tlet C1 = lch.from(lab, [L1, a1, b1])[1];\n\tlet [L2, a2, b2] = lab.from(sample);\n\tlet C2 = lch.from(lab, [L2, a2, b2])[1];\n\n\t// Check for negative Chroma,\n\t// which might happen through\n\t// direct user input of LCH values\n\n\tif (C1 < 0) {\n\t\tC1 = 0;\n\t}\n\tif (C2 < 0) {\n\t\tC2 = 0;\n\t}\n\n\tlet Cbar = (C1 + C2) / 2; // mean Chroma\n\n\t// calculate a-axis asymmetry factor from mean Chroma\n\t// this turns JND ellipses for near-neutral colors back into circles\n\tlet C7 = pow7(Cbar);\n\n\tlet G = 0.5 * (1 - Math.sqrt(C7 / (C7 + Gfactor)));\n\n\t// scale a axes by asymmetry factor\n\t// this by the way is why there is no Lab2000 colorspace\n\tlet adash1 = (1 + G) * a1;\n\tlet adash2 = (1 + G) * a2;\n\n\t// calculate new Chroma from scaled a and original b axes\n\tlet Cdash1 = Math.sqrt(adash1 ** 2 + b1 ** 2);\n\tlet Cdash2 = Math.sqrt(adash2 ** 2 + b2 ** 2);\n\n\t// calculate new hues, with zero hue for true neutrals\n\t// and in degrees, not radians\n\n\tlet h1 = (adash1 === 0 && b1 === 0) ? 0 : Math.atan2(b1, adash1);\n\tlet h2 = (adash2 === 0 && b2 === 0) ? 0 : Math.atan2(b2, adash2);\n\n\tif (h1 < 0) {\n\t\th1 += 2 * π;\n\t}\n\tif (h2 < 0) {\n\t\th2 += 2 * π;\n\t}\n\n\th1 *= r2d;\n\th2 *= r2d;\n\n\t// Lightness and Chroma differences; sign matters\n\tlet ΔL = L2 - L1;\n\tlet ΔC = Cdash2 - Cdash1;\n\n\t// Hue difference, getting the sign correct\n\tlet hdiff = h2 - h1;\n\tlet hsum = h1 + h2;\n\tlet habs = Math.abs(hdiff);\n\tlet Δh;\n\n\tif (Cdash1 * Cdash2 === 0) {\n\t\tΔh = 0;\n\t}\n\telse if (habs <= 180) {\n\t\tΔh = hdiff;\n\t}\n\telse if (hdiff > 180) {\n\t\tΔh = hdiff - 360;\n\t}\n\telse if (hdiff < -180) {\n\t\tΔh = hdiff + 360;\n\t}\n\telse {\n\t\tdefaults.warn(\"the unthinkable has happened\");\n\t}\n\n\t// weighted Hue difference, more for larger Chroma\n\tlet ΔH = 2 * Math.sqrt(Cdash2 * Cdash1) * Math.sin(Δh * d2r / 2);\n\n\t// calculate mean Lightness and Chroma\n\tlet Ldash = (L1 + L2) / 2;\n\tlet Cdash = (Cdash1 + Cdash2) / 2;\n\tlet Cdash7 = pow7(Cdash);\n\n\t// Compensate for non-linearity in the blue region of Lab.\n\t// Four possibilities for hue weighting factor,\n\t// depending on the angles, to get the correct sign\n\tlet hdash;\n\tif (Cdash1 * Cdash2 === 0) {\n\t\thdash = hsum;   // which should be zero\n\t}\n\telse if (habs <= 180) {\n\t\thdash = hsum / 2;\n\t}\n\telse if (hsum < 360) {\n\t\thdash = (hsum + 360) / 2;\n\t}\n\telse {\n\t\thdash = (hsum - 360) / 2;\n\t}\n\n\t// positional corrections to the lack of uniformity of CIELAB\n\t// These are all trying to make JND ellipsoids more like spheres\n\n\t// SL Lightness crispening factor\n\t// a background with L=50 is assumed\n\tlet lsq = (Ldash - 50) ** 2;\n\tlet SL = 1 + ((0.015 * lsq) / Math.sqrt(20 + lsq));\n\n\t// SC Chroma factor, similar to those in CMC and deltaE 94 formulae\n\tlet SC = 1 + 0.045 * Cdash;\n\n\t// Cross term T for blue non-linearity\n\tlet T = 1;\n\tT -= (0.17 * Math.cos((     hdash - 30)  * d2r));\n\tT += (0.24 * Math.cos(  2 * hdash        * d2r));\n\tT += (0.32 * Math.cos(((3 * hdash) + 6)  * d2r));\n\tT -= (0.20 * Math.cos(((4 * hdash) - 63) * d2r));\n\n\t// SH Hue factor depends on Chroma,\n\t// as well as adjusted hue angle like deltaE94.\n\tlet SH = 1 + 0.015 * Cdash * T;\n\n\t// RT Hue rotation term compensates for rotation of JND ellipses\n\t// and Munsell constant hue lines\n\t// in the medium-high Chroma blue region\n\t// (Hue 225 to 315)\n\tlet Δθ = 30 * Math.exp(-1 * (((hdash - 275) / 25) ** 2));\n\tlet RC = 2 * Math.sqrt(Cdash7 / (Cdash7 + Gfactor));\n\tlet RT = -1 * Math.sin(2 * Δθ * d2r) * RC;\n\n\t// Finally calculate the deltaE, term by term as root sume of squares\n\tlet dE = (ΔL / (kL * SL)) ** 2;\n\tdE += (ΔC / (kC * SC)) ** 2;\n\tdE += (ΔH / (kH * SH)) ** 2;\n\tdE += RT * (ΔC / (kC * SC)) * (ΔH / (kH * SH));\n\treturn Math.sqrt(dE);\n\t// Yay!!!\n}\n", "import ColorSpace from \"../space.js\";\nimport {multiplyMatrices} from \"../util.js\";\nimport XYZ_D65 from \"./xyz-d65.js\";\n\n// Recalculated for consistent reference white\n// see https://github.com/w3c/csswg-drafts/issues/6642#issuecomment-943521484\nconst XY<PERSON>toLMS_M = [\n\t[ 0.8190224379967030, 0.3619062600528904, -0.1288737815209879 ],\n\t[ 0.0329836539323885, 0.9292868615863434,  0.0361446663506424 ],\n\t[ 0.0481771893596242, 0.2642395317527308,  0.6335478284694309 ],\n];\n// inverse of XYZtoLMS_M\nconst LMStoXYZ_M = [\n\t[  1.2268798758459243, -0.5578149944602171,  0.2813910456659647 ],\n\t[ -0.0405757452148008,  1.1122868032803170, -0.0717110580655164 ],\n\t[ -0.0763729366746601, -0.4214933324022432,  1.5869240198367816 ],\n];\nconst LMStoLab_M = [\n\t[ 0.2104542683093140,  0.7936177747023054, -0.0040720430116193 ],\n\t[ 1.9779985324311684, -2.4285922420485799,  0.4505937096174110 ],\n\t[ 0.0259040424655478,  0.7827717124575296, -0.8086757549230774 ],\n];\n// LMStoIab_M inverted\nconst LabtoLMS_M = [\n\t[ 1.0000000000000000,  0.3963377773761749,  0.2158037573099136 ],\n\t[ 1.0000000000000000, -0.1055613458156586, -0.0638541728258133 ],\n\t[ 1.0000000000000000, -0.0894841775298119, -1.2914855480194092 ],\n];\n\nexport default new ColorSpace({\n\tid: \"oklab\",\n\tname: \"Oklab\",\n\tcoords: {\n\t\tl: {\n\t\t\trefRange: [0, 1],\n\t\t\tname: \"Lightness\",\n\t\t},\n\t\ta: {\n\t\t\trefRange: [-0.4, 0.4],\n\t\t},\n\t\tb: {\n\t\t\trefRange: [-0.4, 0.4],\n\t\t},\n\t},\n\n\t// Note that XYZ is relative to D65\n\twhite: \"D65\",\n\tbase: XYZ_D65,\n\tfromBase (XYZ) {\n\t\t// move to LMS cone domain\n\t\tlet LMS = multiplyMatrices(XYZtoLMS_M, XYZ);\n\n\t\t// non-linearity\n\t\tlet LMSg = LMS.map(val => Math.cbrt(val));\n\n\t\treturn multiplyMatrices(LMStoLab_M, LMSg);\n\n\t},\n\ttoBase (OKLab) {\n\t\t// move to LMS cone domain\n\t\tlet LMSg = multiplyMatrices(LabtoLMS_M, OKLab);\n\n\t\t// restore linearity\n\t\tlet LMS = LMSg.map(val => val ** 3);\n\n\t\treturn multiplyMatrices(LMStoXYZ_M, LMS);\n\t},\n\n\tformats: {\n\t\t\"oklab\": {\n\t\t\tcoords: [\"<percentage> | <number>\", \"<number> | <percentage>[-1,1]\", \"<number> | <percentage>[-1,1]\"],\n\t\t},\n\t},\n});\n", "// More accurate color-difference formulae\n// than the simple 1976 Euclidean distance in CIE Lab\n\nimport oklab from \"../spaces/oklab.js\";\nimport getColor from \"../getColor.js\";\n\nexport default function (color, sample) {\n\t[color, sample] = getColor([color, sample]);\n\n\t// Given this color as the reference\n\t// and a sample,\n\t// calculate deltaEOK, term by term as root sum of squares\n\tlet [L1, a1, b1] = oklab.from(color);\n\tlet [L2, a2, b2] = oklab.from(sample);\n\tlet ΔL = L1 - L2;\n\tlet Δa = a1 - a2;\n\tlet Δb = b1 - b2;\n\treturn Math.sqrt(ΔL ** 2 + Δa ** 2 + Δb ** 2);\n}\n", "import ColorSpace from \"./space.js\";\nimport getColor from \"./getColor.js\";\n\nconst ε = .000075;\n\n/**\n * Check if a color is in gamut of either its own or another color space\n * @return {Boolean} Is the color in gamut?\n */\nexport default function inGamut (color, space, {epsilon = ε} = {}) {\n\tcolor = getColor(color);\n\n\tif (!space) {\n\t\tspace = color.space;\n\t}\n\n\tspace = ColorSpace.get(space);\n\tlet coords = color.coords;\n\n\tif (space !== color.space) {\n\t\tcoords = space.from(color);\n\t}\n\n\treturn space.inGamut(coords, {epsilon});\n}\n", "export default function clone (color) {\n\treturn {\n\t\tspace: color.space,\n\t\tcoords: color.coords.slice(),\n\t\talpha: color.alpha,\n\t};\n}\n", "import ColorSpace from \"./space.js\";\n\n/**\n * Euclidean distance of colors in an arbitrary color space\n */\nexport default function distance (color1, color2, space = \"lab\") {\n\tspace = ColorSpace.get(space);\n\n\t// Assume getColor() is called on color in space.from()\n\tlet coords1 = space.from(color1);\n\tlet coords2 = space.from(color2);\n\n\treturn Math.sqrt(coords1.reduce((acc, c1, i) => {\n\t\tlet c2 = coords2[i];\n\t\tif (isNaN(c1) || isNaN(c2)) {\n\t\t\treturn acc;\n\t\t}\n\n\t\treturn acc + (c2 - c1) ** 2;\n\t}, 0));\n}\n", "import lab from \"../spaces/lab.js\";\nimport lch from \"../spaces/lch.js\";\nimport getColor from \"../getColor.js\";\n\n// More accurate color-difference formulae\n// than the simple 1976 Euclidean distance in Lab\n\n// CMC by the Color Measurement Committee of the\n// Bradford Society of Dyeists and Colorsts, 1994.\n// Uses LCH rather than Lab,\n// with different weights for L, C and H differences\n// A nice increase in accuracy for modest increase in complexity\nconst π = Math.PI;\nconst d2r = π / 180;\n\nexport default function (color, sample, {l = 2, c = 1} = {}) {\n\t[color, sample] = getColor([color, sample]);\n\n\t// Given this color as the reference\n\t// and a sample,\n\t// calculate deltaE CMC.\n\n\t// This implementation assumes the parametric\n\t// weighting factors l:c are 2:1\n\t// which is typical for non-textile uses.\n\n\tlet [L1, a1, b1] = lab.from(color);\n\tlet [, C1, H1] = lch.from(lab, [L1, a1, b1]);\n\tlet [L2, a2, b2] = lab.from(sample);\n\tlet C2 = lch.from(lab, [L2, a2, b2])[1];\n\n\t// let [L1, a1, b1] = color.getAll(lab);\n\t// let C1 = color.get(\"lch.c\");\n\t// let H1 = color.get(\"lch.h\");\n\t// let [L2, a2, b2] = sample.getAll(lab);\n\t// let C2 = sample.get(\"lch.c\");\n\n\t// Check for negative Chroma,\n\t// which might happen through\n\t// direct user input of LCH values\n\n\tif (C1 < 0) {\n\t\tC1 = 0;\n\t}\n\tif (C2 < 0) {\n\t\tC2 = 0;\n\t}\n\n\t// we don't need H2 as ΔH is calculated from Δa, Δb and ΔC\n\n\t// Lightness and Chroma differences\n\t// These are (color - sample), unlike deltaE2000\n\tlet ΔL = L1 - L2;\n\tlet ΔC = C1 - C2;\n\n\tlet Δa = a1 - a2;\n\tlet Δb = b1 - b2;\n\n\t// weighted Hue difference, less for larger Chroma difference\n\n\tlet H2 = (Δa ** 2) + (Δb ** 2) - (ΔC ** 2);\n\t// due to roundoff error it is possible that, for zero a and b,\n\t// ΔC > Δa + Δb is 0, resulting in attempting\n\t// to take the square root of a negative number\n\n\t// trying instead the equation from Industrial Color Physics\n\t// By Georg A. Klein\n\n\t// let ΔH = ((a1 * b2) - (a2 * b1)) / Math.sqrt(0.5 * ((C2 * C1) + (a2 * a1) + (b2 * b1)));\n\t// console.log({ΔH});\n\t// This gives the same result to 12 decimal places\n\t// except it sometimes NaNs when trying to root a negative number\n\n\t// let ΔH = Math.sqrt(H2); we never actually use the root, it gets squared again!!\n\n\t// positional corrections to the lack of uniformity of CIELAB\n\t// These are all trying to make JND ellipsoids more like spheres\n\n\t// SL Lightness crispening factor, depends entirely on L1 not L2\n\tlet SL = 0.511;\t// linear portion of the Y to L transfer function\n\tif (L1 >= 16) {\t// cubic portion\n\t\tSL = (0.040975 * L1) / (1 + 0.01765 * L1);\n\t}\n\n\t// SC Chroma factor\n\tlet SC = ((0.0638 * C1) / (1 + 0.0131 * C1)) + 0.638;\n\n\t// Cross term T for blue non-linearity\n\tlet T;\n\tif (Number.isNaN(H1)) {\n\t\tH1 = 0;\n\t}\n\n\tif (H1 >= 164 && H1 <= 345) {\n\t\tT = 0.56 + Math.abs(0.2 * Math.cos((H1 + 168) * d2r));\n\t}\n\telse {\n\t\tT = 0.36 + Math.abs(0.4 * Math.cos((H1 + 35) * d2r));\n\t}\n\t// console.log({T});\n\n\t// SH Hue factor also depends on C1,\n\tlet C4 = Math.pow(C1, 4);\n\tlet F = Math.sqrt(C4 / (C4 + 1900));\n\tlet SH = SC * ((F * T) + 1 - F);\n\n\t// Finally calculate the deltaE, term by term as root sume of squares\n\tlet dE = (ΔL / (l * SL)) ** 2;\n\tdE += (ΔC / (c * SC)) ** 2;\n\tdE += (H2 / (SH ** 2));\n\t// dE += (ΔH / SH)  ** 2;\n\treturn Math.sqrt(dE);\n\t// Yay!!!\n}\n", "import ColorSpace from \"../space.js\";\nimport XYZ_D65 from \"./xyz-d65.js\";\n\nconst Yw = 203;\t// absolute luminance of media white\n\nexport default new ColorSpace({\n// Absolute CIE XYZ, with a D65 whitepoint,\n// as used in most HDR colorspaces as a starting point.\n// SDR spaces are converted per BT.2048\n// so that diffuse, media white is 203 cd/m²\n\tid: \"xyz-abs-d65\",\n\tcssId: \"--xyz-abs-d65\",\n\tname: \"Absolute XYZ D65\",\n\tcoords: {\n\t\tx: {\n\t\t\trefRange: [0, 9504.7],\n\t\t\tname: \"Xa\",\n\t\t},\n\t\ty: {\n\t\t\trefRange: [0, 10000],\n\t\t\tname: \"Ya\",\n\t\t},\n\t\tz: {\n\t\t\trefRange: [0, 10888.3],\n\t\t\tname: \"Za\",\n\t\t},\n\t},\n\n\tbase: XYZ_D65,\n\tfromBase (XYZ) {\n\t\t// Make XYZ absolute, not relative to media white\n\t\t// Maximum luminance in PQ is 10,000 cd/m²\n\t\t// Relative XYZ has Y=1 for media white\n\t\treturn XYZ.map (v => Math.max(v * Yw, 0));\n\t},\n\ttoBase (AbsXYZ) {\n\t\t// Convert to media-white relative XYZ\n\t\treturn AbsXYZ.map(v => Math.max(v / Yw, 0));\n\t},\n});\n", "import ColorSpace from \"../space.js\";\nimport {multiplyMatrices} from \"../util.js\";\nimport XYZ_Abs_D65 from \"./xyz-abs-d65.js\";\n\nconst b = 1.15;\nconst g = 0.66;\nconst n = 2610 / (2 ** 14);\nconst ninv = (2 ** 14) / 2610;\nconst c1 = 3424 / (2 ** 12);\nconst c2 = 2413 / (2 ** 7);\nconst c3 = 2392 / (2 ** 7);\nconst p = 1.7 * 2523 / (2 ** 5);\nconst pinv = (2 ** 5) / (1.7 * 2523);\nconst d = -0.56;\nconst d0 = 1.6295499532821566E-11;\n\nconst XYZtoCone_M = [\n\t[  0.41478972, 0.579999,  0.0146480 ],\n\t[ -0.2015100,  1.120649,  0.0531008 ],\n\t[ -0.0166008,  0.264800,  0.6684799 ],\n];\n// XYZtoCone_M inverted\nconst Coneto<PERSON>Y<PERSON>_M = [\n\t[  1.9242264357876067,  -1.0047923125953657,  0.037651404030618   ],\n\t[  0.35031676209499907,  0.7264811939316552, -0.06538442294808501 ],\n\t[ -0.09098281098284752, -0.3127282905230739,  1.5227665613052603  ],\n];\nconst ConetoIab_M = [\n\t[  0.5,       0.5,       0        ],\n\t[  3.524000, -4.066708,  0.542708 ],\n\t[  0.199076,  1.096799, -1.295875 ],\n];\n// ConetoIab_M inverted\nconst IabtoCone_M = [\n\t[ 1,                   0.1386050432715393,   0.05804731615611886 ],\n\t[ 0.9999999999999999, -0.1386050432715393,  -0.05804731615611886 ],\n\t[ 0.9999999999999998, -0.09601924202631895, -0.8118918960560388  ],\n];\n\nexport default new ColorSpace({\n\tid: \"jzazbz\",\n\tname: \"Jzazbz\",\n\tcoords: {\n\t\tjz: {\n\t\t\trefRange: [0, 1],\n\t\t\tname: \"Jz\",\n\t\t},\n\t\taz: {\n\t\t\trefRange: [-0.5, 0.5],\n\t\t},\n\t\tbz: {\n\t\t\trefRange: [-0.5, 0.5],\n\t\t},\n\t},\n\n\tbase: XYZ_Abs_D65,\n\tfromBase (XYZ) {\n\t\t// First make XYZ absolute, not relative to media white\n\t\t// Maximum luminance in PQ is 10,000 cd/m²\n\t\t// Relative XYZ has Y=1 for media white\n\t\t// BT.2048 says media white Y=203 at PQ 58\n\n\t\tlet [ Xa, Ya, Za ] = XYZ;\n\n\t\t// modify X and Y\n\t\tlet Xm = (b * Xa) - ((b - 1) * Za);\n\t\tlet Ym = (g * Ya) - ((g - 1) * Xa);\n\n\t\t// move to LMS cone domain\n\t\tlet LMS = multiplyMatrices(XYZtoCone_M, [ Xm, Ym, Za ]);\n\n\t\t// PQ-encode LMS\n\t\tlet PQLMS = LMS.map (function (val) {\n\t\t\tlet num = c1 + (c2 * ((val / 10000) ** n));\n\t\t\tlet denom = 1 + (c3 * ((val / 10000) ** n));\n\n\t\t\treturn (num / denom)  ** p;\n\t\t});\n\n\t\t// almost there, calculate Iz az bz\n\t\tlet [ Iz, az, bz] = multiplyMatrices(ConetoIab_M, PQLMS);\n\t\t// console.log({Iz, az, bz});\n\n\t\tlet Jz = ((1 + d) * Iz) / (1 + (d * Iz)) - d0;\n\t\treturn [Jz, az, bz];\n\t},\n\ttoBase (Jzazbz) {\n\t\tlet [Jz, az, bz] = Jzazbz;\n\t\tlet Iz = (Jz + d0) / (1 + d - d * (Jz + d0));\n\n\t\t// bring into LMS cone domain\n\t\tlet PQLMS = multiplyMatrices(IabtoCone_M, [ Iz, az, bz ]);\n\n\t\t// convert from PQ-coded to linear-light\n\t\tlet LMS = PQLMS.map(function (val) {\n\t\t\tlet num = (c1 - (val ** pinv));\n\t\t\tlet denom = (c3 * (val ** pinv)) - c2;\n\t\t\tlet x = 10000 * ((num / denom) ** ninv);\n\n\t\t\treturn (x); \t// luminance relative to diffuse white, [0, 70 or so].\n\t\t});\n\n\t\t// modified abs XYZ\n\t\tlet [ Xm, Ym, Za ] = multiplyMatrices(ConetoXYZ_M, LMS);\n\n\t\t// restore standard D50 relative XYZ, relative to media white\n\t\tlet Xa = (Xm + ((b - 1) * Za)) / b;\n\t\tlet Ya = (Ym + ((g - 1) * Xa)) / g;\n\t\treturn [ Xa, Ya, Za ];\n\t},\n\n\tformats: {\n\t\t// https://drafts.csswg.org/css-color-hdr/#Jzazbz\n\t\t\"color\": {\n\t\t\tcoords: [\"<number> | <percentage>\", \"<number> | <percentage>[-1,1]\", \"<number> | <percentage>[-1,1]\"],\n\t\t},\n\t},\n});\n", "import ColorSpace from \"../space.js\";\nimport <PERSON><PERSON><PERSON>b<PERSON> from \"./jzazbz.js\";\nimport {constrain as constrainAngle} from \"../angles.js\";\n\nexport default new ColorSpace({\n\tid: \"jzczhz\",\n\tname: \"J<PERSON><PERSON><PERSON><PERSON><PERSON>\",\n\tcoords: {\n\t\tjz: {\n\t\t\trefRange: [0, 1],\n\t\t\tname: \"<PERSON><PERSON>\",\n\t\t},\n\t\tcz: {\n\t\t\trefRange: [0, 1],\n\t\t\tname: \"Chroma\",\n\t\t},\n\t\thz: {\n\t\t\trefRange: [0, 360],\n\t\t\ttype: \"angle\",\n\t\t\tname: \"<PERSON><PERSON>\",\n\t\t},\n\t},\n\n\tbase: Jzazbz,\n\tfromBase (jzazbz) {\n\t\t// Convert to polar form\n\t\tlet [Jz, az, bz] = jzazbz;\n\t\tlet hue;\n\t\tconst ε = 0.0002; // chromatic components much smaller than a,b\n\n\t\tif (Math.abs(az) < ε && Math.abs(bz) < ε) {\n\t\t\thue = NaN;\n\t\t}\n\t\telse {\n\t\t\thue = Math.atan2(bz, az) * 180 / Math.PI;\n\t\t}\n\n\t\treturn [\n\t\t\tJz, // Jz is still Jz\n\t\t\tMath.sqrt(az ** 2 + bz ** 2), // Chroma\n\t\t\tconstrainAngle(hue), // Hue, in degrees [0 to 360)\n\t\t];\n\t},\n\ttoBase (jzczhz) {\n\t\t// Convert from polar form\n\t\t// debugger;\n\t\treturn [\n\t\t\tjzczhz[0], // Jz is still Jz\n\t\t\tjzczhz[1] * Math.cos(jzczhz[2] * Math.PI / 180), // az\n\t\t\tjzczhz[1] * Math.sin(jzczhz[2] * Math.PI / 180),  // bz\n\t\t];\n\t},\n});\n", "import ColorSpace from \"../space.js\";\nimport {multiplyMatrices} from \"../util.js\";\nimport XYZ_Abs_D65 from \"./xyz-abs-d65.js\";\n\nconst c1 = 3424 / 4096;\nconst c2 = 2413 / 128;\nconst c3 = 2392 / 128;\nconst m1 = 2610 / 16384;\nconst m2 = 2523 / 32;\nconst im1 = 16384 / 2610;\nconst im2 = 32 / 2523;\n\n// The matrix below includes the 4% crosstalk components\n// and is from the Dolby \"What is ICtCp\" paper\"\nconst XYZtoLMS_M = [\n\t[  0.3592832590121217,  0.6976051147779502, -0.0358915932320290 ],\n\t[ -0.1920808463704993,  1.1004767970374321,  0.0753748658519118 ],\n\t[  0.0070797844607479,  0.0748396662186362,  0.8433265453898765 ],\n];\n// linear-light Rec.2020 to LMS, again with crosstalk\n// rational terms from <PERSON>,\n// Encoding High Dynamic Range andWide Color Gamut Imagery, p.97\n// and ITU-R BT.2124-0 p.2\n/*\nconst Rec2020toLMS_M = [\n\t[ 1688 / 4096,  2146 / 4096,   262 / 4096 ],\n\t[  683 / 4096,  2951 / 4096,   462 / 4096 ],\n\t[   99 / 4096,   309 / 4096,  3688 / 4096 ]\n];\n*/\n// this includes the Ebner LMS coefficients,\n// the rotation, and the scaling to [-0.5,0.5] range\n// rational terms from Fröhlich p.97\n// and ITU-R BT.2124-0 pp.2-3\nconst LMStoIPT_M = [\n\t[  2048 / 4096,   2048 / 4096,       0      ],\n\t[  6610 / 4096, -13613 / 4096,  7003 / 4096 ],\n\t[ 17933 / 4096, -17390 / 4096,  -543 / 4096 ],\n];\n\n// inverted matrices, calculated from the above\nconst IPTtoLMS_M = [\n\t[ 0.9999999999999998,  0.0086090370379328,  0.1110296250030260 ],\n\t[ 0.9999999999999998, -0.0086090370379328, -0.1110296250030259 ],\n\t[ 0.9999999999999998,  0.5600313357106791, -0.3206271749873188 ],\n];\n/*\nconst LMStoRec2020_M = [\n\t[ 3.4375568932814012112,   -2.5072112125095058195,   0.069654319228104608382],\n\t[-0.79142868665644156125,   1.9838372198740089874,  -0.19240853321756742626 ],\n\t[-0.025646662911506476363, -0.099240248643945566751, 1.1248869115554520431  ]\n];\n*/\nconst LMStoXYZ_M = [\n\t[  2.0701522183894223, -1.3263473389671563,  0.2066510476294053 ],\n\t[  0.3647385209748072,  0.6805660249472273, -0.0453045459220347 ],\n\t[ -0.0497472075358123, -0.0492609666966131,  1.1880659249923042 ],\n];\n\n// Only the PQ form of ICtCp is implemented here. There is also an HLG form.\n// from Dolby, \"WHAT IS ICTCP?\"\n// https://professional.dolby.com/siteassets/pdfs/ictcp_dolbywhitepaper_v071.pdf\n// and\n// Dolby, \"Perceptual Color Volume\n// Measuring the Distinguishable Colors of HDR and WCG Displays\"\n// https://professional.dolby.com/siteassets/pdfs/dolby-vision-measuring-perceptual-color-volume-v7.1.pdf\nexport default new ColorSpace({\n\tid: \"ictcp\",\n\tname: \"ICTCP\",\n\t// From BT.2100-2 page 7:\n\t// During production, signal values are expected to exceed the\n\t// range E′ = [0.0 : 1.0]. This provides processing headroom and avoids\n\t// signal degradation during cascaded processing. Such values of E′,\n\t// below 0.0 or exceeding 1.0, should not be clipped during production\n\t// and exchange.\n\t// Values below 0.0 should not be clipped in reference displays (even\n\t// though they represent “negative” light) to allow the black level of\n\t// the signal (LB) to be properly set using test signals known as “PLUGE”\n\tcoords: {\n\t\ti: {\n\t\t\trefRange: [0, 1],\t// Constant luminance,\n\t\t\tname: \"I\",\n\t\t},\n\t\tct: {\n\t\t\trefRange: [-0.5, 0.5],\t// Full BT.2020 gamut in range [-0.5, 0.5]\n\t\t\tname: \"CT\",\n\t\t},\n\t\tcp: {\n\t\t\trefRange: [-0.5, 0.5],\n\t\t\tname: \"CP\",\n\t\t},\n\t},\n\n\tbase: XYZ_Abs_D65,\n\tfromBase (XYZ) {\n\t\t// move to LMS cone domain\n\t\tlet LMS = multiplyMatrices(XYZtoLMS_M, XYZ);\n\n\t\treturn LMStoICtCp(LMS);\n\t},\n\ttoBase (ICtCp) {\n\t\tlet LMS = ICtCptoLMS(ICtCp);\n\n\t\treturn multiplyMatrices(LMStoXYZ_M, LMS);\n\t},\n});\n\nfunction LMStoICtCp (LMS) {\n\t// apply the PQ EOTF\n\t// we can't ever be dividing by zero because of the \"1 +\" in the denominator\n\tlet PQLMS = LMS.map (function (val) {\n\t\tlet num = c1 + (c2 * ((val / 10000) ** m1));\n\t\tlet denom = 1 + (c3 * ((val / 10000) ** m1));\n\n\t\treturn (num / denom)  ** m2;\n\t});\n\n\t// LMS to IPT, with rotation for Y'C'bC'r compatibility\n\treturn multiplyMatrices(LMStoIPT_M, PQLMS);\n}\n\nfunction ICtCptoLMS (ICtCp) {\n\tlet PQLMS = multiplyMatrices(IPTtoLMS_M, ICtCp);\n\n\t// From BT.2124-0 Annex 2 Conversion 3\n\tlet LMS = PQLMS.map (function (val) {\n\t\tlet num  = Math.max((val ** im2) - c1, 0);\n\t\tlet denom = (c2 - (c3 * (val ** im2)));\n\t\treturn 10000 * ((num / denom) ** im1);\n\t});\n\n\treturn LMS;\n}\n", "import ColorSpace from \"../space.js\";\nimport {multiplyMatrices, interpolate, copySign, spow, zdiv, bisectLeft} from \"../util.js\";\nimport {constrain} from \"../angles.js\";\nimport xyz_d65 from \"./xyz-d65.js\";\nimport {WHITES} from \"../adapt.js\";\n\nconst white = WHITES.D65;\nconst adaptedCoef = 0.42;\nconst adaptedCoefInv = 1 / adaptedCoef;\nconst tau = 2 * Math.PI;\n\nconst cat16 = [\n\t[  0.401288,  0.650173, -0.051461 ],\n\t[ -0.250268,  1.204414,  0.045854 ],\n\t[ -0.002079,  0.048952,  0.953127 ],\n];\n\nconst cat16Inv = [\n\t[1.8620678550872327, -1.0112546305316843, 0.14918677544445175],\n\t[0.38752654323613717, 0.6214474419314753, -0.008973985167612518],\n\t[-0.015841498849333856, -0.03412293802851557, 1.0499644368778496],\n];\n\nconst m1 = [\n\t[460.0, 451.0, 288.0],\n\t[460.0, -891.0, -261.0],\n\t[460.0, -220.0, -6300.0],\n];\n\nconst surroundMap = {\n\tdark: [0.8, 0.525, 0.8],\n\tdim: [0.9, 0.59, 0.9],\n\taverage: [1, 0.69, 1],\n};\n\nconst hueQuadMap = {\n\t// Red, Yellow, Green, Blue, Red\n\th: [20.14, 90.00, 164.25, 237.53, 380.14],\n\te: [0.8, 0.7, 1.0, 1.2, 0.8],\n\tH: [0.0, 100.0, 200.0, 300.0, 400.0],\n};\n\nconst rad2deg = 180 / Math.PI;\nconst deg2rad = Math.PI / 180;\n\nexport function adapt (coords, fl) {\n\tconst temp = coords.map(c => {\n\t\tconst x = spow(fl * Math.abs(c) * 0.01, adaptedCoef);\n\t\treturn 400 * copySign(x, c) / (x + 27.13);\n\t});\n\treturn temp;\n}\n\nexport function unadapt (adapted, fl) {\n\tconst constant = 100 / fl * (27.13 ** adaptedCoefInv);\n\treturn adapted.map(c => {\n\t\tconst cabs = Math.abs(c);\n\t\treturn copySign(constant * spow(cabs / (400 - cabs), adaptedCoefInv), c);\n\t});\n}\n\nexport function hueQuadrature (h) {\n\tlet hp = constrain(h);\n\tif (hp <= hueQuadMap.h[0]) {\n\t\thp += 360;\n\t}\n\n\tconst i = bisectLeft(hueQuadMap.h, hp) - 1;\n\tconst [hi, hii] = hueQuadMap.h.slice(i, i + 2);\n\tconst [ei, eii] = hueQuadMap.e.slice(i, i + 2);\n\tconst Hi = hueQuadMap.H[i];\n\n\tconst t = (hp - hi) / ei;\n\treturn Hi + (100 * t) / (t + (hii - hp) / eii);\n}\n\nexport function invHueQuadrature (H) {\n\tlet Hp = ((H % 400 + 400) % 400);\n\tconst i = Math.floor(0.01 * Hp);\n\tHp = Hp % 100;\n\tconst [hi, hii] = hueQuadMap.h.slice(i, i + 2);\n\tconst [ei, eii] = hueQuadMap.e.slice(i, i + 2);\n\n\treturn constrain(\n\t\t(Hp * (eii * hi - ei * hii) - 100 * hi * eii) /\n\t\t(Hp * (eii - ei) - 100 * eii),\n\t);\n}\n\nexport function environment (\n\trefWhite,\n\tadaptingLuminance,\n\tbackgroundLuminance,\n\tsurround,\n\tdiscounting,\n) {\n\n\tconst env = {};\n\n\tenv.discounting = discounting;\n\tenv.refWhite = refWhite;\n\tenv.surround = surround;\n\tconst xyzW = refWhite.map(c => {\n\t\treturn c * 100;\n\t});\n\n\t// The average luminance of the environment in `cd/m^2cd/m` (a.k.a. nits)\n\tenv.la = adaptingLuminance;\n\t// The relative luminance of the nearby background\n\tenv.yb = backgroundLuminance;\n\t// Absolute luminance of the reference white.\n\tconst yw = xyzW[1];\n\n\t// Cone response for reference white\n\tconst rgbW = multiplyMatrices(cat16, xyzW);\n\n\t// Surround: dark, dim, and average\n\tsurround = surroundMap[env.surround];\n\tconst f = surround[0];\n\tenv.c = surround[1];\n\tenv.nc = surround[2];\n\n\tconst k = 1 / (5 * env.la + 1);\n\tconst k4 = k ** 4;\n\n\t// Factor of luminance level adaptation\n\tenv.fl = (k4 * env.la + 0.1 * (1 - k4) * (1 - k4) * Math.cbrt(5 * env.la));\n\tenv.flRoot = env.fl ** 0.25;\n\n\tenv.n = env.yb / yw;\n\tenv.z = 1.48 + Math.sqrt(env.n);\n\tenv.nbb = 0.725 * (env.n ** -0.2);\n\tenv.ncb = env.nbb;\n\n\t// Degree of adaptation calculating if not discounting\n\t// illuminant (assumed eye is fully adapted)\n\tconst d = (discounting) ?\n\t\t1 :\n\t\tMath.max(\n\t\t\tMath.min(f * (1 - 1 / 3.6 * Math.exp((-env.la - 42) / 92)), 1),\n\t\t\t0,\n\t\t);\n\tenv.dRgb = rgbW.map(c => {\n\t\treturn interpolate(1, yw / c, d);\n\t});\n\tenv.dRgbInv = env.dRgb.map(c => {\n\t\treturn 1 / c;\n\t});\n\n\t// Achromatic response\n\tconst rgbCW = rgbW.map((c, i) => {\n\t\treturn c * env.dRgb[i];\n\t});\n\tconst rgbAW = adapt(rgbCW, env.fl);\n\tenv.aW = env.nbb * (2 * rgbAW[0] + rgbAW[1] + 0.05 * rgbAW[2]);\n\n\t// console.log(env);\n\n\treturn env;\n}\n\n// Pre-calculate everything we can with the viewing conditions\nconst viewingConditions = environment(\n\twhite,\n\t64 / Math.PI * 0.2, 20,\n\t\"average\",\n\tfalse,\n);\n\nexport function fromCam16 (cam16, env) {\n\n\t// These check ensure one, and only one attribute for a\n\t// given category is provided.\n\tif (!((cam16.J !== undefined) ^ (cam16.Q !== undefined))) {\n\t\tthrow new Error(\"Conversion requires one and only one: 'J' or 'Q'\");\n\t}\n\n\tif (!((cam16.C !== undefined) ^ (cam16.M !== undefined) ^ (cam16.s !== undefined))) {\n\t\tthrow new Error(\"Conversion requires one and only one: 'C', 'M' or 's'\");\n\t}\n\n\t// Hue is absolutely required\n\tif (!((cam16.h !== undefined) ^ (cam16.H !== undefined))) {\n\t\tthrow new Error(\"Conversion requires one and only one: 'h' or 'H'\");\n\t}\n\n\t// Black\n\tif (cam16.J === 0.0 || cam16.Q === 0.0) {\n\t\treturn [0.0, 0.0, 0.0];\n\t}\n\n\t// Break hue into Cartesian components\n\tlet hRad = 0.0;\n\tif (cam16.h !== undefined) {\n\t\thRad = constrain(cam16.h) * deg2rad;\n\t}\n\telse {\n\t\thRad = invHueQuadrature(cam16.H) * deg2rad;\n\t}\n\n\tconst cosh = Math.cos(hRad);\n\tconst sinh = Math.sin(hRad);\n\n\t// Calculate `Jroot` from one of the lightness derived coordinates.\n\tlet Jroot = 0.0;\n\tif (cam16.J !== undefined) {\n\t\tJroot = spow(cam16.J, 1 / 2) * 0.1;\n\t}\n\telse if (cam16.Q !== undefined) {\n\t\tJroot = 0.25 * env.c * cam16.Q / ((env.aW + 4) * env.flRoot);\n\t}\n\n\t// Calculate the `t` value from one of the chroma derived coordinates\n\tlet alpha = 0.0;\n\tif (cam16.C !== undefined) {\n\t\talpha = cam16.C / Jroot;\n\t}\n\telse if (cam16.M !== undefined) {\n\t\talpha = (cam16.M / env.flRoot) / Jroot;\n\t}\n\telse if (cam16.s !== undefined) {\n\t\talpha = 0.0004 * (cam16.s ** 2) * (env.aW + 4) / env.c;\n\t}\n\tconst t = spow(\n\t\talpha * Math.pow(1.64 - Math.pow(0.29, env.n), -0.73),\n\t\t10 / 9,\n\t);\n\n\t// Eccentricity\n\tconst et = 0.25 * (Math.cos(hRad + 2) + 3.8);\n\n\t// Achromatic response\n\tconst A = env.aW * spow(Jroot, 2 / env.c / env.z);\n\n\t// Calculate red-green and yellow-blue components\n\tconst p1 = 5e4 / 13 * env.nc * env.ncb * et;\n\tconst p2 = A / env.nbb;\n\tconst r = (\n\t\t23 * (p2 + 0.305) *\n\t\tzdiv(t, 23 * p1 + t * (11 * cosh + 108 * sinh))\n\t);\n\tconst a = r * cosh;\n\tconst b = r * sinh;\n\n\t// Calculate back from cone response to XYZ\n\tconst rgb_c = unadapt(\n\t\tmultiplyMatrices(m1, [p2, a, b]).map(c => {\n\t\t\treturn c * 1 / 1403;\n\t\t}),\n\t\tenv.fl,\n\t);\n\treturn multiplyMatrices(\n\t\tcat16Inv,\n\t\trgb_c.map((c, i) => {\n\t\t\treturn c * env.dRgbInv[i];\n\t\t}),\n\t).map(c => {\n\t\treturn c / 100;\n\t});\n}\n\n\nexport function toCam16 (xyzd65, env) {\n\t// Cone response\n\tconst xyz100 = xyzd65.map(c => {\n\t\treturn c * 100;\n\t});\n\tconst rgbA = adapt(\n\t\tmultiplyMatrices(cat16, xyz100).map((c, i) => {\n\t\t\treturn c * env.dRgb[i];\n\t\t}),\n\t\tenv.fl,\n\t);\n\n\t// Calculate hue from red-green and yellow-blue components\n\tconst a = rgbA[0] + (-12 * rgbA[1] + rgbA[2]) / 11;\n\tconst b = (rgbA[0] + rgbA[1] - 2 * rgbA[2]) / 9;\n\tconst hRad = ((Math.atan2(b, a) % tau) + tau) % tau;\n\n\t// Eccentricity\n\tconst et = 0.25 * (Math.cos(hRad + 2) + 3.8);\n\n\tconst t = (\n\t\t5e4 / 13 * env.nc * env.ncb *\n\t\tzdiv(\n\t\t\tet * Math.sqrt(a ** 2 + b ** 2),\n\t\t\trgbA[0] + rgbA[1] + 1.05 * rgbA[2] + 0.305,\n\t\t)\n\t);\n\tconst alpha = spow(t, 0.9) * Math.pow(1.64 - Math.pow(0.29, env.n), 0.73);\n\n\t// Achromatic response\n\tconst A = env.nbb * (2 * rgbA[0] + rgbA[1] + 0.05 * rgbA[2]);\n\n\tconst Jroot = spow(A / env.aW, 0.5 * env.c * env.z);\n\n\t// Lightness\n\tconst J = 100 * spow(Jroot, 2);\n\n\t// Brightness\n\tconst Q = (4 / env.c * Jroot * (env.aW + 4) * env.flRoot);\n\n\t// Chroma\n\tconst C = alpha * Jroot;\n\n\t// Colorfulness\n\tconst M = C * env.flRoot;\n\n\t// Hue\n\tconst h = constrain(hRad * rad2deg);\n\n\t// Hue quadrature\n\tconst H = hueQuadrature(h);\n\n\t// Saturation\n\tconst s = 50 * spow(env.c * alpha / (env.aW + 4), 1 / 2);\n\n\t// console.log({J: J, C: C, h: h, s: s, Q: Q, M: M, H: H});\n\n\treturn {J: J, C: C, h: h, s: s, Q: Q, M: M, H: H};\n}\n\n\n// Provided as a way to directly evaluate the CAM16 model\n// https://observablehq.com/@jrus/cam16: reference implementation\n// https://arxiv.org/pdf/1802.06067.pdf: Nico Schlömer\n// https://onlinelibrary.wiley.com/doi/pdf/10.1002/col.22324: hue quadrature\n// https://www.researchgate.net/publication/318152296_Comprehensive_color_solutions_CAM16_CAT16_and_CAM16-UCS\n// Results compared against: https://github.com/colour-science/colour\nexport default new ColorSpace({\n\tid: \"cam16-jmh\",\n\tcssId: \"--cam16-jmh\",\n\tname: \"CAM16-JMh\",\n\tcoords: {\n\t\tj: {\n\t\t\trefRange: [0, 100],\n\t\t\tname: \"J\",\n\t\t},\n\t\tm: {\n\t\t\trefRange: [0, 105.0],\n\t\t\tname: \"Colorfulness\",\n\t\t},\n\t\th: {\n\t\t\trefRange: [0, 360],\n\t\t\ttype: \"angle\",\n\t\t\tname: \"Hue\",\n\t\t},\n\t},\n\n\tbase: xyz_d65,\n\n\tfromBase (xyz) {\n\t\tconst cam16 = toCam16(xyz, viewingConditions);\n\t\treturn [cam16.J, cam16.M, cam16.h];\n\t},\n\ttoBase (cam16) {\n\t\treturn fromCam16(\n\t\t\t{J: cam16[0], M: cam16[1], h: cam16[2]},\n\t\t\tviewingConditions,\n\t\t);\n\t},\n});\n", "import ColorSpace from \"../space.js\";\nimport {constrain} from \"../angles.js\";\nimport xyz_d65 from \"./xyz-d65.js\";\nimport {fromCam16, toCam16, environment} from \"./cam16.js\";\nimport {WHITES} from \"../adapt.js\";\n\nconst white = WHITES.D65;\nconst ε = 216 / 24389;  // 6^3/29^3 == (24/116)^3\nconst κ = 24389 / 27;   // 29^3/3^3\n\nfunction toLstar (y) {\n\t// Convert XYZ Y to L*\n\n\tconst fy = (y > ε) ? Math.cbrt(y) : (κ * y + 16) / 116;\n\treturn (116.0 * fy) - 16.0;\n}\n\nfunction fromLstar (lstar) {\n\t// Convert L* back to XYZ Y\n\n\treturn (lstar > 8) ?  Math.pow((lstar + 16) / 116, 3) : lstar / κ;\n}\n\nfunction fromHct (coords, env) {\n\t// Use Newton's method to try and converge as quick as possible or\n\t// converge as close as we can. While the requested precision is achieved\n\t// most of the time, it may not always be achievable. Especially past the\n\t// visible spectrum, the algorithm will likely struggle to get the same\n\t// precision. If, for whatever reason, we cannot achieve the accuracy we\n\t// seek in the allotted iterations, just return the closest we were able to\n\t// get.\n\n\tlet [h, c, t] = coords;\n\tlet xyz = [];\n\tlet j = 0;\n\n\t// Shortcut out for black\n\tif (t === 0) {\n\t\treturn [0.0, 0.0, 0.0];\n\t}\n\n\t// Calculate the Y we need to target\n\tlet y = fromLstar(t);\n\n\t// A better initial guess yields better results. Polynomials come from\n\t// curve fitting the T vs J response.\n\tif (t > 0) {\n\t\tj = 0.00379058511492914 * t ** 2 + 0.608983189401032 * t + 0.9155088574762233;\n\t}\n\telse {\n\t\tj = 9.514440756550361e-06 * t ** 2 + 0.08693057439788597 * t - 21.928975842194614;\n\t}\n\n\t// Threshold of how close is close enough, and max number of attempts.\n\t// More precision and more attempts means more time spent iterating. Higher\n\t// required precision gives more accuracy but also increases the chance of\n\t// not hitting the goal. 2e-12 allows us to convert round trip with\n\t// reasonable accuracy of six decimal places or more.\n\tconst threshold = 2e-12;\n\tconst max_attempts = 15;\n\n\tlet attempt = 0;\n\tlet last = Infinity;\n\tlet best = j;\n\n\t// Try to find a J such that the returned y matches the returned y of the L*\n\twhile (attempt <= max_attempts) {\n\t\txyz = fromCam16({J: j, C: c, h: h}, env);\n\n\t\t// If we are within range, return XYZ\n\t\t// If we are closer than last time, save the values\n\t\tconst delta = Math.abs(xyz[1] - y);\n\t\tif (delta < last) {\n\t\t\tif (delta <= threshold) {\n\t\t\t\treturn xyz;\n\t\t\t}\n\t\t\tbest = j;\n\t\t\tlast = delta;\n\t\t}\n\n\t\t// f(j_root) = (j ** (1 / 2)) * 0.1\n\t\t// f(j) = ((f(j_root) * 100) ** 2) / j - 1 = 0\n\t\t// f(j_root) = Y = y / 100\n\t\t// f(j) = (y ** 2) / j - 1\n\t\t// f'(j) = (2 * y) / j\n\t\tj = j - (xyz[1] - y) * j / (2 * xyz[1]);\n\n\t\tattempt += 1;\n\t}\n\n\t// We could not acquire the precision we desired,\n\t// return our closest attempt.\n\treturn fromCam16({J: j, C: c, h: h}, env);\n}\n\nfunction toHct (xyz, env) {\n\t// Calculate HCT by taking the L* of CIE LCh D65 and CAM16 chroma and hue.\n\n\tconst t = toLstar(xyz[1]);\n\tif (t === 0.0) {\n\t\treturn [0.0, 0.0, 0.0];\n\t}\n\tconst cam16 = toCam16(xyz, viewingConditions);\n\treturn [constrain(cam16.h), cam16.C, t];\n}\n\n// Pre-calculate everything we can with the viewing conditions\nexport const viewingConditions = environment(\n\twhite, 200 / Math.PI * fromLstar(50.0),\n\tfromLstar(50.0) * 100,\n\t\"average\",\n\tfalse,\n);\n\n// https://material.io/blog/science-of-color-design\n// This is not a port of the material-color-utilities,\n// but instead implements the full color space as described,\n// combining CAM16 JCh and Lab D65. This does not clamp conversion\n// to HCT to specific chroma bands and provides support for wider\n// gamuts than Google currently supports and does so at a greater\n// precision (> 8 bits back to sRGB).\n// This implementation comes from https://github.com/facelessuser/coloraide\n// which is licensed under MIT.\nexport default new ColorSpace({\n\tid: \"hct\",\n\tname: \"HCT\",\n\tcoords: {\n\t\th: {\n\t\t\trefRange: [0, 360],\n\t\t\ttype: \"angle\",\n\t\t\tname: \"Hue\",\n\t\t},\n\t\tc: {\n\t\t\trefRange: [0, 145],\n\t\t\tname: \"Colorfulness\",\n\t\t},\n\t\tt: {\n\t\t\trefRange: [0, 100],\n\t\t\tname: \"Tone\",\n\t\t},\n\t},\n\n\tbase: xyz_d65,\n\n\tfromBase (xyz) {\n\t\treturn toHct(xyz, viewingConditions);\n\t},\n\ttoBase (hct) {\n\t\treturn fromHct(hct, viewingConditions);\n\t},\n\tformats: {\n\t\tcolor: {\n\t\t\tid: \"--hct\",\n\t\t\tcoords: [\"<number> | <angle>\", \"<percentage> | <number>\", \"<percentage> | <number>\"],\n\t\t},\n\t},\n});\n", "import hct from \"../spaces/hct.js\";\nimport {viewingConditions} from \"../spaces/hct.js\";\nimport getColor from \"../getColor.js\";\n\nconst rad2deg = 180 / Math.PI;\nconst deg2rad = Math.PI / 180;\nconst ucsCoeff = [1.00, 0.007, 0.0228];\n\n/**\n* Convert HCT chroma and hue (CAM16 JMh colorfulness and hue) using UCS logic for a and b.\n* @param {number[]} coords - HCT coordinates.\n* @return {number[]}\n*/\nfunction convertUcsAb (coords) {\n\t// We want the distance between the actual color.\n\t// If chroma is negative, it will throw off our calculations.\n\t// Normally, converting back to the base and forward will correct it.\n\t// If we have a negative chroma after this, then we have a color that\n\t// cannot resolve to positive chroma.\n\tif (coords[1] < 0) {\n\t\tcoords = hct.fromBase(hct.toBase(coords));\n\t}\n\n\t// Only in extreme cases (usually outside the visible spectrum)\n\t// can the input value for log become negative.\n\t// Avoid domain error by forcing a zero result via \"max\" if necessary.\n\tconst M = Math.log(Math.max(1 + ucsCoeff[2] * coords[1] * viewingConditions.flRoot, 1.0)) / ucsCoeff[2];\n\tconst hrad = coords[0] * deg2rad;\n\tconst a = M * Math.cos(hrad);\n\tconst b = M * Math.sin(hrad);\n\n\treturn [coords[2], a, b];\n}\n\n\n/**\n* Color distance using HCT.\n* @param {Color} color - Color to compare.\n* @param {Color} sample - Color to compare.\n* @return {number[]}\n*/\nexport default function (color, sample) {\n\t[color, sample] = getColor([color, sample]);\n\n\tlet [ t1, a1, b1 ] = convertUcsAb(hct.from(color));\n\tlet [ t2, a2, b2 ] = convertUcsAb(hct.from(sample));\n\n\t// Use simple euclidean distance with a and b using UCS conversion\n\t// and LCh lightness (HCT tone).\n\treturn Math.sqrt((t1 - t2) ** 2 + (a1 - a2) ** 2 + (b1 - b2) ** 2);\n}\n", "import deltaE76 from \"./deltaE76.js\";\nimport deltaECMC from \"./deltaECMC.js\";\nimport deltaE2000 from \"./deltaE2000.js\";\nimport deltaEJz from \"./deltaEJz.js\";\nimport deltaEITP from \"./deltaEITP.js\";\nimport deltaE<PERSON> from \"./deltaEOK.js\";\nimport deltaEHCT from \"./deltaEHCT.js\";\n\nexport {\n\tdeltaE76,\n\tdeltaECMC,\n\tdeltaE2000,\n\tdeltaEJz,\n\tdeltaEITP,\n\tdeltaEOK,\n\tdeltaEHCT,\n};\n\nexport default {\n\tdeltaE76,\n\tdeltaECMC,\n\tdeltaE2000,\n\tdeltaEJz,\n\tdeltaEITP,\n\tdeltaEOK,\n\tdeltaEHCT,\n};\n", "import distance from \"../distance.js\";\nimport getColor from \"../getColor.js\";\n\nexport default function deltaE76 (color, sample) {\n\t// Assume getColor() is called in the distance function\n\treturn distance(color, sample, \"lab\");\n}\n", "import jzczhz from \"../spaces/jzczhz.js\";\nimport getColor from \"../getColor.js\";\n\n// More accurate color-difference formulae\n// than the simple 1976 Euclidean distance in Lab\n\n// Uses JzCzHz, which has improved perceptual uniformity\n// and thus a simple Euclidean root-sum of ΔL² ΔC² ΔH²\n// gives good results.\n\nexport default function (color, sample) {\n\t[color, sample] = getColor([color, sample]);\n\n\t// Given this color as the reference\n\t// and a sample,\n\t// calculate deltaE in JzCzHz.\n\tlet [Jz1, Cz1, Hz1] = jzczhz.from(color);\n\tlet [Jz2, Cz2, Hz2] = jzczhz.from(sample);\n\n\t// Lightness and Chroma differences\n\t// sign does not matter as they are squared.\n\tlet ΔJ = Jz1 - Jz2;\n\tlet ΔC = Cz1 - Cz2;\n\n\t// length of chord for ΔH\n\tif ((Number.isNaN(Hz1)) && (Number.isNaN(Hz2))) {\n\t\t// both undefined hues\n\t\tHz1 = 0;\n\t\tHz2 = 0;\n\t}\n\telse if (Number.isNaN(Hz1)) {\n\t\t// one undefined, set to the defined hue\n\t\tHz1 = Hz2;\n\t}\n\telse if (Number.isNaN(Hz2)) {\n\t\tHz2 = Hz1;\n\t}\n\n\tlet Δh = Hz1 - Hz2;\n\tlet ΔH = 2 * Math.sqrt(Cz1 * Cz2) * Math.sin((Δh / 2) * (Math.PI / 180));\n\n\treturn Math.sqrt(ΔJ ** 2 + ΔC ** 2 + ΔH ** 2);\n}\n", "import ictcp from \"../spaces/ictcp.js\";\nimport getColor from \"../getColor.js\";\n\n// Delta E in ICtCp space,\n// which the ITU calls Delta E ITP, which is shorter\n// formulae from ITU Rec. ITU-R BT.2124-0\n\nexport default function (color, sample) {\n\t[color, sample] = getColor([color, sample]);\n\n\t// Given this color as the reference\n\t// and a sample,\n\t// calculate deltaE in ICtCp\n\t// which is simply the Euclidean distance\n\n\tlet [ I1, T1, P1 ] = ictcp.from(color);\n\tlet [ I2, T2, P2 ] = ictcp.from(sample);\n\n\t// the 0.25 factor is to undo the encoding scaling in Ct\n\t// the 720 is so that 1 deltaE = 1 JND\n\t// per  ITU-R BT.2124-0 p.3\n\n\treturn 720 * Math.sqrt((I1 - I2) ** 2 + (0.25 * (T1 - T2) ** 2) + (P1 - P2) ** 2);\n}\n", "import * as util from \"./util.js\";\nimport ColorSpace from \"./space.js\";\nimport defaults from \"./defaults.js\";\nimport deltaE2000 from \"./deltaE/deltaE2000.js\";\nimport deltaEOK from \"./deltaE/deltaEOK.js\";\nimport inGamut from \"./inGamut.js\";\nimport to from \"./to.js\";\nimport get from \"./get.js\";\nimport oklab from \"./spaces/oklab.js\";\nimport set from \"./set.js\";\nimport clone from \"./clone.js\";\nimport getColor from \"./getColor.js\";\nimport deltaEMethods from \"./deltaE/index.js\";\nimport {WHITES} from \"./adapt.js\";\n\n/**\n * Calculate the epsilon to 2 degrees smaller than the specified JND.\n * @param {Number} jnd - The target \"just noticeable difference\".\n * @returns {Number}\n */\nfunction calcEpsilon (jnd) {\n\t// Calculate the epsilon to 2 degrees smaller than the specified JND.\n\n\tconst order = (!jnd) ? 0 : Math.floor(Math.log10(Math.abs(jnd)));\n\t// Limit to an arbitrary value to ensure value is never too small and causes infinite loops.\n\treturn Math.max(parseFloat(`1e${order - 2}`), 1e-6);\n}\n\nconst GMAPPRESET = {\n\t\"hct\": {\n\t\tmethod: \"hct.c\",\n\t\tjnd: 2,\n\t\tdeltaEMethod: \"hct\",\n\t\tblackWhiteClamp: {},\n\t},\n\t\"hct-tonal\": {\n\t\tmethod: \"hct.c\",\n\t\tjnd: 0,\n\t\tdeltaEMethod: \"hct\",\n\t\tblackWhiteClamp: { channel: \"hct.t\", min: 0, max: 100 },\n\t},\n};\n\n/**\n * Force coordinates to be in gamut of a certain color space.\n * Mutates the color it is passed.\n * @param {Object|string} options object or spaceId string\n * @param {string} options.method - How to force into gamut.\n *        If \"clip\", coordinates are just clipped to their reference range.\n *        If \"css\", coordinates are reduced according to the CSS 4 Gamut Mapping Algorithm.\n *        If in the form [colorSpaceId].[coordName], that coordinate is reduced\n *        until the color is in gamut. Please note that this may produce nonsensical\n *        results for certain coordinates (e.g. hue) or infinite loops if reducing the coordinate never brings the color in gamut.\n * @param {ColorSpace|string} options.space - The space whose gamut we want to map to\n * @param {string} options.deltaEMethod - The delta E method to use while performing gamut mapping.\n *        If no method is specified, delta E 2000 is used.\n * @param {Number} options.jnd - The \"just noticeable difference\" to target.\n * @param {Object} options.blackWhiteClamp - Used to configure SDR black and clamping.\n *        \"channel\" indicates the \"space.channel\" to use for determining when to clamp.\n *        \"min\" indicates the lower limit for black clamping and \"max\" indicates the upper\n *        limit for white clamping.\n */\n\nexport default function toGamut (\n\tcolor,\n\t{\n\t\tmethod = defaults.gamut_mapping,\n\t\tspace = undefined,\n\t\tdeltaEMethod = \"\",\n\t\tjnd = 2,\n\t\tblackWhiteClamp = {},\n\t} = {},\n) {\n\tcolor = getColor(color);\n\n\tif (util.isString(arguments[1])) {\n\t\tspace = arguments[1];\n\t}\n\telse if (!space) {\n\t\tspace = color.space;\n\t}\n\n\tspace = ColorSpace.get(space);\n\n\t// 3 spaces:\n\t// color.space: current color space\n\t// space: space whose gamut we are mapping to\n\t// mapSpace: space with the coord we're reducing\n\n\tif (inGamut(color, space, { epsilon: 0 })) {\n\t\treturn color;\n\t}\n\n\tlet spaceColor;\n\tif (method === \"css\") {\n\t\tspaceColor = toGamutCSS(color, { space });\n\t}\n\telse {\n\t\tif (method !== \"clip\" && !inGamut(color, space)) {\n\n\t\t\tif (Object.prototype.hasOwnProperty.call(GMAPPRESET, method)) {\n\t\t\t\t({method, jnd, deltaEMethod, blackWhiteClamp} = GMAPPRESET[method]);\n\t\t\t}\n\n\t\t\t// Get the correct delta E method\n\t\t\tlet de = deltaE2000;\n\t\t\tif (deltaEMethod !== \"\") {\n\t\t\t\tfor (let m in deltaEMethods) {\n\t\t\t\t\tif (\"deltae\" + deltaEMethod.toLowerCase() === m.toLowerCase()) {\n\t\t\t\t\t\tde = deltaEMethods[m];\n\t\t\t\t\t\tbreak;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tlet clipped = toGamut(to(color, space), { method: \"clip\", space });\n\t\t\tif (de(color, clipped) > jnd) {\n\n\t\t\t\t// Clamp to SDR white and black if required\n\t\t\t\tif (Object.keys(blackWhiteClamp).length === 3) {\n\t\t\t\t\tlet channelMeta = ColorSpace.resolveCoord(blackWhiteClamp.channel);\n\t\t\t\t\tlet channel = get(to(color, channelMeta.space), channelMeta.id);\n\t\t\t\t\tif (util.isNone(channel)) {\n\t\t\t\t\t\tchannel = 0;\n\t\t\t\t\t}\n\t\t\t\t\tif (channel >= blackWhiteClamp.max) {\n\t\t\t\t\t\treturn to({ space: \"xyz-d65\", coords: WHITES[\"D65\"] }, color.space);\n\t\t\t\t\t}\n\t\t\t\t\telse if (channel <= blackWhiteClamp.min) {\n\t\t\t\t\t\treturn to({ space: \"xyz-d65\", coords: [0, 0, 0] }, color.space);\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t// Reduce a coordinate of a certain color space until the color is in gamut\n\t\t\t\tlet coordMeta = ColorSpace.resolveCoord(method);\n\t\t\t\tlet mapSpace = coordMeta.space;\n\t\t\t\tlet coordId = coordMeta.id;\n\n\t\t\t\tlet mappedColor = to(color, mapSpace);\n\t\t\t\t// If we were already in the mapped color space, we need to resolve undefined channels\n\t\t\t\tmappedColor.coords.forEach((c, i) => {\n\t\t\t\t\tif (util.isNone(c)) {\n\t\t\t\t\t\tmappedColor.coords[i] = 0;\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t\tlet bounds = coordMeta.range || coordMeta.refRange;\n\t\t\t\tlet min = bounds[0];\n\t\t\t\tlet ε = calcEpsilon(jnd);\n\t\t\t\tlet low = min;\n\t\t\t\tlet high = get(mappedColor, coordId);\n\n\t\t\t\twhile (high - low > ε) {\n\t\t\t\t\tlet clipped = clone(mappedColor);\n\t\t\t\t\tclipped = toGamut(clipped, { space, method: \"clip\" });\n\t\t\t\t\tlet deltaE = de(mappedColor, clipped);\n\n\t\t\t\t\tif (deltaE - jnd < ε) {\n\t\t\t\t\t\tlow = get(mappedColor, coordId);\n\t\t\t\t\t}\n\t\t\t\t\telse {\n\t\t\t\t\t\thigh = get(mappedColor, coordId);\n\t\t\t\t\t}\n\n\t\t\t\t\tset(mappedColor, coordId, (low + high) / 2);\n\t\t\t\t}\n\n\t\t\t\tspaceColor = to(mappedColor, space);\n\t\t\t}\n\t\t\telse {\n\t\t\t\tspaceColor = clipped;\n\t\t\t}\n\t\t}\n\t\telse {\n\t\t\tspaceColor = to(color, space);\n\t\t}\n\n\t\tif (method === \"clip\" // Dumb coord clipping\n\t\t\t// finish off smarter gamut mapping with clip to get rid of ε, see #17\n\t\t\t|| !inGamut(spaceColor, space, { epsilon: 0 })\n\t\t) {\n\t\t\tlet bounds = Object.values(space.coords).map(c => c.range || []);\n\n\t\t\tspaceColor.coords = spaceColor.coords.map((c, i) => {\n\t\t\t\tlet [min, max] = bounds[i];\n\n\t\t\t\tif (min !== undefined) {\n\t\t\t\t\tc = Math.max(min, c);\n\t\t\t\t}\n\n\t\t\t\tif (max !== undefined) {\n\t\t\t\t\tc = Math.min(c, max);\n\t\t\t\t}\n\n\t\t\t\treturn c;\n\t\t\t});\n\t\t}\n\t}\n\n\tif (space !== color.space) {\n\t\tspaceColor = to(spaceColor, color.space);\n\t}\n\n\tcolor.coords = spaceColor.coords;\n\treturn color;\n}\n\ntoGamut.returns = \"color\";\n\n// The reference colors to be used if lightness is out of the range 0-1 in the\n// `Oklch` space. These are created in the `Oklab` space, as it is used by the\n// DeltaEOK calculation, so it is guaranteed to be imported.\nconst COLORS = {\n\tWHITE: { space: oklab, coords: [1, 0, 0] },\n\tBLACK: { space: oklab, coords: [0, 0, 0] },\n};\n\n/**\n * Given a color `origin`, returns a new color that is in gamut using\n * the CSS Gamut Mapping Algorithm. If `space` is specified, it will be in gamut\n * in `space`, and returned in `space`. Otherwise, it will be in gamut and\n * returned in the color space of `origin`.\n * @param {Object} origin\n * @param {Object} options\n * @param {ColorSpace|string} options.space\n * @returns {Color}\n */\nexport function toGamutCSS (origin, {space} = {}) {\n\tconst JND = 0.02;\n\tconst ε = 0.0001;\n\n\torigin = getColor(origin);\n\n\tif (!space) {\n\t\tspace = origin.space;\n\t}\n\n\tspace = ColorSpace.get(space);\n\tconst oklchSpace = ColorSpace.get(\"oklch\");\n\n\tif (space.isUnbounded) {\n\t\treturn to(origin, space);\n\t}\n\n\tconst origin_OKLCH = to(origin, oklchSpace);\n\tlet L = origin_OKLCH.coords[0];\n\n\t// return media white or black, if lightness is out of range\n\tif (L >= 1) {\n\t\tconst white = to(COLORS.WHITE, space);\n\t\twhite.alpha = origin.alpha;\n\t\treturn to(white, space);\n\t}\n\tif (L <= 0) {\n\t\tconst black = to(COLORS.BLACK, space);\n\t\tblack.alpha = origin.alpha;\n\t\treturn to(black, space);\n\t}\n\n\tif (inGamut(origin_OKLCH, space, {epsilon: 0})) {\n\t\treturn to(origin_OKLCH, space);\n\t}\n\n\tfunction clip (_color) {\n\t\tconst destColor = to(_color, space);\n\t\tconst spaceCoords = Object.values(space.coords);\n\t\tdestColor.coords = destColor.coords.map((coord, index) => {\n\t\t\tif (\"range\" in spaceCoords[index]) {\n\t\t\t\tconst [min, max] =  spaceCoords[index].range;\n\t\t\t\treturn util.clamp(min, coord, max);\n\t\t\t}\n\t\t\treturn coord;\n\t\t});\n\t\treturn destColor;\n\t}\n\tlet min = 0;\n\tlet max = origin_OKLCH.coords[1];\n\tlet min_inGamut = true;\n\tlet current = clone(origin_OKLCH);\n\tlet clipped = clip(current);\n\n\tlet E = deltaEOK(clipped, current);\n\tif (E < JND) {\n\t\treturn clipped;\n\t}\n\n\twhile ((max - min) > ε) {\n\t\tconst chroma = (min + max) / 2;\n\t\tcurrent.coords[1] = chroma;\n\t\tif (min_inGamut && inGamut(current, space, {epsilon: 0})) {\n\t\t\tmin = chroma;\n\t\t}\n\t\telse {\n\t\t\tclipped = clip(current);\n\t\t\tE = deltaEOK(clipped, current);\n\t\t\tif (E < JND) {\n\t\t\t\tif ((JND - E < ε)) {\n\t\t\t\t\tbreak;\n\t\t\t\t}\n\t\t\t\telse {\n\t\t\t\t\tmin_inGamut = false;\n\t\t\t\t\tmin = chroma;\n\t\t\t\t}\n\t\t\t}\n\t\t\telse {\n\t\t\t\tmax = chroma;\n\t\t\t}\n\t\t}\n\t}\n\treturn clipped;\n}\n", "import getColor from \"./getColor.js\";\nimport ColorSpace from \"./space.js\";\nimport toGamut from \"./toGamut.js\";\n\n/**\n * Convert to color space and return a new color\n * @param {Object|string} space - Color space object or id\n * @param {Object} options\n * @param {boolean} options.inGamut - Whether to force resulting color in gamut\n * @returns {Color}\n */\nexport default function to (color, space, {inGamut} = {}) {\n\tcolor = getColor(color);\n\tspace = ColorSpace.get(space);\n\n\tlet coords = space.from(color);\n\tlet ret = {space, coords, alpha: color.alpha};\n\n\tif (inGamut) {\n\t\tret = toGamut(ret, inGamut === true ? undefined : inGamut);\n\t}\n\n\treturn ret;\n}\n\nto.returns = \"color\";\n", "'use strict';\nvar tryToString = require('../internals/try-to-string');\n\nvar $TypeError = TypeError;\n\nmodule.exports = function (O, P) {\n  if (!delete O[P]) throw new $TypeError('Cannot delete property ' + tryToString(P) + ' of ' + tryToString(O));\n};\n", "import * as util from \"./util.js\";\nimport ColorSpace from \"./space.js\";\nimport defaults from \"./defaults.js\";\nimport getColor from \"./getColor.js\";\nimport checkInGamut from \"./inGamut.js\";\nimport toGamut from \"./toGamut.js\";\nimport clone from \"./clone.js\";\n\n/**\n * Generic toString() method, outputs a color(spaceId ...coords) function, a functional syntax, or custom formats defined by the color space\n * @param {Object} options\n * @param {number} options.precision - Significant digits\n * @param {boolean} options.inGamut - Adjust coordinates to fit in gamut first? [default: false]\n */\nexport default function serialize (color, {\n\tprecision = defaults.precision,\n\tformat = \"default\",\n\tinGamut = true,\n\t...customOptions\n} = {}) {\n\tlet ret;\n\n\tcolor = getColor(color);\n\n\tlet formatId = format;\n\tformat = color.space.getFormat(format)\n\t       ?? color.space.getFormat(\"default\")\n\t       ?? ColorSpace.DEFAULT_FORMAT;\n\n\t// The assignment to coords and inGamut needs to stay in the order they are now\n\t// The order of the assignment was changed as a workaround for a bug in Next.js\n\t// See this issue for details: https://github.com/color-js/color.js/issues/260\n\n\tlet coords = color.coords.slice(); // clone so we can manipulate it\n\n\tinGamut ||= format.toGamut;\n\n\tif (inGamut && !checkInGamut(color)) {\n\t\t// FIXME what happens if the color contains NaNs?\n\t\tcoords = toGamut(clone(color), inGamut === true ? undefined : inGamut).coords;\n\t}\n\n\tif (format.type === \"custom\") {\n\t\tcustomOptions.precision = precision;\n\n\t\tif (format.serialize) {\n\t\t\tret = format.serialize(coords, color.alpha, customOptions);\n\t\t}\n\t\telse {\n\t\t\tthrow new TypeError(`format ${formatId} can only be used to parse colors, not for serialization`);\n\t\t}\n\t}\n\telse {\n\t\t// Functional syntax\n\t\tlet name = format.name || \"color\";\n\n\t\tif (format.serializeCoords) {\n\t\t\tcoords = format.serializeCoords(coords, precision);\n\t\t}\n\t\telse {\n\t\t\tif (precision !== null) {\n\t\t\t\tcoords = coords.map(c => {\n\t\t\t\t\treturn util.serializeNumber(c, {precision});\n\t\t\t\t});\n\t\t\t}\n\t\t}\n\n\t\tlet args = [...coords];\n\n\t\tif (name === \"color\") {\n\t\t\t// If output is a color() function, add colorspace id as first argument\n\t\t\tlet cssId = format.id || format.ids?.[0] || color.space.id;\n\t\t\targs.unshift(cssId);\n\t\t}\n\n\t\tlet alpha = color.alpha;\n\t\tif (precision !== null) {\n\t\t\talpha = util.serializeNumber(alpha, {precision});\n\t\t}\n\n\t\tlet strAlpha = color.alpha >= 1 || format.noAlpha ? \"\" : `${format.commas ? \",\" : \" /\"} ${alpha}`;\n\t\tret = `${name}(${args.join(format.commas ? \", \" : \" \")}${strAlpha})`;\n\t}\n\n\treturn ret;\n}\n", "'use strict';\nvar $ = require('../internals/export');\nvar toObject = require('../internals/to-object');\nvar lengthOfArrayLike = require('../internals/length-of-array-like');\nvar setArrayLength = require('../internals/array-set-length');\nvar deletePropertyOrThrow = require('../internals/delete-property-or-throw');\nvar doesNotExceedSafeInteger = require('../internals/does-not-exceed-safe-integer');\n\n// IE8-\nvar INCORRECT_RESULT = [].unshift(0) !== 1;\n\n// V8 ~ Chrome < 71 and Safari <= 15.4, FF < 23 throws InternalError\nvar properErrorOnNonWritableLength = function () {\n  try {\n    // eslint-disable-next-line es/no-object-defineproperty -- safe\n    Object.defineProperty([], 'length', { writable: false }).unshift();\n  } catch (error) {\n    return error instanceof TypeError;\n  }\n};\n\nvar FORCED = INCORRECT_RESULT || !properErrorOnNonWritableLength();\n\n// `Array.prototype.unshift` method\n// https://tc39.es/ecma262/#sec-array.prototype.unshift\n$({ target: 'Array', proto: true, arity: 1, forced: FORCED }, {\n  // eslint-disable-next-line no-unused-vars -- required for `.length`\n  unshift: function unshift(item) {\n    var O = toObject(this);\n    var len = lengthOfArrayLike(O);\n    var argCount = arguments.length;\n    if (argCount) {\n      doesNotExceedSafeInteger(len + argCount);\n      var k = len;\n      while (k--) {\n        var to = k + argCount;\n        if (k in O) O[to] = O[k];\n        else deletePropertyOrThrow(O, to);\n      }\n      for (var j = 0; j < argCount; j++) {\n        O[j] = arguments[j];\n      }\n    } return setArrayLength(O, len + argCount);\n  }\n});\n", "import RGBColorSpace from \"../rgbspace.js\";\n\n// convert an array of linear-light rec2020 values to CIE XYZ\n// using  D65 (no chromatic adaptation)\n// http://www.brucelindbloom.com/index.html?Eqn_RGB_XYZ_Matrix.html\n// 0 is actually calculated as  4.994106574466076e-17\nconst toXYZ_M = [\n\t[ 0.6369580483012914, 0.14461690358620832,  0.1688809751641721  ],\n\t[ 0.2627002120112671, 0.6779980715188708,   0.05930171646986196 ],\n\t[ 0.000000000000000,  0.028072693049087428, 1.060985057710791   ],\n];\n\n// from ITU-R BT.2124-0 Annex 2 p.3\nconst fromXYZ_M = [\n\t[  1.716651187971268,  -0.355670783776392, -0.253366281373660  ],\n\t[ -0.666684351832489,   1.616481236634939,  0.0157685458139111 ],\n\t[  0.017639857445311,  -0.042770613257809,  0.942103121235474  ],\n];\n\nexport default new RGBColorSpace({\n\tid: \"rec2020-linear\",\n\tcssId: \"--rec2020-linear\",\n\tname: \"Linear REC.2020\",\n\twhite: \"D65\",\n\ttoXYZ_M,\n\tfromXYZ_M,\n});\n", "import RGBColorSpace from \"../rgbspace.js\";\nimport REC2020Linear from \"./rec2020-linear.js\";\n// import sRGB from \"./srgb.js\";\n\nconst α = 1.09929682680944;\nconst β = 0.018053968510807;\n\nexport default new RGBColorSpace({\n\tid: \"rec2020\",\n\tname: \"REC.2020\",\n\tbase: REC2020Linear,\n\t// Non-linear transfer function from Rec. ITU-R BT.2020-2 table 4\n\ttoBase (RGB) {\n\t\treturn RGB.map(function (val) {\n\t\t\tif (val < β * 4.5) {\n\t\t\t\treturn val / 4.5;\n\t\t\t}\n\n\t\t\treturn Math.pow((val + α - 1) / α, 1 / 0.45);\n\t\t});\n\t},\n\tfromBase (RGB) {\n\t\treturn RGB.map(function (val) {\n\t\t\tif (val >= β) {\n\t\t\t\treturn α * Math.pow(val, 0.45) - (α - 1);\n\t\t\t}\n\n\t\t\treturn 4.5 * val;\n\t\t});\n\t},\n});\n", "import RGBColorSpace from \"../rgbspace.js\";\n\nconst toXYZ_M = [\n\t[0.4865709486482162, 0.26566769316909306, 0.1982172852343625],\n\t[0.2289745640697488, 0.6917385218365064,  0.079286914093745],\n\t[0.0000000000000000, 0.04511338185890264, 1.043944368900976],\n];\n\nconst fromXYZ_M = [\n\t[ 2.493496911941425,   -0.9313836179191239, -0.40271078445071684],\n\t[-0.8294889695615747,   1.7626640603183463,  0.023624685841943577],\n\t[ 0.03584583024378447, -0.07617238926804182, 0.9568845240076872],\n];\n\nexport default new RGBColorSpace({\n\tid: \"p3-linear\",\n\tcssId: \"--display-p3-linear\",\n\tname: \"Linear P3\",\n\twhite: \"D65\",\n\tto<PERSON><PERSON><PERSON>_<PERSON>,\n\tfromXYZ_M,\n});\n", "import RGBColorSpace from \"../rgbspace.js\";\n\n// This is the linear-light version of sRGB\n// as used for example in SVG filters\n// or in Canvas\n\n// This matrix was calculated directly from the RGB and white chromaticities\n// when rounded to 8 decimal places, it agrees completely with the official matrix\n// see https://github.com/w3c/csswg-drafts/issues/5922\nconst toXYZ_M = [\n\t[ 0.41239079926595934, 0.357584339383878,   0.1804807884018343  ],\n\t[ 0.21263900587151027, 0.715168678767756,   0.07219231536073371 ],\n\t[ 0.01933081871559182, 0.11919477979462598, 0.9505321522496607  ],\n];\n\n// This matrix is the inverse of the above;\n// again it agrees with the official definition when rounded to 8 decimal places\nexport const fromXYZ_M = [\n\t[  3.2409699419045226,  -1.537383177570094,   -0.4986107602930034  ],\n\t[ -0.9692436362808796,   1.8759675015077202,   0.04155505740717559 ],\n\t[  0.05563007969699366, -0.20397695888897652,  1.0569715142428786  ],\n];\n\nexport default new RGBColorSpace({\n\tid: \"srgb-linear\",\n\tname: \"Linear sRGB\",\n\twhite: \"D65\",\n\ttoXYZ_M,\n\tfromXYZ_M,\n});\n", "/* List of CSS color keywords\n * Note that this does not include currentColor, transparent,\n * or system colors\n */\n\n// To produce: Visit https://www.w3.org/TR/css-color-4/#named-colors\n// and run in the console:\n// copy($$(\"tr\", $(\".named-color-table tbody\")).map(tr => `\"${tr.cells[2].textContent.trim()}\": [${tr.cells[4].textContent.trim().split(/\\s+/).map(c => c === \"0\"? \"0\" : c === \"255\"? \"1\" : c + \" / 255\").join(\", \")}]`).join(\",\\n\"))\nexport default {\n\t\"aliceblue\": [240 / 255, 248 / 255, 1],\n\t\"antiquewhite\": [250 / 255, 235 / 255, 215 / 255],\n\t\"aqua\": [0, 1, 1],\n\t\"aquamarine\": [127 / 255, 1, 212 / 255],\n\t\"azure\": [240 / 255, 1, 1],\n\t\"beige\": [245 / 255, 245 / 255, 220 / 255],\n\t\"bisque\": [1, 228 / 255, 196 / 255],\n\t\"black\": [0, 0, 0],\n\t\"blanchedalmond\": [1, 235 / 255, 205 / 255],\n\t\"blue\": [0, 0, 1],\n\t\"blueviolet\": [138 / 255, 43 / 255, 226 / 255],\n\t\"brown\": [165 / 255, 42 / 255, 42 / 255],\n\t\"burlywood\": [222 / 255, 184 / 255, 135 / 255],\n\t\"cadetblue\": [95 / 255, 158 / 255, 160 / 255],\n\t\"chartreuse\": [127 / 255, 1, 0],\n\t\"chocolate\": [210 / 255, 105 / 255, 30 / 255],\n\t\"coral\": [1, 127 / 255, 80 / 255],\n\t\"cornflowerblue\": [100 / 255, 149 / 255, 237 / 255],\n\t\"cornsilk\": [1, 248 / 255, 220 / 255],\n\t\"crimson\": [220 / 255, 20 / 255, 60 / 255],\n\t\"cyan\": [0, 1, 1],\n\t\"darkblue\": [0, 0, 139 / 255],\n\t\"darkcyan\": [0, 139 / 255, 139 / 255],\n\t\"darkgoldenrod\": [184 / 255, 134 / 255, 11 / 255],\n\t\"darkgray\": [169 / 255, 169 / 255, 169 / 255],\n\t\"darkgreen\": [0, 100 / 255, 0],\n\t\"darkgrey\": [169 / 255, 169 / 255, 169 / 255],\n\t\"darkkhaki\": [189 / 255, 183 / 255, 107 / 255],\n\t\"darkmagenta\": [139 / 255, 0, 139 / 255],\n\t\"darkolivegreen\": [85 / 255, 107 / 255, 47 / 255],\n\t\"darkorange\": [1, 140 / 255, 0],\n\t\"darkorchid\": [153 / 255, 50 / 255, 204 / 255],\n\t\"darkred\": [139 / 255, 0, 0],\n\t\"darksalmon\": [233 / 255, 150 / 255, 122 / 255],\n\t\"darkseagreen\": [143 / 255, 188 / 255, 143 / 255],\n\t\"darkslateblue\": [72 / 255, 61 / 255, 139 / 255],\n\t\"darkslategray\": [47 / 255, 79 / 255, 79 / 255],\n\t\"darkslategrey\": [47 / 255, 79 / 255, 79 / 255],\n\t\"darkturquoise\": [0, 206 / 255, 209 / 255],\n\t\"darkviolet\": [148 / 255, 0, 211 / 255],\n\t\"deeppink\": [1, 20 / 255, 147 / 255],\n\t\"deepskyblue\": [0, 191 / 255, 1],\n\t\"dimgray\": [105 / 255, 105 / 255, 105 / 255],\n\t\"dimgrey\": [105 / 255, 105 / 255, 105 / 255],\n\t\"dodgerblue\": [30 / 255, 144 / 255, 1],\n\t\"firebrick\": [178 / 255, 34 / 255, 34 / 255],\n\t\"floralwhite\": [1, 250 / 255, 240 / 255],\n\t\"forestgreen\": [34 / 255, 139 / 255, 34 / 255],\n\t\"fuchsia\": [1, 0, 1],\n\t\"gainsboro\": [220 / 255, 220 / 255, 220 / 255],\n\t\"ghostwhite\": [248 / 255, 248 / 255, 1],\n\t\"gold\": [1, 215 / 255, 0],\n\t\"goldenrod\": [218 / 255, 165 / 255, 32 / 255],\n\t\"gray\": [128 / 255, 128 / 255, 128 / 255],\n\t\"green\": [0, 128 / 255, 0],\n\t\"greenyellow\": [173 / 255, 1, 47 / 255],\n\t\"grey\": [128 / 255, 128 / 255, 128 / 255],\n\t\"honeydew\": [240 / 255, 1, 240 / 255],\n\t\"hotpink\": [1, 105 / 255, 180 / 255],\n\t\"indianred\": [205 / 255, 92 / 255, 92 / 255],\n\t\"indigo\": [75 / 255, 0, 130 / 255],\n\t\"ivory\": [1, 1, 240 / 255],\n\t\"khaki\": [240 / 255, 230 / 255, 140 / 255],\n\t\"lavender\": [230 / 255, 230 / 255, 250 / 255],\n\t\"lavenderblush\": [1, 240 / 255, 245 / 255],\n\t\"lawngreen\": [124 / 255, 252 / 255, 0],\n\t\"lemonchiffon\": [1, 250 / 255, 205 / 255],\n\t\"lightblue\": [173 / 255, 216 / 255, 230 / 255],\n\t\"lightcoral\": [240 / 255, 128 / 255, 128 / 255],\n\t\"lightcyan\": [224 / 255, 1, 1],\n\t\"lightgoldenrodyellow\": [250 / 255, 250 / 255, 210 / 255],\n\t\"lightgray\": [211 / 255, 211 / 255, 211 / 255],\n\t\"lightgreen\": [144 / 255, 238 / 255, 144 / 255],\n\t\"lightgrey\": [211 / 255, 211 / 255, 211 / 255],\n\t\"lightpink\": [1, 182 / 255, 193 / 255],\n\t\"lightsalmon\": [1, 160 / 255, 122 / 255],\n\t\"lightseagreen\": [32 / 255, 178 / 255, 170 / 255],\n\t\"lightskyblue\": [135 / 255, 206 / 255, 250 / 255],\n\t\"lightslategray\": [119 / 255, 136 / 255, 153 / 255],\n\t\"lightslategrey\": [119 / 255, 136 / 255, 153 / 255],\n\t\"lightsteelblue\": [176 / 255, 196 / 255, 222 / 255],\n\t\"lightyellow\": [1, 1, 224 / 255],\n\t\"lime\": [0, 1, 0],\n\t\"limegreen\": [50 / 255, 205 / 255, 50 / 255],\n\t\"linen\": [250 / 255, 240 / 255, 230 / 255],\n\t\"magenta\": [1, 0, 1],\n\t\"maroon\": [128 / 255, 0, 0],\n\t\"mediumaquamarine\": [102 / 255, 205 / 255, 170 / 255],\n\t\"mediumblue\": [0, 0, 205 / 255],\n\t\"mediumorchid\": [186 / 255, 85 / 255, 211 / 255],\n\t\"mediumpurple\": [147 / 255, 112 / 255, 219 / 255],\n\t\"mediumseagreen\": [60 / 255, 179 / 255, 113 / 255],\n\t\"mediumslateblue\": [123 / 255, 104 / 255, 238 / 255],\n\t\"mediumspringgreen\": [0, 250 / 255, 154 / 255],\n\t\"mediumturquoise\": [72 / 255, 209 / 255, 204 / 255],\n\t\"mediumvioletred\": [199 / 255, 21 / 255, 133 / 255],\n\t\"midnightblue\": [25 / 255, 25 / 255, 112 / 255],\n\t\"mintcream\": [245 / 255, 1, 250 / 255],\n\t\"mistyrose\": [1, 228 / 255, 225 / 255],\n\t\"moccasin\": [1, 228 / 255, 181 / 255],\n\t\"navajowhite\": [1, 222 / 255, 173 / 255],\n\t\"navy\": [0, 0, 128 / 255],\n\t\"oldlace\": [253 / 255, 245 / 255, 230 / 255],\n\t\"olive\": [128 / 255, 128 / 255, 0],\n\t\"olivedrab\": [107 / 255, 142 / 255, 35 / 255],\n\t\"orange\": [1, 165 / 255, 0],\n\t\"orangered\": [1, 69 / 255, 0],\n\t\"orchid\": [218 / 255, 112 / 255, 214 / 255],\n\t\"palegoldenrod\": [238 / 255, 232 / 255, 170 / 255],\n\t\"palegreen\": [152 / 255, 251 / 255, 152 / 255],\n\t\"paleturquoise\": [175 / 255, 238 / 255, 238 / 255],\n\t\"palevioletred\": [219 / 255, 112 / 255, 147 / 255],\n\t\"papayawhip\": [1, 239 / 255, 213 / 255],\n\t\"peachpuff\": [1, 218 / 255, 185 / 255],\n\t\"peru\": [205 / 255, 133 / 255, 63 / 255],\n\t\"pink\": [1, 192 / 255, 203 / 255],\n\t\"plum\": [221 / 255, 160 / 255, 221 / 255],\n\t\"powderblue\": [176 / 255, 224 / 255, 230 / 255],\n\t\"purple\": [128 / 255, 0, 128 / 255],\n\t\"rebeccapurple\": [102 / 255, 51 / 255, 153 / 255],\n\t\"red\": [1, 0, 0],\n\t\"rosybrown\": [188 / 255, 143 / 255, 143 / 255],\n\t\"royalblue\": [65 / 255, 105 / 255, 225 / 255],\n\t\"saddlebrown\": [139 / 255, 69 / 255, 19 / 255],\n\t\"salmon\": [250 / 255, 128 / 255, 114 / 255],\n\t\"sandybrown\": [244 / 255, 164 / 255, 96 / 255],\n\t\"seagreen\": [46 / 255, 139 / 255, 87 / 255],\n\t\"seashell\": [1, 245 / 255, 238 / 255],\n\t\"sienna\": [160 / 255, 82 / 255, 45 / 255],\n\t\"silver\": [192 / 255, 192 / 255, 192 / 255],\n\t\"skyblue\": [135 / 255, 206 / 255, 235 / 255],\n\t\"slateblue\": [106 / 255, 90 / 255, 205 / 255],\n\t\"slategray\": [112 / 255, 128 / 255, 144 / 255],\n\t\"slategrey\": [112 / 255, 128 / 255, 144 / 255],\n\t\"snow\": [1, 250 / 255, 250 / 255],\n\t\"springgreen\": [0, 1, 127 / 255],\n\t\"steelblue\": [70 / 255, 130 / 255, 180 / 255],\n\t\"tan\": [210 / 255, 180 / 255, 140 / 255],\n\t\"teal\": [0, 128 / 255, 128 / 255],\n\t\"thistle\": [216 / 255, 191 / 255, 216 / 255],\n\t\"tomato\": [1, 99 / 255, 71 / 255],\n\t\"turquoise\": [64 / 255, 224 / 255, 208 / 255],\n\t\"violet\": [238 / 255, 130 / 255, 238 / 255],\n\t\"wheat\": [245 / 255, 222 / 255, 179 / 255],\n\t\"white\": [1, 1, 1],\n\t\"whitesmoke\": [245 / 255, 245 / 255, 245 / 255],\n\t\"yellow\": [1, 1, 0],\n\t\"yellowgreen\": [154 / 255, 205 / 255, 50 / 255],\n};\n", "import RGBColorSpace from \"../rgbspace.js\";\nimport sRGBLinear from \"./srgb-linear.js\";\nimport KEYWORDS from \"../keywords.js\";\n\nlet coordGrammar = Array(3).fill(\"<percentage> | <number>[0, 255]\");\nlet coordGrammarNumber = Array(3).fill(\"<number>[0, 255]\");\n\nexport default new RGBColorSpace({\n\tid: \"srgb\",\n\tname: \"sRGB\",\n\tbase: sRGBLinear,\n\tfromBase: rgb => {\n\t\t// convert an array of linear-light sRGB values in the range 0.0-1.0\n\t\t// to gamma corrected form\n\t\t// https://en.wikipedia.org/wiki/SRGB\n\t\treturn rgb.map(val => {\n\t\t\tlet sign = val < 0 ? -1 : 1;\n\t\t\tlet abs = val * sign;\n\n\t\t\tif (abs > 0.0031308) {\n\t\t\t\treturn sign * (1.055 * (abs ** (1 / 2.4)) - 0.055);\n\t\t\t}\n\n\t\t\treturn 12.92 * val;\n\t\t});\n\t},\n\ttoBase: rgb => {\n\t\t// convert an array of sRGB values in the range 0.0 - 1.0\n\t\t// to linear light (un-companded) form.\n\t\t// https://en.wikipedia.org/wiki/SRGB\n\t\treturn rgb.map(val => {\n\t\t\tlet sign = val < 0 ? -1 : 1;\n\t\t\tlet abs = val * sign;\n\n\t\t\tif (abs <= 0.04045) {\n\t\t\t\treturn val / 12.92;\n\t\t\t}\n\n\t\t\treturn sign * (((abs + 0.055) / 1.055) ** 2.4);\n\t\t});\n\t},\n\tformats: {\n\t\t\"rgb\": {\n\t\t\tcoords: coordGrammar,\n\t\t},\n\t\t\"rgb_number\": {\n\t\t\tname: \"rgb\",\n\t\t\tcommas: true,\n\t\t\tcoords: coordGrammarNumber,\n\t\t\tnoAlpha: true,\n\t\t},\n\t\t\"color\": { /* use defaults */ },\n\t\t\"rgba\": {\n\t\t\tcoords: coordGrammar,\n\t\t\tcommas: true,\n\t\t\tlastAlpha: true,\n\t\t},\n\t\t\"rgba_number\": {\n\t\t\tname: \"rgba\",\n\t\t\tcommas: true,\n\t\t\tcoords: coordGrammarNumber,\n\t\t},\n\t\t\"hex\": {\n\t\t\ttype: \"custom\",\n\t\t\ttoGamut: true,\n\t\t\ttest: str => /^#([a-f0-9]{3,4}){1,2}$/i.test(str),\n\t\t\tparse (str) {\n\t\t\t\tif (str.length <= 5) {\n\t\t\t\t\t// #rgb or #rgba, duplicate digits\n\t\t\t\t\tstr = str.replace(/[a-f0-9]/gi, \"$&$&\");\n\t\t\t\t}\n\n\t\t\t\tlet rgba = [];\n\t\t\t\tstr.replace(/[a-f0-9]{2}/gi, component => {\n\t\t\t\t\trgba.push(parseInt(component, 16) / 255);\n\t\t\t\t});\n\n\t\t\t\treturn {\n\t\t\t\t\tspaceId: \"srgb\",\n\t\t\t\t\tcoords: rgba.slice(0, 3),\n\t\t\t\t\talpha: rgba.slice(3)[0],\n\t\t\t\t};\n\t\t\t},\n\t\t\tserialize: (coords, alpha, {\n\t\t\t\tcollapse = true, // collapse to 3-4 digit hex when possible?\n\t\t\t} = {}) => {\n\t\t\t\tif (alpha < 1) {\n\t\t\t\t\tcoords.push(alpha);\n\t\t\t\t}\n\n\t\t\t\tcoords = coords.map(c => Math.round(c * 255));\n\n\t\t\t\tlet collapsible = collapse && coords.every(c => c % 17 === 0);\n\n\t\t\t\tlet hex = coords.map(c => {\n\t\t\t\t\tif (collapsible) {\n\t\t\t\t\t\treturn (c / 17).toString(16);\n\t\t\t\t\t}\n\n\t\t\t\t\treturn c.toString(16).padStart(2, \"0\");\n\t\t\t\t}).join(\"\");\n\n\t\t\t\treturn \"#\" + hex;\n\t\t\t},\n\t\t},\n\t\t\"keyword\": {\n\t\t\ttype: \"custom\",\n\t\t\ttest: str => /^[a-z]+$/i.test(str),\n\t\t\tparse (str) {\n\t\t\t\tstr = str.toLowerCase();\n\t\t\t\tlet ret = {spaceId: \"srgb\", coords: null, alpha: 1};\n\n\t\t\t\tif (str === \"transparent\") {\n\t\t\t\t\tret.coords = KEYWORDS.black;\n\t\t\t\t\tret.alpha = 0;\n\t\t\t\t}\n\t\t\t\telse {\n\t\t\t\t\tret.coords = KEYWORDS[str];\n\t\t\t\t}\n\n\t\t\t\tif (ret.coords) {\n\t\t\t\t\treturn ret;\n\t\t\t\t}\n\t\t\t},\n\t\t},\n\t},\n});\n", "import RGBColorSpace from \"../rgbspace.js\";\nimport P3Linear from \"./p3-linear.js\";\nimport sRGB from \"./srgb.js\";\n\nexport default new RGBColorSpace({\n\tid: \"p3\",\n\tcssId: \"display-p3\",\n\tname: \"P3\",\n\tbase: P3Linear,\n\t// Gamma encoding/decoding is the same as sRGB\n\tfromBase: sRGB.fromBase,\n\ttoBase: sRGB.toBase,\n});\n", "import { isNone, skipNone } from \"./util.js\";\nimport defaults from \"./defaults.js\";\nimport to from \"./to.js\";\nimport serialize from \"./serialize.js\";\nimport clone from \"./clone.js\";\nimport REC2020 from \"./spaces/rec2020.js\";\nimport P3 from \"./spaces/p3.js\";\nimport Lab from \"./spaces/lab.js\";\nimport sRGB from \"./spaces/srgb.js\";\n\n// Default space for CSS output. Code in Color.js makes this wider if there's a DOM available\ndefaults.display_space = sRGB;\n\nlet supportsNone;\n\nif (typeof CSS !== \"undefined\" && CSS.supports) {\n\t// Find widest supported color space for CSS\n\tfor (let space of [Lab, REC2020, P3]) {\n\t\tlet coords = space.getMinCoords();\n\t\tlet color = {space, coords, alpha: 1};\n\t\tlet str = serialize(color);\n\n\t\tif (CSS.supports(\"color\", str)) {\n\t\t\tdefaults.display_space = space;\n\t\t\tbreak;\n\t\t}\n\t}\n}\n\n/**\n * Returns a serialization of the color that can actually be displayed in the browser.\n * If the default serialization can be displayed, it is returned.\n * Otherwise, the color is converted to Lab, REC2020, or P3, whichever is the widest supported.\n * In Node.js, this is basically equivalent to `serialize()` but returns a `String` object instead.\n *\n * @export\n * @param {{space, coords} | Color | string} color\n * @param {*} [options={}] Options to be passed to serialize()\n * @param {ColorSpace | string} [options.space = defaults.display_space] Color space to use for serialization if default is not supported\n * @returns {String} String object containing the serialized color with a color property containing the converted color (or the original, if no conversion was necessary)\n */\nexport default function display (color, {space = defaults.display_space, ...options} = {}) {\n\tlet ret = serialize(color, options);\n\n\tif (typeof CSS === \"undefined\" || CSS.supports(\"color\", ret) || !defaults.display_space) {\n\t\tret = new String(ret);\n\t\tret.color = color;\n\t}\n\telse {\n\t\t// If we're here, what we were about to output is not supported\n\t\tlet fallbackColor = color;\n\n\t\t// First, check if the culprit is none values\n\t\tlet hasNone = color.coords.some(isNone) || isNone(color.alpha);\n\n\t\tif (hasNone) {\n\t\t\t// Does the browser support none values?\n\t\t\tif (!(supportsNone ??= CSS.supports(\"color\", \"hsl(none 50% 50%)\"))) {\n\t\t\t\t// Nope, try again without none\n\t\t\t\tfallbackColor = clone(color);\n\t\t\t\tfallbackColor.coords = fallbackColor.coords.map(skipNone);\n\t\t\t\tfallbackColor.alpha = skipNone(fallbackColor.alpha);\n\n\t\t\t\tret = serialize(fallbackColor, options);\n\n\t\t\t\tif (CSS.supports(\"color\", ret)) {\n\t\t\t\t\t// We're done, now it's supported\n\t\t\t\t\tret = new String(ret);\n\t\t\t\t\tret.color = fallbackColor;\n\t\t\t\t\treturn ret;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t// If we're here, the color function is not supported\n\t\t// Fall back to fallback space\n\t\tfallbackColor = to(fallbackColor, space);\n\t\tret = new String(serialize(fallbackColor, options));\n\t\tret.color = fallbackColor;\n\t}\n\n\treturn ret;\n}\n", "/**\n * Relative luminance\n */\nimport get from \"./get.js\";\nimport set from \"./set.js\";\nimport xyz_d65 from \"./spaces/xyz-d65.js\";\n\nexport function getLuminance (color) {\n\t// Assume getColor() is called on color in get()\n\treturn get(color, [xyz_d65, \"y\"]);\n}\n\nexport function setLuminance (color, value) {\n\t// Assume getColor() is called on color in set()\n\tset(color, [xyz_d65, \"y\"], value);\n}\n\nexport function register (Color) {\n\tObject.defineProperty(Color.prototype, \"luminance\", {\n\t\tget () {\n\t\t\treturn getLuminance(this);\n\t\t},\n\t\tset (value) {\n\t\t\tsetLuminance(this, value);\n\t\t},\n\t});\n}\n", "// APCA 0.0.98G\n// https://github.com/Myndex/apca-w3\n// see also https://github.com/w3c/silver/issues/643\n\nimport getColor from \"../getColor.js\";\nimport to from \"../to.js\";\n\n// exponents\nconst normBG = 0.56;\nconst normTXT = 0.57;\nconst revTXT = 0.62;\nconst revBG = 0.65;\n\n// clamps\nconst blkThrs = 0.022;\nconst blkClmp = 1.414;\nconst loClip = 0.1;\nconst deltaYmin = 0.0005;\n\n// scalers\n// see https://github.com/w3c/silver/issues/645\nconst scaleBoW = 1.14;\nconst loBoWoffset = 0.027;\nconst scaleWoB = 1.14;\nconst loWoBoffset = 0.027;\n\nfunction fclamp (Y) {\n\tif (Y >= blkThrs) {\n\t\treturn Y;\n\t}\n\treturn Y + (blkThrs - Y) ** blkClmp;\n}\n\nfunction linearize (val) {\n\tlet sign = val < 0 ? -1 : 1;\n\tlet abs = Math.abs(val);\n\treturn sign * Math.pow(abs, 2.4);\n}\n\n// Not symmetric, requires a foreground (text) color, and a background color\nexport default function contrastAPCA (background, foreground) {\n\tforeground = getColor(foreground);\n\tbackground = getColor(background);\n\n\tlet S;\n\tlet C;\n\tlet Sapc;\n\n\t// Myndex as-published, assumes sRGB inputs\n\tlet R, G, B;\n\n\tforeground = to(foreground, \"srgb\");\n\t// Should these be clamped to in-gamut values?\n\n\t// Calculates \"screen luminance\" with non-standard simple gamma EOTF\n\t// weights should be from CSS Color 4, not the ones here which are via Myndex and copied from Lindbloom\n\t[R, G, B] = foreground.coords;\n\tlet lumTxt = linearize(R) * 0.2126729 + linearize(G) * 0.7151522 + linearize(B) * 0.0721750;\n\n\tbackground = to(background, \"srgb\");\n\t[R, G, B] = background.coords;\n\tlet lumBg = linearize(R) * 0.2126729 + linearize(G) * 0.7151522 + linearize(B) * 0.0721750;\n\n\t// toe clamping of very dark values to account for flare\n\tlet Ytxt = fclamp(lumTxt);\n\tlet Ybg = fclamp(lumBg);\n\n\t// are we \"Black on White\" (dark on light), or light on dark?\n\tlet BoW = Ybg > Ytxt;\n\n\t// why is this a delta, when Y is not perceptually uniform?\n\t// Answer: it is a noise gate, see\n\t// https://github.com/LeaVerou/color.js/issues/208\n\tif (Math.abs(Ybg - Ytxt) < deltaYmin) {\n\t\tC = 0;\n\t}\n\telse {\n\t\tif (BoW) {\n\t\t\t// dark text on light background\n\t\t\tS = Ybg ** normBG - Ytxt ** normTXT;\n\t\t\tC = S * scaleBoW;\n\t\t}\n\t\telse {\n\t\t\t// light text on dark background\n\t\t\tS = Ybg ** revBG - Ytxt ** revTXT;\n\t\t\tC = S * scaleWoB;\n\t\t}\n\t}\n\tif (Math.abs(C) < loClip) {\n\t\tSapc = 0;\n\t}\n\telse if (C > 0) {\n\t\t// not clear whether Woffset is loBoWoffset or loWoBoffset\n\t\t// but they have the same value\n\t\tSapc = C - loBoWoffset;\n\t}\n\telse {\n\t\tSapc = C + loBoWoffset;\n\t}\n\n\treturn Sapc * 100;\n}\n", "import ColorSpace from \"../space.js\";\nimport {WHITES} from \"../adapt.js\";\nimport xyz_d65 from \"./xyz-d65.js\";\n\n// κ * ε  = 2^3 = 8\nconst ε = 216 / 24389;  // 6^3/29^3 == (24/116)^3\nconst ε3 = 24 / 116;\nconst κ = 24389 / 27;   // 29^3/3^3\n\nlet white = WHITES.D65;\n\nexport default new ColorSpace({\n\tid: \"lab-d65\",\n\tname: \"Lab D65\",\n\tcoords: {\n\t\tl: {\n\t\t\trefRange: [0, 100],\n\t\t\tname: \"Lightness\",\n\t\t},\n\t\ta: {\n\t\t\trefRange: [-125, 125],\n\t\t},\n\t\tb: {\n\t\t\trefRange: [-125, 125],\n\t\t},\n\t},\n\n\t// Assuming XYZ is relative to D65, convert to CIE Lab\n\t// from CIE standard, which now defines these as a rational fraction\n\twhite,\n\n\tbase: xyz_d65,\n\t// Convert D65-adapted XYZ to Lab\n\t//  CIE 15.3:2004 section 8.2.1.1\n\tfromBase (XYZ) {\n\t\t// compute xyz, which is XYZ scaled relative to reference white\n\t\tlet xyz = XYZ.map((value, i) => value / white[i]);\n\n\t\t// now compute f\n\t\tlet f = xyz.map(value => value > ε ? Math.cbrt(value) : (κ * value + 16) / 116);\n\n\t\treturn [\n\t\t\t(116 * f[1]) - 16,   // L\n\t\t\t500 * (f[0] - f[1]), // a\n\t\t\t200 * (f[1] - f[2]),  // b\n\t\t];\n\t},\n\t// Convert Lab to D65-adapted XYZ\n\t// Same result as CIE 15.3:2004 Appendix D although the derivation is different\n\t// http://www.brucelindbloom.com/index.html?Eqn_RGB_XYZ_Matrix.html\n\ttoBase (Lab) {\n\t\t// compute f, starting with the luminance-related term\n\t\tlet f = [];\n\t\tf[1] = (Lab[0] + 16) / 116;\n\t\tf[0] = Lab[1] / 500 + f[1];\n\t\tf[2] = f[1] - Lab[2] / 200;\n\n\t\t// compute xyz\n\t\tlet xyz = [\n\t\t\tf[0]   > ε3 ? Math.pow(f[0], 3)                : (116 * f[0] - 16) / κ,\n\t\t\tLab[0] > 8  ? Math.pow((Lab[0] + 16) / 116, 3) : Lab[0] / κ,\n\t\t\tf[2]   > ε3 ? Math.pow(f[2], 3)                : (116 * f[2] - 16) / κ,\n\t\t];\n\n\t\t// Compute XYZ by scaling xyz by reference white\n\t\treturn xyz.map((value, i) => value * white[i]);\n\t},\n\n\tformats: {\n\t\t\"lab-d65\": {\n\t\t\tcoords: [\"<number> | <percentage>\", \"<number> | <percentage>[-1,1]\", \"<number> | <percentage>[-1,1]\"],\n\t\t},\n\t},\n});\n", "// Delta Phi Star perceptual lightness contrast\n// See https://github.com/Myndex/deltaphistar\n// The (difference between two Lstars each raised to phi) raised to (1/phi)\n// Symmetric, does not matter which is foreground and which is background\n\n\nimport getColor from \"../getColor.js\";\nimport get from \"../get.js\";\nimport lab_d65 from \"../spaces/lab-d65.js\";\n\nconst phi = Math.pow(5, 0.5) * 0.5 + 0.5; // Math.phi can be used if Math.js\n\nexport default function contrastDeltaPhi (color1, color2) {\n\tcolor1 = getColor(color1);\n\tcolor2 = getColor(color2);\n\n\tlet Lstr1 = get(color1, [lab_d65, \"l\"]);\n\tlet Lstr2 = get(color2, [lab_d65, \"l\"]);\n\n\tlet deltaPhiStar = Math.abs(Math.pow(Lstr1, phi) - Math.pow(Lstr2, phi));\n\n\tlet contrast = Math.pow(deltaPhiStar, (1 / phi)) * Math.SQRT2 - 40;\n\n\treturn (contrast < 7.5) ? 0.0 : contrast ;\n}\n", "// CIE Lightness difference, as used by Google Material Design\n// Google HCT Tone is the same as CIE Lightness\n// https://material.io/blog/science-of-color-design\n\nimport getColor from \"../getColor.js\";\nimport get from \"../get.js\";\nimport lab from \"../spaces/lab.js\";\n\nexport default function contrastLstar (color1, color2) {\n\tcolor1 = getColor(color1);\n\tcolor2 = getColor(color2);\n\n\tlet L1 = get(color1, [lab, \"l\"]);\n\tlet L2 = get(color2, [lab, \"l\"]);\n\n\treturn Math.abs(L1 - L2);\n}\n", "// Michelson  luminance contrast\n// the relation between the spread and the sum of the two luminances\n// Symmetric, does not matter which is foreground and which is background\n// No black level compensation for flare.\n\nimport getColor from \"../getColor.js\";\nimport {getLuminance} from \"../luminance.js\";\n\nexport default function contrast<PERSON><PERSON><PERSON> (color1, color2) {\n\tcolor1 = getColor(color1);\n\tcolor2 = getColor(color2);\n\n\tlet Y1 = Math.max(getLuminance(color1), 0);\n\tlet Y2 = Math.max(getLuminance(color2), 0);\n\n\tif (Y2 > Y1) {\n\t\t[Y1, Y2] = [Y2, Y1];\n\t}\n\n\tlet denom = (Y1 + Y2);\n\treturn denom === 0 ? 0 : (Y1 - Y2) / denom;\n}\n", "// WCAG 2.0 contrast https://www.w3.org/TR/WCAG20-TECHS/G18.html\n// Simple contrast, with fixed 5% viewing flare contribution\n// Symmetric, does not matter which is foreground and which is background\n\nimport getColor from \"../getColor.js\";\nimport {getLuminance} from \"../luminance.js\";\n\nexport default function contrastWCAG21 (color1, color2) {\n\tcolor1 = getColor(color1);\n\tcolor2 = getColor(color2);\n\n\tlet Y1 = Math.max(getLuminance(color1), 0);\n\tlet Y2 = Math.max(getLuminance(color2), 0);\n\n\tif (Y2 > Y1) {\n\t\t[Y1, Y2] = [Y2, Y1];\n\t}\n\n\treturn (Y1 + .05) / (Y2 + .05);\n}\n", "// Weber luminance contrast\n// The difference between the two luminances divided by the lower luminance\n// Symmetric, does not matter which is foreground and which is background\n// No black level compensation for flare.\n\nimport getColor from \"../getColor.js\";\nimport {getLuminance} from \"../luminance.js\";\n\n// the darkest sRGB color above black is #000001 and this produces\n// a plain Weber contrast of ~45647.\n// So, setting the divide-by-zero result at 50000 is a reasonable\n// max clamp for the plain Weber\nconst max = 50000;\n\nexport default function contrastWeber (color1, color2) {\n\tcolor1 = getColor(color1);\n\tcolor2 = getColor(color2);\n\n\tlet Y1 = Math.max(getLuminance(color1), 0);\n\tlet Y2 = Math.max(getLuminance(color2), 0);\n\n\tif (Y2 > Y1) {\n\t\t[Y1, Y2] = [Y2, Y1];\n\t}\n\n\treturn Y2 === 0 ? max : (Y1 - Y2) / Y2;\n}\n", "import xyz_d65 from \"./spaces/xyz-d65.js\";\nimport getAll from \"./getAll.js\";\n\n// Chromaticity coordinates\nexport function uv (color) {\n\t// Assumes getAll() calls getColor() on color\n\tlet [X, Y, Z] = getAll(color, xyz_d65);\n\tlet denom = X + 15 * Y + 3 * Z;\n\treturn [4 * X / denom, 9 * Y / denom];\n}\n\nexport function xy (color) {\n\t// Assumes getAll() calls getColor() on color\n\tlet [X, Y, Z] = getAll(color, xyz_d65);\n\tlet  sum = X + Y + Z;\n\treturn [X / sum, Y / sum];\n}\n\nexport function register (Color) {\n\t// no setters, as lightness information is lost\n\t// when converting color to chromaticity\n\tObject.defineProperty(Color.prototype, \"uv\", {\n\t\tget () {\n\t\t\treturn uv(this);\n\t\t},\n\t});\n\n\tObject.defineProperty(Color.prototype, \"xy\", {\n\t\tget () {\n\t\t\treturn xy(this);\n\t\t},\n\t});\n}\n", "import defaults from \"./defaults.js\";\nimport {isString} from \"./util.js\";\nimport deltaEMethods from \"./deltaE/index.js\";\n\nexport default function deltaE (c1, c2, o = {}) {\n\tif (isString(o)) {\n\t\to = {method: o};\n\t}\n\n\tlet {method = defaults.deltaE, ...rest} = o;\n\n\tfor (let m in deltaEMethods) {\n\t\tif (\"deltae\" + method.toLowerCase() === m.toLowerCase()) {\n\t\t\treturn deltaEMethods[m](c1, c2, rest);\n\t\t}\n\t}\n\n\tthrow new TypeError(`Unknown deltaE method: ${method}`);\n}\n", "import ColorSpace from \"./space.js\";\nimport set from \"./set.js\";\n\nexport function lighten (color, amount = .25) {\n\tlet space = ColorSpace.get(\"oklch\", \"lch\");\n\tlet lightness = [space, \"l\"];\n\treturn set(color, lightness, l => l * (1 + amount));\n}\n\nexport function darken (color, amount = .25) {\n\tlet space = ColorSpace.get(\"oklch\", \"lch\");\n\tlet lightness = [space, \"l\"];\n\treturn set(color, lightness, l => l * (1 - amount));\n}\n", "/**\n * Functions related to color interpolation\n */\nimport ColorSpace from \"./space.js\";\nimport {type, interpolate} from \"./util.js\";\nimport getColor from \"./getColor.js\";\nimport clone from \"./clone.js\";\nimport to from \"./to.js\";\nimport toGamut from \"./toGamut.js\";\nimport get from \"./get.js\";\nimport set from \"./set.js\";\nimport defaults from \"./defaults.js\";\nimport * as angles from \"./angles.js\";\nimport deltaE from \"./deltaE.js\";\n\n/**\n * Return an intermediate color between two colors\n * Signatures: mix(c1, c2, p, options)\n *             mix(c1, c2, options)\n *             mix(color)\n * @param {Color | string} c1 The first color\n * @param {Color | string} [c2] The second color\n * @param {number} [p=.5] A 0-1 percentage where 0 is c1 and 1 is c2\n * @param {Object} [o={}]\n * @return {Color}\n */\nexport function mix (c1, c2, p = .5, o = {}) {\n\t[c1, c2] = [getColor(c1), getColor(c2)];\n\n\tif (type(p) === \"object\") {\n\t\t[p, o] = [.5, p];\n\t}\n\n\tlet r = range(c1, c2, o);\n\treturn r(p);\n}\n\n/**\n *\n * @param {Color | string | Function} c1 The first color or a range\n * @param {Color | string} [c2] The second color if c1 is not a range\n * @param {Object} [options={}]\n * @return {Color[]}\n */\nexport function steps (c1, c2, options = {}) {\n\tlet colorRange;\n\n\tif (isRange(c1)) {\n\t\t// Tweaking existing range\n\t\t[colorRange, options] = [c1, c2];\n\t\t[c1, c2] = colorRange.rangeArgs.colors;\n\t}\n\n\tlet {\n\t\tmaxDeltaE, deltaEMethod,\n\t\tsteps = 2, maxSteps = 1000,\n\t\t...rangeOptions\n\t} = options;\n\n\tif (!colorRange) {\n\t\t[c1, c2] = [getColor(c1), getColor(c2)];\n\t\tcolorRange = range(c1, c2, rangeOptions);\n\t}\n\n\tlet totalDelta = deltaE(c1, c2);\n\tlet actualSteps = maxDeltaE > 0 ? Math.max(steps, Math.ceil(totalDelta / maxDeltaE) + 1) : steps;\n\tlet ret = [];\n\n\tif (maxSteps !== undefined) {\n\t\tactualSteps = Math.min(actualSteps, maxSteps);\n\t}\n\n\tif (actualSteps === 1) {\n\t\tret = [{p: .5, color: colorRange(.5)}];\n\t}\n\telse {\n\t\tlet step = 1 / (actualSteps - 1);\n\t\tret = Array.from({length: actualSteps}, (_, i) => {\n\t\t\tlet p = i * step;\n\t\t\treturn {p, color: colorRange(p)};\n\t\t});\n\t}\n\n\tif (maxDeltaE > 0) {\n\t\t// Iterate over all stops and find max deltaE\n\t\tlet maxDelta = ret.reduce((acc, cur, i) => {\n\t\t\tif (i === 0) {\n\t\t\t\treturn 0;\n\t\t\t}\n\n\t\t\tlet ΔΕ = deltaE(cur.color, ret[i - 1].color, deltaEMethod);\n\t\t\treturn Math.max(acc, ΔΕ);\n\t\t}, 0);\n\n\t\twhile (maxDelta > maxDeltaE) {\n\t\t\t// Insert intermediate stops and measure maxDelta again\n\t\t\t// We need to do this for all pairs, otherwise the midpoint shifts\n\t\t\tmaxDelta = 0;\n\n\t\t\tfor (let i = 1; (i < ret.length) && (ret.length < maxSteps); i++) {\n\t\t\t\tlet prev = ret[i - 1];\n\t\t\t\tlet cur = ret[i];\n\n\t\t\t\tlet p = (cur.p + prev.p) / 2;\n\t\t\t\tlet color = colorRange(p);\n\t\t\t\tmaxDelta = Math.max(maxDelta, deltaE(color, prev.color), deltaE(color, cur.color));\n\t\t\t\tret.splice(i, 0, {p, color: colorRange(p)});\n\t\t\t\ti++;\n\t\t\t}\n\t\t}\n\t}\n\n\tret = ret.map(a => a.color);\n\n\treturn ret;\n}\n\n/**\n * Interpolate to color2 and return a function that takes a 0-1 percentage\n * @param {Color | string | Function} color1 The first color or an existing range\n * @param {Color | string} [color2] If color1 is a color, this is the second color\n * @param {Object} [options={}]\n * @returns {Function} A function that takes a 0-1 percentage and returns a color\n */\nexport function range (color1, color2, options = {}) {\n\tif (isRange(color1)) {\n\t\t// Tweaking existing range\n\t\tlet [r, options] = [color1, color2];\n\n\t\treturn range(...r.rangeArgs.colors, {...r.rangeArgs.options, ...options});\n\t}\n\n\tlet {space, outputSpace, progression, premultiplied} = options;\n\n\tcolor1 = getColor(color1);\n\tcolor2 = getColor(color2);\n\n\t// Make sure we're working on copies of these colors\n\tcolor1 = clone(color1);\n\tcolor2 = clone(color2);\n\n\tlet rangeArgs = {colors: [color1, color2], options};\n\n\tif (space) {\n\t\tspace = ColorSpace.get(space);\n\t}\n\telse {\n\t\tspace = ColorSpace.registry[defaults.interpolationSpace] || color1.space;\n\t}\n\n\toutputSpace = outputSpace ? ColorSpace.get(outputSpace) : space;\n\n\tcolor1 = to(color1, space);\n\tcolor2 = to(color2, space);\n\n\t// Gamut map to avoid areas of flat color\n\tcolor1 = toGamut(color1);\n\tcolor2 = toGamut(color2);\n\n\t// Handle hue interpolation\n\t// See https://github.com/w3c/csswg-drafts/issues/4735#issuecomment-635741840\n\tif (space.coords.h && space.coords.h.type === \"angle\") {\n\t\tlet arc = options.hue = options.hue || \"shorter\";\n\n\t\tlet hue = [space, \"h\"];\n\t\tlet [θ1, θ2] = [get(color1, hue), get(color2, hue)];\n\t\t// Undefined hues must be evaluated before hue fix-up to properly\n\t\t// calculate hue arcs between undefined and defined hues.\n\t\t// See https://github.com/w3c/csswg-drafts/issues/9436#issuecomment-1746957545\n\t\tif (isNaN(θ1) && !isNaN(θ2)) {\n\t\t\tθ1 = θ2;\n\t\t}\n\t\telse if (isNaN(θ2) && !isNaN(θ1)) {\n\t\t\tθ2 = θ1;\n\t\t}\n\t\t[θ1, θ2] = angles.adjust(arc, [θ1, θ2]);\n\t\tset(color1, hue, θ1);\n\t\tset(color2, hue, θ2);\n\t}\n\n\tif (premultiplied) {\n\t\t// not coping with polar spaces yet\n\t\tcolor1.coords = color1.coords.map(c => c * color1.alpha);\n\t\tcolor2.coords = color2.coords.map(c => c * color2.alpha);\n\t}\n\n\treturn Object.assign(p => {\n\t\tp = progression ? progression(p) : p;\n\t\tlet coords = color1.coords.map((start, i) => {\n\t\t\tlet end = color2.coords[i];\n\t\t\treturn interpolate(start, end, p);\n\t\t});\n\n\t\tlet alpha = interpolate(color1.alpha, color2.alpha, p);\n\t\tlet ret = {space, coords, alpha};\n\n\t\tif (premultiplied) {\n\t\t\t// undo premultiplication\n\t\t\tret.coords = ret.coords.map(c => c / alpha);\n\t\t}\n\n\t\tif (outputSpace !== space) {\n\t\t\tret = to(ret, outputSpace);\n\t\t}\n\n\t\treturn ret;\n\t}, {\n\t\trangeArgs,\n\t});\n}\n\nexport function isRange (val) {\n\treturn type(val) === \"function\" && !!val.rangeArgs;\n}\n\ndefaults.interpolationSpace = \"lab\";\n\nexport function register (Color) {\n\tColor.defineFunction(\"mix\", mix, {returns: \"color\"});\n\tColor.defineFunction(\"range\", range, {returns: \"function<color>\"});\n\tColor.defineFunction(\"steps\", steps, {returns: \"array<color>\"});\n}\n", "import ColorSpace from \"../space.js\";\nimport sRGB from \"./srgb.js\";\n\nexport default new ColorSpace({\n\tid: \"hsl\",\n\tname: \"H<PERSON>\",\n\tcoords: {\n\t\th: {\n\t\t\trefRange: [0, 360],\n\t\t\ttype: \"angle\",\n\t\t\tname: \"<PERSON><PERSON>\",\n\t\t},\n\t\ts: {\n\t\t\trange: [0, 100],\n\t\t\tname: \"Saturation\",\n\t\t},\n\t\tl: {\n\t\t\trange: [0, 100],\n\t\t\tname: \"Lightness\",\n\t\t},\n\t},\n\n\tbase: sRGB,\n\n\t// Adapted from https://drafts.csswg.org/css-color-4/better-rgbToHsl.js\n\tfromBase: rgb => {\n\t\tlet max = Math.max(...rgb);\n\t\tlet min = Math.min(...rgb);\n\t\tlet [r, g, b] = rgb;\n\t\tlet [h, s, l] = [NaN, 0, (min + max) / 2];\n\t\tlet d = max - min;\n\n\t\tif (d !== 0) {\n\t\t\ts = (l === 0 || l === 1) ? 0 : (max - l) / Math.min(l, 1 - l);\n\n\t\t\tswitch (max) {\n\t\t\t\tcase r: h = (g - b) / d + (g < b ? 6 : 0); break;\n\t\t\t\tcase g: h = (b - r) / d + 2; break;\n\t\t\t\tcase b: h = (r - g) / d + 4;\n\t\t\t}\n\n\t\t\th = h * 60;\n\t\t}\n\n\t\t// Very out of gamut colors can produce negative saturation\n\t\t// If so, just rotate the hue by 180 and use a positive saturation\n\t\t// see https://github.com/w3c/csswg-drafts/issues/9222\n\t\tif (s < 0) {\n\t\t\th += 180;\n\t\t\ts = Math.abs(s);\n\t\t}\n\n\t\tif (h >= 360) {\n\t\t\th -= 360;\n\t\t}\n\n\t\treturn [h, s * 100, l * 100];\n\t},\n\n\t// Adapted from https://en.wikipedia.org/wiki/HSL_and_HSV#HSL_to_RGB_alternative\n\ttoBase: hsl => {\n\t\tlet [h, s, l] = hsl;\n\t\th = h % 360;\n\n\t\tif (h < 0) {\n\t\t\th += 360;\n\t\t}\n\n\t\ts /= 100;\n\t\tl /= 100;\n\n\t\tfunction f (n) {\n\t\t\tlet k = (n + h / 30) % 12;\n\t\t\tlet a = s * Math.min(l, 1 - l);\n\t\t\treturn l - a * Math.max(-1, Math.min(k - 3, 9 - k, 1));\n\t\t}\n\n\t\treturn [f(0), f(8), f(4)];\n\t},\n\n\tformats: {\n\t\t\"hsl\": {\n\t\t\tcoords: [\"<number> | <angle>\", \"<percentage>\", \"<percentage>\"],\n\t\t},\n\t\t\"hsla\": {\n\t\t\tcoords: [\"<number> | <angle>\", \"<percentage>\", \"<percentage>\"],\n\t\t\tcommas: true,\n\t\t\tlastAlpha: true,\n\t\t},\n\t},\n});\n", "import ColorSpace from \"../space.js\";\nimport HSL from \"./hsl.js\";\n\n// The Hue, Whiteness Blackness (HWB) colorspace\n// See https://drafts.csswg.org/css-color-4/#the-hwb-notation\n// Note that, like HSL, calculations are done directly on\n// gamma-corrected sRGB values rather than linearising them first.\n\nexport default new ColorSpace({\n\tid: \"hsv\",\n\tname: \"HSV\",\n\tcoords: {\n\t\th: {\n\t\t\trefRange: [0, 360],\n\t\t\ttype: \"angle\",\n\t\t\tname: \"Hu<PERSON>\",\n\t\t},\n\t\ts: {\n\t\t\trange: [0, 100],\n\t\t\tname: \"Saturation\",\n\t\t},\n\t\tv: {\n\t\t\trange: [0, 100],\n\t\t\tname: \"Value\",\n\t\t},\n\t},\n\n\tbase: HSL,\n\t// https://en.wikipedia.org/wiki/HSL_and_HSV#Interconversion\n\tfromBase (hsl) {\n\t\tlet [h, s, l] = hsl;\n\t\ts /= 100;\n\t\tl /= 100;\n\n\t\tlet v = l + s * Math.min(l, 1 - l);\n\n\t\treturn [\n\t\t\th, // h is the same\n\t\t\tv === 0 ? 0 : 200 * (1 - l / v), // s\n\t\t\t100 * v,\n\t\t];\n\t},\n\t// https://en.wikipedia.org/wiki/HSL_and_HSV#Interconversion\n\ttoBase (hsv) {\n\t\tlet [h, s, v] = hsv;\n\n\t\ts /= 100;\n\t\tv /= 100;\n\n\t\tlet l = v * (1 - s / 2);\n\n\t\treturn [\n\t\t\th, // h is the same\n\t\t\t(l === 0 || l === 1) ? 0 : ((v - l) / Math.min(l, 1 - l)) * 100,\n\t\t\tl * 100,\n\t\t];\n\t},\n\n\tformats: {\n\t\tcolor: {\n\t\t\tid: \"--hsv\",\n\t\t\tcoords: [\"<number> | <angle>\", \"<percentage> | <number>\", \"<percentage> | <number>\"],\n\t\t},\n\t},\n});\n", "import ColorSpace from \"../space.js\";\nimport HSV from \"./hsv.js\";\n\n// The Hue, Whiteness Blackness (HWB) colorspace\n// See https://drafts.csswg.org/css-color-4/#the-hwb-notation\n// Note that, like HSL, calculations are done directly on\n// gamma-corrected sRGB values rather than linearising them first.\n\nexport default new ColorSpace({\n\tid: \"hwb\",\n\tname: \"HWB\",\n\tcoords: {\n\t\th: {\n\t\t\trefRange: [0, 360],\n\t\t\ttype: \"angle\",\n\t\t\tname: \"Hu<PERSON>\",\n\t\t},\n\t\tw: {\n\t\t\trange: [0, 100],\n\t\t\tname: \"Whiteness\",\n\t\t},\n\t\tb: {\n\t\t\trange: [0, 100],\n\t\t\tname: \"Blackness\",\n\t\t},\n\t},\n\n\tbase: HSV,\n\tfromBase (hsv) {\n\t\tlet [h, s, v] = hsv;\n\n\t\treturn [h, v * (100 - s) / 100, 100 - v];\n\t},\n\ttoBase (hwb) {\n\t\tlet [h, w, b] = hwb;\n\n\t\t// Now convert percentages to [0..1]\n\t\tw /= 100;\n\t\tb /= 100;\n\n\t\t// Achromatic check (white plus black >= 1)\n\t\tlet sum = w + b;\n\t\tif (sum >= 1) {\n\t\t\tlet gray = w / sum;\n\t\t\treturn [h, 0, gray * 100];\n\t\t}\n\n\t\tlet v = (1 - b);\n\t\tlet s = (v === 0) ? 0 : 1 - w / v;\n\t\treturn [h, s * 100, v * 100];\n\t},\n\n\tformats: {\n\t\t\"hwb\": {\n\t\t\tcoords: [\"<number> | <angle>\", \"<percentage> | <number>\", \"<percentage> | <number>\"],\n\t\t},\n\t},\n});\n", "import RGBColorSpace from \"../rgbspace.js\";\n\n// convert an array of linear-light a98-rgb values to CIE XYZ\n// http://www.brucelindbloom.com/index.html?Eqn_RGB_XYZ_Matrix.html\n// has greater numerical precision than section ******* of\n// https://www.adobe.com/digitalimag/pdfs/AdobeRGB1998.pdf\n// but the values below were calculated from first principles\n// from the chromaticity coordinates of R G B W\nconst toXYZ_M = [\n\t[ 0.5766690429101305,   0.1855582379065463,   0.1882286462349947  ],\n\t[ 0.29734497525053605,  0.6273635662554661,   0.07529145849399788 ],\n\t[ 0.02703136138641234,  0.07068885253582723,  0.9913375368376388  ],\n];\n\nconst fromXYZ_M = [\n\t[  2.0415879038107465,    -0.5650069742788596,   -0.34473135077832956 ],\n\t[ -0.9692436362808795,     1.8759675015077202,    0.04155505740717557 ],\n\t[  0.013444280632031142,  -0.11836239223101838,   1.0151749943912054  ],\n];\n\nexport default new RGBColorSpace({\n\tid: \"a98rgb-linear\",\n\tcssId: \"--a98-rgb-linear\",\n\tname: \"Linear Adobe® 98 RGB compatible\",\n\twhite: \"D65\",\n\ttoXYZ_M,\n\tfromXYZ_M,\n});\n", "import RGBColorSpace from \"../rgbspace.js\";\nimport A98Linear from \"./a98rgb-linear.js\";\n\nexport default new RGBColorSpace({\n\tid: \"a98rgb\",\n\tcssId: \"a98-rgb\",\n\tname: \"Adobe® 98 RGB compatible\",\n\tbase: A98Linear,\n\ttoBase: RGB => RGB.map(val => Math.pow(Math.abs(val), 563 / 256) * Math.sign(val)),\n\tfromBase: RGB => RGB.map(val => Math.pow(Math.abs(val), 256 / 563) * Math.sign(val)),\n});\n", "import RGBColorSpace from \"../rgbspace.js\";\nimport XYZ_D50 from \"./xyz-d50.js\";\n\n// convert an array of  prophoto-rgb values to CIE XYZ\n// using  D50 (so no chromatic adaptation needed afterwards)\n// matrix cannot be expressed in rational form, but is calculated to 64 bit accuracy\n// see https://github.com/w3c/csswg-drafts/issues/7675\nconst toXYZ_M = [\n\t[ 0.79776664490064230,  0.13518129740053308,  0.03134773412839220 ],\n\t[ 0.28807482881940130,  0.71183523424187300,  0.00008993693872564 ],\n\t[ 0.00000000000000000,  0.00000000000000000,  0.82510460251046020 ],\n];\n\nconst fromXYZ_M = [\n\t[  1.34578688164715830, -0.25557208737979464, -0.05110186497554526 ],\n\t[ -0.54463070512490190,  1.50824774284514680,  0.02052744743642139 ],\n\t[  0.00000000000000000,  0.00000000000000000,  1.21196754563894520 ],\n];\n\nexport default new RGBColorSpace({\n\tid: \"prophoto-linear\",\n\tcssId: \"--prophoto-rgb-linear\",\n\tname: \"Linear ProPhoto\",\n\twhite: \"D50\",\n\tbase: XYZ_D50,\n\ttoXYZ_M,\n\tfromXYZ_M,\n});\n", "import RGBColorSpace from \"../rgbspace.js\";\nimport ProPhotoLinear from \"./prophoto-linear.js\";\n\nconst Et = 1 / 512;\nconst Et2 = 16 / 512;\n\nexport default new RGBColorSpace({\n\tid: \"prophoto\",\n\tcssId: \"prophoto-rgb\",\n\tname: \"ProPhoto\",\n\tbase: ProPhotoLinear,\n\ttoBase (RGB) {\n\t\t// Transfer curve is gamma 1.8 with a small linear portion\n\t\treturn RGB.map(v => v < Et2 ? v / 16 : v ** 1.8);\n\t},\n\tfromBase (RGB) {\n\t\treturn RGB.map(v => v >= Et ? v ** (1 / 1.8) : 16 * v);\n\t},\n});\n", "import ColorSpace from \"../space.js\";\nimport OKLab from \"./oklab.js\";\nimport {constrain as constrainAngle} from \"../angles.js\";\n\nexport default new ColorSpace({\n\tid: \"oklch\",\n\tname: \"Oklch\",\n\tcoords: {\n\t\tl: {\n\t\t\trefRange: [0, 1],\n\t\t\tname: \"Lightness\",\n\t\t},\n\t\tc: {\n\t\t\trefRange: [0, 0.4],\n\t\t\tname: \"Chroma\",\n\t\t},\n\t\th: {\n\t\t\trefRange: [0, 360],\n\t\t\ttype: \"angle\",\n\t\t\tname: \"<PERSON><PERSON>\",\n\t\t},\n\t},\n\twhite: \"D65\",\n\n\tbase: OKLab,\n\tfromBase (oklab) {\n\t\t// Convert to polar form\n\t\tlet [L, a, b] = oklab;\n\t\tlet h;\n\t\tconst ε = 0.0002; // chromatic components much smaller than a,b\n\n\t\tif (Math.abs(a) < ε && Math.abs(b) < ε) {\n\t\t\th = NaN;\n\t\t}\n\t\telse {\n\t\t\th = Math.atan2(b, a) * 180 / Math.PI;\n\t\t}\n\n\t\treturn [\n\t\t\tL, // OKLab L is still L\n\t\t\tMath.sqrt(a ** 2 + b ** 2), // Chroma\n\t\t\tconstrainAngle(h), // Hue, in degrees [0 to 360)\n\t\t];\n\t},\n\t// Convert from polar form\n\ttoBase (oklch) {\n\t\tlet [L, C, h] = oklch;\n\t\tlet a, b;\n\n\t\t// check for NaN hue\n\t\tif (isNaN(h)) {\n\t\t\ta = 0;\n\t\t\tb = 0;\n\t\t}\n\t\telse {\n\t\t\ta = C * Math.cos(h * Math.PI / 180);\n\t\t\tb = C * Math.sin(h * Math.PI / 180);\n\t\t}\n\n\t\treturn [ L, a, b ];\n\t},\n\n\tformats: {\n\t\t\"oklch\": {\n\t\t\tcoords: [\"<percentage> | <number>\", \"<number> | <percentage>[0,1]\", \"<number> | <angle>\"],\n\t\t},\n\t},\n});\n", "import ColorSpace from \"../space.js\";\nimport {WHITES} from \"../adapt.js\";\nimport xyz_d65 from \"./xyz-d65.js\";\nimport {uv} from \"../chromaticity.js\";\nimport {isNone, skipNone} from \"../util.js\";\n\nlet white = WHITES.D65;\n\nconst ε = 216 / 24389;  // 6^3/29^3 == (24/116)^3\nconst κ = 24389 / 27;   // 29^3/3^3\nconst [U_PRIME_WHITE, V_PRIME_WHITE] = uv({space: xyz_d65, coords: white});\n\nexport default new ColorSpace({\n\tid: \"luv\",\n\tname: \"Luv\",\n\tcoords: {\n\t\tl: {\n\t\t\trefRange: [0, 100],\n\t\t\tname: \"Lightness\",\n\t\t},\n\t\t// Reference ranges from https://facelessuser.github.io/coloraide/colors/luv/\n\t\tu: {\n\t\t\trefRange: [-215, 215],\n\t\t},\n\t\tv: {\n\t\t\trefRange: [-215, 215],\n\t\t},\n\t},\n\n\twhite: white,\n\tbase: xyz_d65,\n\n\t// Convert D65-adapted XYZ to Luv\n\t// https://en.wikipedia.org/wiki/CIELUV#The_forward_transformation\n\tfromBase (XYZ) {\n\t\tlet xyz = [skipNone(XYZ[0]), skipNone(XYZ[1]), skipNone(XYZ[2])];\n\t\tlet y = xyz[1];\n\n\t\tlet [up, vp] = uv({space: xyz_d65, coords: xyz});\n\n\t\t// Protect against XYZ of [0, 0, 0]\n\t\tif (!Number.isFinite(up) || !Number.isFinite(vp)) {\n\t\t\treturn [0, 0, 0];\n\t\t}\n\n\t\tlet L = y <= ε ? κ * y : 116 * Math.cbrt(y) - 16;\n\t\treturn [\n\t\t\tL,\n\t\t\t13 * L * (up - U_PRIME_WHITE),\n\t\t\t13 * L * (vp - V_PRIME_WHITE),\n\t\t ];\n\t},\n\n\t// Convert Luv to D65-adapted XYZ\n\t// https://en.wikipedia.org/wiki/CIELUV#The_reverse_transformation\n\ttoBase (Luv) {\n\t\tlet [L, u, v] = Luv;\n\n\t\t// Protect against division by zero and NaN Lightness\n\t\tif (L === 0 || isNone(L)) {\n\t\t\treturn [0, 0, 0];\n\t\t}\n\n\t\tu = skipNone(u);\n\t\tv = skipNone(v);\n\n\t\tlet up = (u / (13 * L)) + U_PRIME_WHITE;\n\t\tlet vp = (v / (13 * L)) + V_PRIME_WHITE;\n\n\t\tlet y = L <= 8 ? L / κ : Math.pow((L + 16) / 116, 3);\n\n\t\treturn [\n\t\t\ty * ((9 * up) / (4 * vp)),\n\t\t\ty,\n\t\t\ty * ((12 - 3 * up - 20 * vp) / (4 * vp)),\n\t\t];\n\t},\n\n\tformats: {\n\t\tcolor: {\n\t\t\tid: \"--luv\",\n\t\t\tcoords: [\"<number> | <percentage>\", \"<number> | <percentage>[-1,1]\", \"<number> | <percentage>[-1,1]\"],\n\t\t},\n\t},\n});\n", "import ColorSpace from \"../space.js\";\nimport Luv from \"./luv.js\";\nimport {constrain as constrainAngle} from \"../angles.js\";\n\nexport default new ColorSpace({\n\tid: \"lchuv\",\n\tname: \"<PERSON>Chuv\",\n\tcoords: {\n\t\tl: {\n\t\t\trefRange: [0, 100],\n\t\t\tname: \"Lightness\",\n\t\t},\n\t\tc: {\n\t\t\trefRange: [0, 220],\n\t\t\tname: \"Chroma\",\n\t\t},\n\t\th: {\n\t\t\trefRange: [0, 360],\n\t\t\ttype: \"angle\",\n\t\t\tname: \"<PERSON><PERSON>\",\n\t\t},\n\t},\n\n\tbase: Luv,\n\tfromBase (Luv) {\n\t\t// Convert to polar form\n\t\tlet [L, u, v] = Luv;\n\t\tlet hue;\n\t\tconst ε = 0.02;\n\n\t\tif (Math.abs(u) < ε && Math.abs(v) < ε) {\n\t\t\thue = NaN;\n\t\t}\n\t\telse {\n\t\t\thue = Math.atan2(v, u) * 180 / Math.PI;\n\t\t}\n\n\t\treturn [\n\t\t\tL, // L is still L\n\t\t\tMath.sqrt(u ** 2 + v ** 2), // Chroma\n\t\t\tconstrainAngle(hue), // Hue, in degrees [0 to 360)\n\t\t];\n\t},\n\ttoBase (LCH) {\n\t\t// Convert from polar form\n\t\tlet [Lightness, Chroma, Hue] = LCH;\n\t\t// Clamp any negative Chroma\n\t\tif (Chroma < 0) {\n\t\t\tChroma = 0;\n\t\t}\n\t\t// Deal with NaN Hue\n\t\tif (isNaN(Hue)) {\n\t\t\tHue = 0;\n\t\t}\n\t\treturn [\n\t\t\tLightness, // L is still L\n\t\t\tChroma * Math.cos(Hue * Math.PI / 180), // u\n\t\t\tChroma * Math.sin(Hue * Math.PI / 180),  // v\n\t\t];\n\t},\n\n\tformats: {\n\t\tcolor: {\n\t\t\tid: \"--lchuv\",\n\t\t\tcoords: [\"<number> | <percentage>\", \"<number> | <percentage>\", \"<number> | <angle>\"],\n\t\t},\n\t},\n});\n", "/*\nAdapted from: https://github.com/hsluv/hsluv-javascript/blob/14b49e6cf9a9137916096b8487a5372626b57ba4/src/hsluv.ts\n\nCopyright (c) 2012-2022 Alexei <PERSON>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n*/\n\nimport ColorSpace from \"../space.js\";\nimport LCHuv from \"./lchuv.js\";\nimport sRGB from \"./srgb.js\";\nimport {fromXYZ_M} from \"./srgb-linear.js\";\nimport {skipNone} from \"../util.js\";\n\nconst ε = 216 / 24389;  // 6^3/29^3 == (24/116)^3\nconst κ = 24389 / 27;   // 29^3/3^3\n\nconst m_r0 = fromXYZ_M[0][0];\nconst m_r1 = fromXYZ_M[0][1];\nconst m_r2 = fromXYZ_M[0][2];\nconst m_g0 = fromXYZ_M[1][0];\nconst m_g1 = fromXYZ_M[1][1];\nconst m_g2 = fromXYZ_M[1][2];\nconst m_b0 = fromXYZ_M[2][0];\nconst m_b1 = fromXYZ_M[2][1];\nconst m_b2 = fromXYZ_M[2][2];\n\nfunction distanceFromOriginAngle (slope, intercept, angle) {\n\tconst d = intercept / (Math.sin(angle) - slope * Math.cos(angle));\n\treturn d < 0 ? Infinity : d;\n}\n\nexport function calculateBoundingLines (l) {\n\tconst sub1 = Math.pow(l + 16, 3) / 1560896;\n\tconst sub2 = sub1 > ε ? sub1 : l / κ;\n\tconst s1r = sub2 * (284517 * m_r0 - 94839 * m_r2);\n\tconst s2r = sub2 * (838422 * m_r2 + 769860 * m_r1 + 731718 * m_r0);\n\tconst s3r = sub2 * (632260 * m_r2 - 126452 * m_r1);\n\tconst s1g = sub2 * (284517 * m_g0 - 94839 * m_g2);\n\tconst s2g = sub2 * (838422 * m_g2 + 769860 * m_g1 + 731718 * m_g0);\n\tconst s3g = sub2 * (632260 * m_g2 - 126452 * m_g1);\n\tconst s1b = sub2 * (284517 * m_b0 - 94839 * m_b2);\n\tconst s2b = sub2 * (838422 * m_b2 + 769860 * m_b1 + 731718 * m_b0);\n\tconst s3b = sub2 * (632260 * m_b2 - 126452 * m_b1);\n\n\treturn {\n\t\tr0s: s1r / s3r,\n\t\tr0i: s2r * l / s3r,\n\t\tr1s: s1r / (s3r + 126452),\n\t\tr1i: (s2r - 769860) * l / (s3r + 126452),\n\t\tg0s: s1g / s3g,\n\t\tg0i: s2g * l / s3g,\n\t\tg1s: s1g / (s3g + 126452),\n\t\tg1i: (s2g - 769860) * l / (s3g + 126452),\n\t\tb0s: s1b / s3b,\n\t\tb0i: s2b * l / s3b,\n\t\tb1s: s1b / (s3b + 126452),\n\t\tb1i: (s2b - 769860) * l / (s3b + 126452),\n\t};\n}\n\nfunction calcMaxChromaHsluv (lines, h) {\n\tconst hueRad = h / 360 * Math.PI * 2;\n\tconst r0 = distanceFromOriginAngle(lines.r0s, lines.r0i, hueRad);\n\tconst r1 = distanceFromOriginAngle(lines.r1s, lines.r1i, hueRad);\n\tconst g0 = distanceFromOriginAngle(lines.g0s, lines.g0i, hueRad);\n\tconst g1 = distanceFromOriginAngle(lines.g1s, lines.g1i, hueRad);\n\tconst b0 = distanceFromOriginAngle(lines.b0s, lines.b0i, hueRad);\n\tconst b1 = distanceFromOriginAngle(lines.b1s, lines.b1i, hueRad);\n\n\treturn Math.min(r0, r1, g0, g1, b0, b1);\n}\n\nexport default new ColorSpace({\n\tid: \"hsluv\",\n\tname: \"HSLuv\",\n\tcoords: {\n\t\th: {\n\t\t\trefRange: [0, 360],\n\t\t\ttype: \"angle\",\n\t\t\tname: \"Hue\",\n\t\t},\n\t\ts: {\n\t\t\trange: [0, 100],\n\t\t\tname: \"Saturation\",\n\t\t},\n\t\tl: {\n\t\t\trange: [0, 100],\n\t\t\tname: \"Lightness\",\n\t\t},\n\t},\n\n\tbase: LCHuv,\n\tgamutSpace: sRGB,\n\n\t// Convert LCHuv to HSLuv\n\tfromBase (lch) {\n\t\tlet [l, c, h] = [skipNone(lch[0]), skipNone(lch[1]), skipNone(lch[2])];\n\t\tlet s;\n\n\t\tif (l > 99.9999999) {\n\t\t\ts = 0;\n\t\t\tl = 100;\n\t\t}\n\t\telse if (l < 0.00000001) {\n\t\t\ts = 0;\n\t\t\tl = 0;\n\t\t}\n\t\telse {\n\t\t\tlet lines = calculateBoundingLines(l);\n\t\t\tlet max = calcMaxChromaHsluv(lines, h);\n\t\t\ts = c / max * 100;\n\t\t}\n\n\t\treturn [h, s, l];\n\t},\n\n\t// Convert HSLuv to LCHuv\n\ttoBase (hsl) {\n\t\tlet [h, s, l] = [skipNone(hsl[0]), skipNone(hsl[1]), skipNone(hsl[2])];\n\t\tlet c;\n\n\t\tif (l > 99.9999999) {\n\t\t\tl = 100;\n\t\t\tc = 0;\n\t\t}\n\t\telse if (l < 0.00000001) {\n\t\t\tl = 0;\n\t\t\tc = 0;\n\t\t}\n\t\telse {\n\t\t\tlet lines = calculateBoundingLines(l);\n\t\t\tlet max = calcMaxChromaHsluv(lines, h);\n\t\t\tc = max / 100 * s;\n\t\t}\n\n\t\treturn [l, c, h];\n\t},\n\n\tformats: {\n\t\tcolor: {\n\t\t\tid: \"--hsluv\",\n\t\t\tcoords: [\"<number> | <angle>\", \"<percentage> | <number>\", \"<percentage> | <number>\"],\n\t\t},\n\t},\n});\n", "/*\nAdapted from: https://github.com/hsluv/hsluv-javascript/blob/14b49e6cf9a9137916096b8487a5372626b57ba4/src/hsluv.ts\n\nCopyright (c) 2012-2022 Alexei <PERSON>\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n*/\n\nimport ColorSpace from \"../space.js\";\nimport LCHuv from \"./lchuv.js\";\nimport {fromXYZ_M} from \"./srgb-linear.js\";\nimport {skipNone} from \"../util.js\";\nimport {calculateBoundingLines} from \"./hsluv.js\";\n\nconst ε = 216 / 24389;  // 6^3/29^3 == (24/116)^3\nconst κ = 24389 / 27;   // 29^3/3^3\n\nconst m_r0 = fromXYZ_M[0][0];\nconst m_r1 = fromXYZ_M[0][1];\nconst m_r2 = fromXYZ_M[0][2];\nconst m_g0 = fromXYZ_M[1][0];\nconst m_g1 = fromXYZ_M[1][1];\nconst m_g2 = fromXYZ_M[1][2];\nconst m_b0 = fromXYZ_M[2][0];\nconst m_b1 = fromXYZ_M[2][1];\nconst m_b2 = fromXYZ_M[2][2];\n\nfunction distanceFromOrigin (slope, intercept) {\n\treturn Math.abs(intercept) / Math.sqrt(Math.pow(slope, 2) + 1);\n}\n\nfunction calcMaxChromaHpluv (lines) {\n\tlet r0 = distanceFromOrigin(lines.r0s, lines.r0i);\n\tlet r1 = distanceFromOrigin(lines.r1s, lines.r1i);\n\tlet g0 = distanceFromOrigin(lines.g0s, lines.g0i);\n\tlet g1 = distanceFromOrigin(lines.g1s, lines.g1i);\n\tlet b0 = distanceFromOrigin(lines.b0s, lines.b0i);\n\tlet b1 = distanceFromOrigin(lines.b1s, lines.b1i);\n\n\treturn Math.min(r0, r1, g0, g1, b0, b1);\n}\n\nexport default new ColorSpace({\n\tid: \"hpluv\",\n\tname: \"HPLuv\",\n\tcoords: {\n\t\th: {\n\t\t\trefRange: [0, 360],\n\t\t\ttype: \"angle\",\n\t\t\tname: \"Hue\",\n\t\t},\n\t\ts: {\n\t\t\trange: [0, 100],\n\t\t\tname: \"Saturation\",\n\t\t},\n\t\tl: {\n\t\t\trange: [0, 100],\n\t\t\tname: \"Lightness\",\n\t\t},\n\t},\n\n\tbase: LCHuv,\n\tgamutSpace: \"self\",\n\n\t// Convert LCHuv to HPLuv\n\tfromBase (lch) {\n\t\tlet [l, c, h] = [skipNone(lch[0]), skipNone(lch[1]), skipNone(lch[2])];\n\t\tlet s;\n\n\t\tif (l > 99.9999999) {\n\t\t\ts = 0;\n\t\t\tl = 100;\n\t\t}\n\t\telse if (l < 0.00000001) {\n\t\t\ts = 0;\n\t\t\tl = 0;\n\t\t}\n\t\telse {\n\t\t\tlet lines = calculateBoundingLines(l);\n\t\t\tlet max = calcMaxChromaHpluv(lines);\n\t\t\ts = c / max * 100;\n\t\t}\n\t\treturn [h, s, l];\n\t},\n\n\t// Convert HPLuv to LCHuv\n\ttoBase (hsl) {\n\t\tlet [h, s, l] = [skipNone(hsl[0]), skipNone(hsl[1]), skipNone(hsl[2])];\n\t\tlet c;\n\n\t\tif (l > 99.9999999) {\n\t\t\tl = 100;\n\t\t\tc = 0;\n\t\t}\n\t\telse if (l < 0.00000001) {\n\t\t\tl = 0;\n\t\t\tc = 0;\n\t\t}\n\t\telse {\n\t\t\tlet lines = calculateBoundingLines(l);\n\t\t\tlet max = calcMaxChromaHpluv(lines, h);\n\t\t\tc = max / 100 * s;\n\t\t}\n\n\t\treturn [l, c, h];\n\t},\n\n\tformats: {\n\t\tcolor: {\n\t\t\tid: \"--hpluv\",\n\t\t\tcoords: [\"<number> | <angle>\", \"<percentage> | <number>\", \"<percentage> | <number>\"],\n\t\t},\n\t},\n});\n", "import RGBColorSpace from \"../rgbspace.js\";\nimport REC2020Linear from \"./rec2020-linear.js\";\n\nconst Yw = 203;\t// absolute luminance of media white, cd/m²\nconst n = 2610 / (2 ** 14);\nconst ninv = (2 ** 14) / 2610;\nconst m = 2523 / (2 ** 5);\nconst minv = (2 ** 5) / 2523;\nconst c1 = 3424 / (2 ** 12);\nconst c2 = 2413 / (2 ** 7);\nconst c3 = 2392 / (2 ** 7);\n\nexport default new RGBColorSpace({\n\tid: \"rec2100pq\",\n\tcssId: \"rec2100-pq\",\n\tname: \"REC.2100-PQ\",\n\tbase: REC2020Linear,\n\ttoBase (RGB) {\n\t\t// given PQ encoded component in range [0, 1]\n\t\t// return media-white relative linear-light\n\t\treturn RGB.map(function (val) {\n\t\t\tlet x = ((Math.max(((val ** minv) - c1), 0) / (c2 - (c3 * (val ** minv)))) ** ninv);\n\t\t\treturn (x * 10000 / Yw); \t// luminance relative to diffuse white, [0, 70 or so].\n\t\t});\n\t},\n\tfromBase (RGB) {\n\t\t// given media-white relative linear-light\n\t\t// returnPQ encoded component in range [0, 1]\n\t\treturn RGB.map(function (val) {\n\t\t\tlet x = Math.max(val * Yw / 10000, 0); \t// absolute luminance of peak white is 10,000 cd/m².\n\t\t\tlet num = (c1 + (c2 * (x ** n)));\n\t\t\tlet denom = (1 + (c3 * (x ** n)));\n\n\t\t\treturn ((num / denom)  ** m);\n\t\t});\n\t},\n});\n", "import RGBColorSpace from \"../rgbspace.js\";\nimport REC2020Linear from \"./rec2020-linear.js\";\n\n// FIXME see https://github.com/LeaVerou/color.js/issues/190\n\nconst a = 0.17883277;\nconst b = 0.28466892; // 1 - (4 * a)\nconst c = 0.55991073; // 0.5 - a * Math.log(4 *a)\n\nconst scale = 3.7743;\t// Place 18% grey at HLG 0.38, so media white at 0.75\n\nexport default new RGBColorSpace({\n\tid: \"rec2100hlg\",\n\tcssId: \"rec2100-hlg\",\n\tname: \"REC.2100-HLG\",\n\treferred: \"scene\",\n\n\tbase: REC2020Linear,\n\ttoBase (RGB) {\n\t\t// given HLG encoded component in range [0, 1]\n\t\t// return media-white relative linear-light\n\t\treturn RGB.map(function (val) {\n\t\t\t// first the HLG EOTF\n\t\t\t// ITU-R BT.2390-10 p.30 section\n\t\t\t// 6.3 The hybrid log-gamma electro-optical transfer function (EOTF)\n\t\t\t// Then scale by 3 so media white is 1.0\n\t\t\tif (val <= 0.5) {\n\t\t\t\treturn (val ** 2) / 3 * scale;\n\t\t\t}\n\t\t\treturn ((Math.exp((val - c) / a) + b) / 12) * scale;\n\t\t});\n\t},\n\tfromBase (RGB) {\n\t\t// given media-white relative linear-light\n\t\t// where diffuse white is 1.0,\n\t\t// return HLG encoded component in range [0, 1]\n\t\treturn RGB.map(function (val) {\n\t\t\t// first scale to put linear-light media white at 1/3\n\t\t\tval /= scale;\n\t\t\t// now the HLG OETF\n\t\t\t// ITU-R BT.2390-10 p.23\n\t\t\t// 6.1 The hybrid log-gamma opto-electronic transfer function (OETF)\n\t\t\tif (val <= 1 / 12) {\n\t\t\t\treturn Math.sqrt(3 * val);\n\t\t\t}\n\t\t\treturn a * Math.log(12 * val - b) + c;\n\t\t});\n\t},\n});\n", "import hooks from \"./hooks.js\";\nimport {multiplyMatrices} from \"./util.js\";\nimport {WHITES} from \"./adapt.js\";\n\nexport const CATs = {};\n\nhooks.add(\"chromatic-adaptation-start\", env => {\n\tif (env.options.method) {\n\t\tenv.M = adapt(env.W1, env.W2, env.options.method);\n\t}\n});\n\nhooks.add(\"chromatic-adaptation-end\", env => {\n\tif (!env.M) {\n\t\tenv.M = adapt(env.W1, env.W2, env.options.method);\n\t}\n});\n\nexport function defineCAT ({id, toCone_M, fromCone_M}) {\n\t// Use id, toCone_M, fromCone_M like variables\n\tCATs[id] = arguments[0];\n}\n\nexport function adapt (W1, W2, id = \"Bradford\") {\n\t// adapt from a source whitepoint or illuminant W1\n\t// to a destination whitepoint or illuminant W2,\n\t// using the given chromatic adaptation transform (CAT)\n\t// debugger;\n\tlet method = CATs[id];\n\n\tlet [ρs, γs, βs] = multiplyMatrices(method.toCone_M, W1);\n\tlet [ρd, γd, βd] = multiplyMatrices(method.toCone_M, W2);\n\n\t// all practical illuminants have non-zero XYZ so no division by zero can occur below\n\tlet scale = [\n\t\t[ρd / ρs,  0,        0      ],\n\t\t[0,        γd / γs,  0      ],\n\t\t[0,        0,        βd / βs],\n\t];\n\t// console.log({scale});\n\n\tlet scaled_cone_M = multiplyMatrices(scale, method.toCone_M);\n\tlet adapt_M\t= multiplyMatrices(method.fromCone_M, scaled_cone_M);\n\t// console.log({scaled_cone_M, adapt_M});\n\treturn adapt_M;\n}\n\ndefineCAT({\n\tid: \"von Kries\",\n\ttoCone_M: [\n\t\t[  0.4002400,  0.7076000, -0.0808100 ],\n\t\t[ -0.2263000,  1.1653200,  0.0457000 ],\n\t\t[  0.0000000,  0.0000000,  0.9182200 ],\n\t],\n\tfromCone_M: [\n\t\t[ 1.8599363874558397, -1.1293816185800916,   0.21989740959619328     ],\n\t\t[ 0.3611914362417676,  0.6388124632850422,  -0.000006370596838649899 ],\n\t\t[ 0,                   0,                    1.0890636230968613      ],\n\t],\n});\n\ndefineCAT({\n\tid: \"Bradford\",\n\t// Convert an array of XYZ values in the range 0.0 - 1.0\n\t// to cone fundamentals\n\ttoCone_M: [\n\t\t[  0.8951000,  0.2664000, -0.1614000 ],\n\t\t[ -0.7502000,  1.7135000,  0.0367000 ],\n\t\t[  0.0389000, -0.0685000,  1.0296000 ],\n\t],\n\t// and back\n\tfromCone_M: [\n\t\t[  0.9869929054667121, -0.14705425642099013, 0.15996265166373122  ],\n\t\t[  0.4323052697233945,  0.5183602715367774,  0.049291228212855594 ],\n\t\t[ -0.00852866457517732, 0.04004282165408486, 0.96848669578755     ],\n\t],\n});\n\ndefineCAT({\n\tid: \"CAT02\",\n\t// with complete chromatic adaptation to W2, so D = 1.0\n\ttoCone_M: [\n\t\t[  0.7328000,  0.4296000, -0.1624000 ],\n\t\t[ -0.7036000,  1.6975000,  0.0061000 ],\n\t\t[  0.0030000,  0.0136000,  0.9834000 ],\n\t],\n\tfromCone_M: [\n\t\t[  1.0961238208355142,   -0.27886900021828726, 0.18274517938277307 ],\n\t\t[  0.4543690419753592,    0.4735331543074117,  0.07209780371722911 ],\n\t\t[ -0.009627608738429355, -0.00569803121611342, 1.0153256399545427  ],\n\t],\n});\n\ndefineCAT({\n\tid: \"CAT16\",\n\ttoCone_M: [\n\t\t[  0.401288,  0.650173, -0.051461 ],\n\t\t[ -0.250268,  1.204414,  0.045854 ],\n\t\t[ -0.002079,  0.048952,  0.953127 ],\n\t],\n\t// the extra precision is needed to avoid roundtripping errors\n\tfromCone_M: [\n\t\t[  1.862067855087233,   -1.0112546305316845,  0.14918677544445172  ],\n\t\t[  0.3875265432361372,   0.6214474419314753, -0.008973985167612521 ],\n\t\t[ -0.01584149884933386, -0.03412293802851557, 1.0499644368778496   ],\n\t],\n});\n\nObject.assign(WHITES, {\n\t// whitepoint values from ASTM E308-01 with 10nm spacing, 1931 2 degree observer\n\t// all normalized to Y (luminance) = 1.00000\n\t// Illuminant A is a tungsten electric light, giving a very warm, orange light.\n\tA:   [1.09850, 1.00000, 0.35585],\n\n\t// Illuminant C was an early approximation to daylight: illuminant A with a blue filter.\n\tC:   [0.98074, 1.000000, 1.18232],\n\n\t// The daylight series of illuminants simulate natural daylight.\n\t// The color temperature (in degrees Kelvin/100) ranges from\n\t// cool, overcast daylight (D50) to bright, direct sunlight (D65).\n\tD55: [0.95682, 1.00000, 0.92149],\n\tD75: [0.94972, 1.00000, 1.22638],\n\n\t// Equal-energy illuminant, used in two-stage CAT16\n\tE:   [1.00000, 1.00000, 1.00000],\n\n\t// The F series of illuminants represent fluorescent lights\n\tF2:  [0.99186, 1.00000, 0.67393],\n\tF7:  [0.95041, 1.00000, 1.08747],\n\tF11: [1.00962, 1.00000, 0.64350],\n});\n", "import RGBColorSpace from \"../rgbspace.js\";\nimport {WHITES} from \"../adapt.js\";\nimport \"../CATs.js\"; // because of the funky whitepoint\n\n// The ACES whitepoint\n// see TB-2018-001 Derivation of the ACES White Point CIE Chromaticity Coordinates\n// also https://github.com/ampas/aces-dev/blob/master/documents/python/TB-2018-001/aces_wp.py\n// Similar to D60\nWHITES.ACES = [0.32168 / 0.33767, 1.00000, (1.00000 - 0.32168 - 0.33767) / 0.33767];\n\n// convert an array of linear-light ACEScc values to CIE XYZ\nconst toXYZ_M = [\n\t[  0.6624541811085053,   0.13400420645643313,  0.1561876870049078  ],\n\t[  0.27222871678091454,  0.6740817658111484,   0.05368951740793705 ],\n\t[ -0.005574649490394108, 0.004060733528982826, 1.0103391003129971  ],\n];\nconst fromXYZ_M = [\n\t[  1.6410233796943257,   -0.32480329418479,    -0.23642469523761225  ],\n\t[ -0.6636628587229829,    1.6153315916573379,   0.016756347685530137 ],\n\t[  0.011721894328375376, -0.008284441996237409, 0.9883948585390215   ],\n];\n\nexport default new RGBColorSpace({\n\tid: \"acescg\",\n\tcssId: \"--acescg\",\n\tname: \"ACEScg\",\n\n\t// ACEScg – A scene-referred, linear-light encoding of ACES Data\n\t// https://docs.acescentral.com/specifications/acescg/\n\t// uses the AP1 primaries, see section 4.3.1 Color primaries\n\tcoords: {\n\t\tr: {\n\t\t\trange: [0, 65504],\n\t\t\tname: \"Red\",\n\t\t},\n\t\tg: {\n\t\t\trange: [0, 65504],\n\t\t\tname: \"Green\",\n\t\t},\n\t\tb: {\n\t\t\trange: [0, 65504],\n\t\t\tname: \"Blue\",\n\t\t},\n\t},\n\n\treferred: \"scene\",\n\n\twhite: WHITES.ACES,\n\n\ttoXYZ_M,\n\tfromXYZ_M,\n});\n\n// export default Color;\n", "import RGBColorSpace from \"../rgbspace.js\";\nimport \"../CATs.js\"; // because of the funky whitepoint\nimport ACEScg from \"./acescg.js\";\n\nconst ε = 2 ** -16;\n\n// the smallest value which, in the 32bit IEEE 754 float encoding,\n// decodes as a non-negative value\nconst ACES_min_nonzero = -0.35828683;\n\n// brightest encoded value, decodes to 65504\nconst ACES_cc_max = (Math.log2(65504) + 9.72) / 17.52; // 1.468\n\nexport default new RGBColorSpace({\n\tid: \"acescc\",\n\tcssId: \"--acescc\",\n\tname: \"ACEScc\",\n\t// see S-2014-003 ACEScc – A Logarithmic Encoding of ACES Data\n\t// https://docs.acescentral.com/specifications/acescc/\n\t// uses the AP1 primaries, see section 4.3.1 Color primaries\n\n\t// Appendix A: \"Very small ACES scene referred values below 7 1/4 stops\n\t// below 18% middle gray are encoded as negative ACEScc values.\n\t// These values should be preserved per the encoding in Section 4.4\n\t// so that all positive ACES values are maintained.\"\n\tcoords: {\n\t\tr: {\n\t\t\trange: [ACES_min_nonzero, ACES_cc_max],\n\t\t\tname: \"Red\",\n\t\t},\n\t\tg: {\n\t\t\trange: [ACES_min_nonzero, ACES_cc_max],\n\t\t\tname: \"Green\",\n\t\t},\n\t\tb: {\n\t\t\trange: [ACES_min_nonzero, ACES_cc_max],\n\t\t\tname: \"Blue\",\n\t\t},\n\t},\n\treferred: \"scene\",\n\n\tbase: ACEScg,\n\t// from section 4.4.2 Decoding Function\n\ttoBase (RGB) {\n\t\tconst low = (9.72 - 15) / 17.52; // -0.3014\n\n\t\treturn RGB.map(function (val) {\n\t\t\tif (val <= low) {\n\t\t\t\treturn (2 ** ((val * 17.52) - 9.72) - ε) * 2; // very low values, below -0.3014\n\t\t\t}\n\t\t\telse if (val < ACES_cc_max) {\n\t\t\t\treturn 2 ** ((val * 17.52) - 9.72);\n\t\t\t}\n\t\t\telse { // val >= ACES_cc_max\n\t\t\t\treturn 65504;\n\t\t\t}\n\t\t});\n\t},\n\n\t// Non-linear encoding function from S-2014-003, section 4.4.1 Encoding Function\n\tfromBase (RGB) {\n\t\treturn RGB.map(function (val) {\n\t\t\tif (val <= 0) {\n\t\t\t\treturn (Math.log2(ε) + 9.72) / 17.52; // -0.3584\n\t\t\t}\n\t\t\telse if (val < ε) {\n\t\t\t\treturn  (Math.log2(ε + val * 0.5) + 9.72) / 17.52;\n\t\t\t}\n\t\t\telse { // val >= ε\n\t\t\t\treturn  (Math.log2(val) + 9.72) / 17.52;\n\t\t\t}\n\t\t});\n\t},\n\t// encoded media white (rgb 1,1,1) => linear  [ 222.861, 222.861, 222.861 ]\n\t// encoded media black (rgb 0,0,0) => linear [ 0.0011857, 0.0011857, 0.0011857]\n});\n", "import * as util from \"./util.js\";\nimport hooks from \"./hooks.js\";\nimport defaults from \"./defaults.js\";\nimport ColorSpace from \"./space.js\";\nimport {WHITES} from \"./adapt.js\";\nimport {\n\tgetColor,\n\tparse,\n\tto,\n\tserialize,\n\tinGamut,\n\ttoGamut,\n\tdistance,\n\tequals,\n\tget,\n\tgetAll,\n\tset,\n\tsetAll,\n\tdisplay,\n} from \"./index-fn.js\";\n\n\nimport \"./spaces/xyz-d50.js\";\nimport \"./spaces/srgb.js\";\n\n/**\n * Class that represents a color\n */\nexport default class Color {\n\t/**\n\t * Creates an instance of Color.\n\t * Signatures:\n\t * - `new Color(stringToParse)`\n\t * - `new Color(otherColor)`\n\t * - `new Color({space, coords, alpha})`\n\t * - `new Color(space, coords, alpha)`\n\t * - `new Color(spaceId, coords, alpha)`\n\t */\n\tconstructor (...args) {\n\t\tlet color;\n\n\t\tif (args.length === 1) {\n\t\t\tcolor = getColor(args[0]);\n\t\t}\n\n\t\tlet space, coords, alpha;\n\n\t\tif (color) {\n\t\t\tspace = color.space || color.spaceId;\n\t\t\tcoords = color.coords;\n\t\t\talpha = color.alpha;\n\t\t}\n\t\telse {\n\t\t\t// default signature new Color(ColorSpace, array [, alpha])\n\t\t\t[space, coords, alpha] = args;\n\t\t}\n\n\t\tObject.defineProperty(this, \"space\", {\n\t\t\tvalue: ColorSpace.get(space),\n\t\t\twritable: false,\n\t\t\tenumerable: true,\n\t\t\tconfigurable: true, // see note in https://262.ecma-international.org/8.0/#sec-proxy-object-internal-methods-and-internal-slots-get-p-receiver\n\t\t});\n\n\t\tthis.coords = coords ? coords.slice() : [0, 0, 0];\n\n\t\t// Clamp alpha to [0, 1]\n\t\tthis.alpha = alpha > 1 || alpha === undefined ? 1 : (alpha < 0 ? 0 : alpha);\n\n\t\t// Convert \"NaN\" to NaN\n\t\tfor (let i = 0; i < this.coords.length; i++) {\n\t\t\tif (this.coords[i] === \"NaN\") {\n\t\t\t\tthis.coords[i] = NaN;\n\t\t\t}\n\t\t}\n\n\t\t// Define getters and setters for each coordinate\n\t\tfor (let id in this.space.coords) {\n\t\t\tObject.defineProperty(this, id, {\n\t\t\t\tget: () => this.get(id),\n\t\t\t\tset: value => this.set(id, value),\n\t\t\t});\n\t\t}\n\t}\n\n\tget spaceId () {\n\t\treturn this.space.id;\n\t}\n\n\tclone () {\n\t\treturn new Color(this.space, this.coords, this.alpha);\n\t}\n\n\ttoJSON () {\n\t\treturn {\n\t\t\tspaceId: this.spaceId,\n\t\t\tcoords: this.coords,\n\t\t\talpha: this.alpha,\n\t\t};\n\t}\n\n\tdisplay (...args) {\n\t\tlet ret = display(this, ...args);\n\n\t\t// Convert color object to Color instance\n\t\tret.color = new Color(ret.color);\n\n\t\treturn ret;\n\t}\n\n\t/**\n\t * Get a color from the argument passed\n\t * Basically gets us the same result as new Color(color) but doesn't clone an existing color object\n\t */\n\tstatic get (color, ...args) {\n\t\tif (color instanceof Color) {\n\t\t\treturn color;\n\t\t}\n\n\t\treturn new Color(color, ...args);\n\t}\n\n\tstatic defineFunction (name, code, o = code) {\n\t\tlet {instance = true, returns} = o;\n\n\t\tlet func = function (...args) {\n\t\t\tlet ret = code(...args);\n\n\t\t\tif (returns === \"color\") {\n\t\t\t\tret = Color.get(ret);\n\t\t\t}\n\t\t\telse if (returns === \"function<color>\") {\n\t\t\t\tlet f = ret;\n\t\t\t\tret = function (...args) {\n\t\t\t\t\tlet ret = f(...args);\n\t\t\t\t\treturn Color.get(ret);\n\t\t\t\t};\n\t\t\t\t// Copy any function metadata\n\t\t\t\tObject.assign(ret, f);\n\t\t\t}\n\t\t\telse if (returns === \"array<color>\") {\n\t\t\t\tret = ret.map(c => Color.get(c));\n\t\t\t}\n\n\t\t\treturn ret;\n\t\t};\n\n\t\tif (!(name in Color)) {\n\t\t\tColor[name] = func;\n\t\t}\n\n\t\tif (instance) {\n\t\t\tColor.prototype[name] = function (...args) {\n\t\t\t\treturn func(this, ...args);\n\t\t\t};\n\t\t}\n\t}\n\n\tstatic defineFunctions (o) {\n\t\tfor (let name in o) {\n\t\t\tColor.defineFunction(name, o[name], o[name]);\n\t\t}\n\t}\n\n\tstatic extend (exports) {\n\t\tif (exports.register) {\n\t\t\texports.register(Color);\n\t\t}\n\t\telse {\n\t\t\t// No register method, just add the module's functions\n\t\t\tfor (let name in exports) {\n\t\t\t\tColor.defineFunction(name, exports[name]);\n\t\t\t}\n\t\t}\n\t}\n}\n\nColor.defineFunctions({\n\tget,\n\tgetAll,\n\tset,\n\tsetAll,\n\tto,\n\tequals,\n\tinGamut,\n\ttoGamut,\n\tdistance,\n\ttoString: serialize,\n});\n\nObject.assign(Color, {\n\tutil,\n\thooks,\n\tWHITES,\n\tSpace: ColorSpace,\n\tspaces: ColorSpace.registry,\n\tparse,\n\n\t// Global defaults one may want to configure\n\tdefaults,\n});\n", "import getColor from \"./getColor.js\";\n\nexport default function equals (color1, color2) {\n\tcolor1 = getColor(color1);\n\tcolor2 = getColor(color2);\n\n\treturn color1.space === color2.space\n\t       && color1.alpha === color2.alpha\n\t       && color1.coords.every((c, i) => c === color2.coords[i]);\n}\n", "import ColorSpace from \"../space.js\";\nimport * as spaces from \"./index-fn.js\";\n\nexport * as spaces from \"./index-fn.js\";\n\nfor (let key of Object.keys(spaces)) {\n\tColorSpace.register(spaces[key]);\n}\n", "'use strict';\nvar defineProperty = require('../internals/object-define-property').f;\nvar hasOwn = require('../internals/has-own-property');\nvar wellKnownSymbol = require('../internals/well-known-symbol');\n\nvar TO_STRING_TAG = wellKnownSymbol('toStringTag');\n\nmodule.exports = function (target, TAG, STATIC) {\n  if (target && !STATIC) target = target.prototype;\n  if (target && !hasOwn(target, TO_STRING_TAG)) {\n    defineProperty(target, TO_STRING_TAG, { configurable: true, value: TAG });\n  }\n};\n", "'use strict';\nvar $ = require('../internals/export');\nvar global = require('../internals/global');\nvar setToStringTag = require('../internals/set-to-string-tag');\n\n$({ global: true }, { Reflect: {} });\n\n// Reflect[@@toStringTag] property\n// https://tc39.es/ecma262/#sec-reflect-@@tostringtag\nsetToStringTag(global.Reflect, 'Reflect', true);\n", "/**\n * This plugin defines getters and setters for color[spaceId]\n * e.g. color.lch on *any* color gives us the lch coords\n */\nimport ColorSpace from \"./space.js\";\nimport Color from \"./color.js\";\nimport hooks from \"./hooks.js\";\n\n// Add space accessors to existing color spaces\nfor (let id in ColorSpace.registry) {\n\taddSpaceAccessors(id, ColorSpace.registry[id]);\n}\n\n// Add space accessors to color spaces not yet created\nhooks.add(\"colorspace-init-end\", space => {\n\taddSpaceAccessors(space.id, space);\n\tspace.aliases?.forEach(alias => {\n\t\taddSpaceAccessors(alias, space);\n\t});\n});\n\nfunction addSpaceAccessors (id, space) {\n\tlet propId = id.replace(/-/g, \"_\");\n\n\tObject.defineProperty(Color.prototype, propId, {\n\t\t// Convert coords to coords in another colorspace and return them\n\t\t// Source colorspace: this.spaceId\n\t\t// Target colorspace: id\n\t\tget () {\n\t\t\tlet ret = this.getAll(id);\n\n\t\t\tif (typeof Proxy === \"undefined\") {\n\t\t\t\t// If proxies are not supported, just return a static array\n\t\t\t\treturn ret;\n\t\t\t}\n\n\t\t\t// Enable color.spaceId.coordName syntax\n\t\t\treturn new Proxy(ret, {\n\t\t\t\thas: (obj, property) => {\n\t\t\t\t\ttry {\n\t\t\t\t\t\tColorSpace.resolveCoord([space, property]);\n\t\t\t\t\t\treturn true;\n\t\t\t\t\t}\n\t\t\t\t\tcatch (e) {}\n\n\t\t\t\t\treturn Reflect.has(obj, property);\n\t\t\t\t},\n\t\t\t\tget: (obj, property, receiver) => {\n\t\t\t\t\tif (property && typeof property !== \"symbol\" && !(property in obj)) {\n\t\t\t\t\t\tlet {index} = ColorSpace.resolveCoord([space, property]);\n\n\t\t\t\t\t\tif (index >= 0) {\n\t\t\t\t\t\t\treturn obj[index];\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\treturn Reflect.get(obj, property, receiver);\n\t\t\t\t},\n\t\t\t\tset: (obj, property, value, receiver) => {\n\t\t\t\t\tif (property && typeof property !== \"symbol\" && !(property in obj) || property >= 0) {\n\t\t\t\t\t\tlet {index} = ColorSpace.resolveCoord([space, property]);\n\n\t\t\t\t\t\tif (index >= 0) {\n\t\t\t\t\t\t\tobj[index] = value;\n\n\t\t\t\t\t\t\t// Update color.coords\n\t\t\t\t\t\t\tthis.setAll(id, obj);\n\n\t\t\t\t\t\t\treturn true;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\treturn Reflect.set(obj, property, value, receiver);\n\t\t\t\t},\n\t\t\t});\n\t\t},\n\t\t// Convert coords in another colorspace to internal coords and set them\n\t\t// Target colorspace: this.spaceId\n\t\t// Source colorspace: id\n\t\tset (coords) {\n\t\t\tthis.setAll(id, coords);\n\t\t},\n\t\tconfigurable: true,\n\t\tenumerable: true,\n\t});\n}\n", "// Import all modules of Color.js\nimport Color from \"./color.js\";\n\n// Import all color spaces\nimport \"./spaces/index.js\";\n\n// Import all DeltaE methods\nimport deltaE from \"./deltaE.js\";\nimport deltaEMethods from \"./deltaE/index.js\";\n\nColor.extend(deltaEMethods);\nColor.extend({deltaE});\nObject.assign(Color, {deltaEMethods});\n\n// Import optional modules\nimport * as variations from \"./variations.js\";\nColor.extend(variations);\n\nimport contrast from \"./contrast.js\";\nColor.extend({contrast});\n\nimport * as chromaticity from \"./chromaticity.js\";\nColor.extend(chromaticity);\n\nimport * as luminance from \"./luminance.js\";\nColor.extend(luminance);\n\nimport * as interpolation from \"./interpolation.js\";\nColor.extend(interpolation);\n\nimport * as contrastMethods from \"./contrast/index.js\";\nColor.extend(contrastMethods);\n\nimport \"./CATs.js\";\nimport \"./space-accessors.js\";\n\n// Re-export everything\nexport default Color;\n", "import getColor from \"./getColor.js\";\n// import defaults from \"./defaults.js\";\nimport {isString} from \"./util.js\";\nimport * as contrastAlgorithms from \"./contrast/index.js\";\n\nexport default function contrast (background, foreground, o = {}) {\n\tif (isString(o)) {\n\t\to = {algorithm: o};\n\t}\n\n\tlet {algorithm, ...rest} = o;\n\n\tif (!algorithm) {\n\t\tlet algorithms = Object.keys(contrastAlgorithms).map(a => a.replace(/^contrast/, \"\")).join(\", \");\n\t\tthrow new TypeError(`contrast() function needs a contrast algorithm. Please specify one of: ${algorithms}`);\n\t}\n\n\tbackground = getColor(background);\n\tforeground = getColor(foreground);\n\n\tfor (let a in contrastAlgorithms) {\n\t\tif (\"contrast\" + algorithm.toLowerCase() === a.toLowerCase()) {\n\t\t\treturn contrastAlgorithms[a](background, foreground, rest);\n\t\t}\n\t}\n\n\tthrow new TypeError(`Unknown contrast algorithm: ${algorithm}`);\n}\n"], "names": ["check", "it", "Math", "global", "globalThis", "window", "self", "this", "Function", "fails", "exec", "error", "require$$0", "descriptors", "Object", "defineProperty", "get", "functionBindNative", "test", "bind", "hasOwnProperty", "NATIVE_BIND", "call", "prototype", "functionCall", "apply", "arguments", "createPropertyDescriptor", "bitmap", "value", "enumerable", "configurable", "writable", "FunctionPrototype", "uncurryThisWithBind", "functionUncurryThis", "fn", "uncurryThis", "toString", "stringSlice", "slice", "classofRaw", "isNullOrUndefined", "$TypeError", "TypeError", "requireObjectCoercible", "IndexedObject", "require$$1", "classof", "require$$2", "$Object", "split", "indexedObject", "propertyIsEnumerable", "toIndexedObject", "documentAll", "document", "all", "isCallable", "undefined", "argument", "isObject", "getBuiltIn", "namespace", "method", "length", "objectIsPrototypeOf", "isPrototypeOf", "match", "version", "userAgent", "engineUserAgent", "navigator", "String", "process", "<PERSON><PERSON>", "versions", "v8", "engineV8Version", "V8_VERSION", "$String", "symbolConstructorDetection", "getOwnPropertySymbols", "symbol", "Symbol", "sham", "NATIVE_SYMBOL", "useSymbolAsUid", "iterator", "USE_SYMBOL_AS_UID", "require$$3", "isSymbol", "$Symbol", "tryToString", "aCallable", "getMethod", "V", "P", "func", "ordinaryToPrimitive", "input", "pref", "val", "valueOf", "isPure", "defineGlobalProperty", "key", "IS_PURE", "SHARED", "store", "sharedStoreModule", "exports", "push", "mode", "copyright", "license", "source", "shared", "toObject", "hasOwnProperty_1", "hasOwn", "id", "postfix", "random", "uid", "require$$4", "require$$5", "WellKnownSymbolsStore", "createWellKnownSymbol", "withoutSetter", "wellKnownSymbol", "name", "TO_PRIMITIVE", "toPrimitive", "result", "exoticToPrim", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "DESCRIPTORS", "createElement", "EXISTS", "documentCreateElement", "ie8DomDefine", "a", "propertyIsEnumerableModule", "$propertyIsEnumerable", "getOwnPropertyDescriptor", "NASHORN_BUG", "objectPropertyIsEnumerable", "f", "descriptor", "require$$6", "IE8_DOM_DEFINE", "require$$7", "$getOwnPropertyDescriptor", "objectGetOwnPropertyDescriptor", "O", "anObject", "V8_PROTOTYPE_DEFINE_BUG", "v8PrototypeDefineBug", "$defineProperty", "ENUMERABLE", "CONFIGURABLE", "WRITABLE", "objectDefineProperty", "Attributes", "current", "definePropertyModule", "createNonEnumerableProperty", "object", "functionToString", "inspectSource", "keys", "sharedKey", "hiddenKeys", "set", "has", "NATIVE_WEAK_MAP", "WeakMap", "weakMapBasicDetection", "OBJECT_ALREADY_INITIALIZED", "state", "metadata", "facade", "STATE", "internalState", "enforce", "getter<PERSON>or", "TYPE", "type", "CONFIGURABLE_FUNCTION_NAME", "getDescriptor", "PROPER", "functionName", "InternalStateModule", "enforceInternalState", "getInternalState", "replace", "join", "CONFIGURABLE_LENGTH", "TEMPLATE", "makeBuiltIn", "makeBuiltInModule", "options", "getter", "setter", "arity", "constructor", "defineBuiltIn", "simple", "unsafe", "nonConfigurable", "nonWritable", "trunc", "ceil", "floor", "math<PERSON>runc", "x", "n", "toIntegerOrInfinity", "number", "max", "min", "toAbsoluteIndex", "index", "integer", "to<PERSON><PERSON><PERSON>", "len", "lengthOfArrayLike", "obj", "indexOf", "createMethod", "IS_INCLUDES", "$this", "el", "fromIndex", "arrayIncludes", "includes", "objectKeysInternal", "names", "i", "getOwnPropertyNamesModule", "internalObjectKeys", "enumBugKeys", "concat", "objectGetOwnPropertyNames", "getOwnPropertyNames", "getOwnPropertySymbolsModule", "objectGetOwnPropertySymbols", "ownKeys", "getOwnPropertyDescriptorModule", "copyConstructorProperties", "target", "exceptions", "isForced", "replacement", "feature", "detection", "data", "normalize", "POLYFILL", "NATIVE", "string", "toLowerCase", "isForced_1", "_export", "targetProperty", "sourceProperty", "TARGET", "GLOBAL", "STATIC", "stat", "dontCallGetSet", "forced", "isArray", "Array", "SILENT_ON_NON_WRITABLE_LENGTH_SET", "arraySetLength", "doesNotExceedSafeInteger", "multiplyMatrices", "A", "B", "m", "map", "p", "B_cols", "_", "product", "row", "col", "ret", "c", "isString", "str", "o", "serializeNumber", "_ref", "precision", "unit", "isNone", "toPrecision", "Number", "isNaN", "none", "<PERSON><PERSON><PERSON>", "digits", "log10", "abs", "multiplier", "$", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "proto", "properErrorOnNonWritableLength", "item", "argCount", "angleFactor", "deg", "grad", "rad", "PI", "turn", "parseFunction", "trim", "isNumberRegex", "unitValueRegex", "singleArgument", "parts", "args", "$0", "rawArg", "arg", "unitlessArg", "NaN", "startsWith", "alpha", "raw", "rawName", "rawArgs", "last", "arr", "interpolate", "start", "end", "interpolateInv", "mapRange", "from", "to", "parseCoordGrammar", "coordGrammars", "coordGrammar", "range", "clamp", "copySign", "sign", "spow", "base", "exp", "zdiv", "d", "bisectLeft", "lo", "hi", "mid", "hooks", "add", "callback", "first", "for<PERSON>ach", "run", "env", "context", "defaults", "gamut_mapping", "deltaE", "verbose", "_globalThis$process", "NODE_ENV", "warn", "msg", "_globalThis$console", "_globalThis$console$w", "console", "isPossiblePrototype", "aPossiblePrototype", "uncurry<PERSON><PERSON><PERSON><PERSON><PERSON>or", "functionUncurryThisAccessor", "objectSetPrototypeOf", "setPrototypeOf", "CORRECT_SETTER", "__proto__", "proxyAccessor", "Target", "Source", "inheritIfRequired", "dummy", "Wrapper", "<PERSON><PERSON><PERSON><PERSON>", "NewTargetPrototype", "TO_STRING_TAG_SUPPORT", "toStringTagSupport", "TO_STRING_TAG", "CORRECT_ARGUMENTS", "tag", "tryGet", "callee", "normalizeStringArgument", "$default", "installErrorCause", "cause", "clearErrorStack", "$Error", "Error", "TEST", "stack", "V8_OR_CHAKRA_STACK_ENTRY", "IS_V8_OR_CHAKRA_STACK", "errorStackClear", "dropEntries", "prepareStackTrace", "ERROR_STACK_INSTALLABLE", "errorStackInstallable", "captureStackTrace", "errorStackInstall", "C", "require$$8", "require$$9", "installErrorStack", "require$$10", "require$$11", "require$$12", "wrapErrorConstructorWithCause", "FULL_NAME", "wrapper", "FORCED", "IS_AGGREGATE_ERROR", "STACK_TRACE_LIMIT", "OPTIONS_POSITION", "path", "ERROR_NAME", "OriginalError", "OriginalErrorPrototype", "BaseError", "WrappedError", "b", "message", "functionApply", "Reflect", "WEB_ASSEMBLY", "WebAssembly", "exportGlobalErrorCauseWrapper", "exportWebAssemblyErrorCauseWrapper", "init", "WHITES", "D50", "D65", "<PERSON><PERSON><PERSON><PERSON>", "adapt", "W1", "W2", "XYZ", "M", "noneTypes", "Set", "coerceCoords", "space", "format", "coords", "types", "entries", "coordMeta", "providedType", "find", "coordName", "fromRange", "to<PERSON><PERSON><PERSON>", "refRange", "util", "parse", "_String", "meta", "color", "parsed", "shift", "alternateId", "substring", "ids", "pop", "ColorSpace", "colorSpec", "getFormat", "_colorSpec$ids", "filter", "specId", "assign", "formatId", "spaceId", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "registryId", "registry", "_ColorSpace$registry$", "cssId", "formats", "lastAlpha", "_color$alpha", "getColor", "ε", "_options$coords", "_options$white", "_options$formats", "_this$formats$color", "aliases", "fromBase", "toBase", "white", "_this$formats$color2", "gamutSpace", "isPolar", "isUnbounded", "inGamut", "referred", "<PERSON><PERSON><PERSON>", "reverse", "epsilon", "equals", "values", "every", "coord", "_this$formats", "processFormat", "connectionSpace", "connectionSpaceIndex", "myPath", "otherPath", "getMinCoords", "_range$min", "static", "register", "alias", "_len", "alternatives", "_key", "resolveCoord", "ref", "workingSpace", "coordType", "coordId", "normalizedCoord", "_meta$name", "s", "coordFormats", "_ref2", "outputType", "suffix", "serializeCoords", "xyz_d65", "y", "z", "RGBColorSpace", "_options$referred", "_options$toBase", "_options$fromBase", "r", "g", "XYZ_D65", "toXYZ_M", "fromXYZ_M", "rgb", "xyz", "super", "getAll", "prop", "setAll", "returns", "XYZ_D50", "ε3", "κ", "lab", "l", "xyz_d50", "cbrt", "Lab", "pow", "constrain", "angle", "lch", "h", "hue", "L", "atan2", "sqrt", "constrainAngle", "LCH", "Lightness", "Chroma", "<PERSON><PERSON>", "cos", "sin", "Gfactor", "π", "r2d", "d2r", "pow7", "x2", "deltaE2000", "sample", "kL", "kC", "kH", "L1", "a1", "b1", "C1", "L2", "a2", "b2", "C2", "C7", "G", "adash1", "adash2", "Cdash1", "Cdash2", "h1", "h2", "Δh", "ΔL", "ΔC", "hdiff", "hsum", "habs", "hdash", "ΔH", "Ldash", "Cdash", "Cdash7", "lsq", "SL", "SC", "T", "SH", "Δθ", "RC", "dE", "XYZtoLMS_M", "LMStoXYZ_M", "LMStoLab_M", "LabtoLMS_M", "OKLab", "LMSg", "LMS", "oklab", "deltaEOK", "Δa", "Δb", "clone", "distance", "color1", "color2", "coords1", "coords2", "reduce", "acc", "c1", "c2", "XYZ_Abs_D65", "v", "AbsXYZ", "c3", "pinv", "d0", "XYZtoCone_M", "ConetoXYZ_M", "ConetoIab_M", "IabtoCone_M", "Jzazbz", "jz", "az", "bz", "Xa", "Ya", "<PERSON>a", "PQLMS", "Iz", "Jz", "Xm", "Ym", "jzczhz", "cz", "hz", "jzazbz", "m1", "m2", "im1", "im2", "LMStoIPT_M", "IPTtoLMS_M", "ictcp", "ct", "cp", "LMStoICtCp", "ICtCp", "ICtCptoLMS", "<PERSON><PERSON><PERSON><PERSON>", "adaptedCoefInv", "tau", "cat16", "cat16Inv", "surroundMap", "dark", "dim", "average", "hueQuadMap", "e", "H", "rad2deg", "deg2rad", "fl", "temp", "environment", "refWhite", "adaptingLuminance", "backgroundLuminance", "surround", "discounting", "xyzW", "la", "yb", "yw", "rgbW", "nc", "k4", "flRoot", "nbb", "ncb", "dRgb", "dRgbInv", "rgbCW", "rgbAW", "aW", "viewingConditions", "fromCam16", "cam16", "J", "Q", "hRad", "Hp", "hii", "ei", "eii", "invHueQuadrature", "cosh", "sinh", "<PERSON><PERSON>", "t", "et", "p1", "p2", "rgb_c", "adapted", "constant", "cabs", "unadapt", "toCam16", "xyzd65", "xyz100", "rgbA", "hp", "hueQuadrature", "j", "fromLstar", "lstar", "toHct", "hct", "attempt", "Infinity", "delta", "fromHct", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "convertUcsAb", "log", "hrad", "deltaEMethods", "deltaE76", "deltaECMC", "H1", "H2", "C4", "F", "deltaEJz", "Jz1", "Cz1", "Hz1", "Jz2", "Cz2", "Hz2", "ΔJ", "deltaEITP", "I1", "T1", "P1", "I2", "T2", "P2", "deltaEHCT", "t1", "t2", "GMAPPRESET", "jnd", "deltaEMethod", "blackWhiteClamp", "channel", "toGamut", "spaceColor", "origin", "JND", "oklchSpace", "origin_OKLCH", "COLORS", "WHITE", "black", "BLACK", "clip", "_color", "destColor", "spaceCoords", "min_inGamut", "clipped", "E", "chroma", "toGamutCSS", "de", "channelMeta", "mapSpace", "mappedColor", "order", "parseFloat", "calcEpsilon", "low", "high", "bounds", "deletePropertyOrThrow", "serialize", "_color$space$getForma", "customOptions", "DEFAULT_FORMAT", "checkInGamut", "_format$ids", "unshift", "strAlpha", "noAlpha", "commas", "k", "REC2020Linear", "α", "β", "REC2020", "RGB", "P3Linear", "sRGBLinear", "KEYWORDS", "aliceblue", "antiquewhite", "aqua", "aquamarine", "azure", "beige", "bisque", "blanche<PERSON><PERSON>", "blue", "blueviolet", "brown", "burlywood", "cadetblue", "chartreuse", "chocolate", "coral", "cornflowerblue", "cornsilk", "crimson", "cyan", "darkblue", "dark<PERSON>an", "darkgoldenrod", "darkgray", "darkgreen", "<PERSON><PERSON>rey", "<PERSON><PERSON><PERSON>", "darkmagenta", "darkolivegreen", "darkorange", "darkorchid", "darkred", "<PERSON><PERSON><PERSON>", "darkseagreen", "darkslateblue", "darkslategray", "darkslateg<PERSON>", "darkturquoise", "darkviolet", "deeppink", "deepskyblue", "dimgray", "<PERSON><PERSON><PERSON>", "dodgerblue", "firebrick", "<PERSON><PERSON><PERSON><PERSON>", "forestgreen", "fuchsia", "gainsboro", "ghostwhite", "gold", "goldenrod", "gray", "green", "greenyellow", "grey", "honeydew", "hotpink", "indianred", "indigo", "ivory", "khaki", "lavender", "lavenderblush", "lawngreen", "lemon<PERSON>ffon", "lightblue", "lightcoral", "lightcyan", "lightgoldenrodyellow", "lightgray", "lightgreen", "<PERSON><PERSON>rey", "lightpink", "<PERSON><PERSON><PERSON>", "lightseagreen", "lightskyblue", "lightslategray", "lightslategrey", "lightsteelblue", "lightyellow", "lime", "limegreen", "linen", "magenta", "maroon", "mediumaquamarine", "mediumblue", "mediumorchid", "mediumpurple", "mediumseagreen", "mediumslateblue", "mediumspringgreen", "mediumturquoise", "mediumvioletred", "midnightblue", "mintcream", "mistyrose", "moccasin", "navajowhite", "navy", "oldlace", "olive", "<PERSON><PERSON><PERSON>", "orange", "orangered", "orchid", "palegoldenrod", "palegreen", "paleturquoise", "palevioletred", "papayawhip", "peachpuff", "peru", "pink", "plum", "powderblue", "purple", "rebeccapurple", "red", "rosybrown", "royalblue", "saddlebrown", "salmon", "sandybrown", "seagreen", "seashell", "sienna", "silver", "skyblue", "slateblue", "slategray", "<PERSON><PERSON><PERSON>", "snow", "springgreen", "steelblue", "tan", "teal", "thistle", "tomato", "turquoise", "violet", "wheat", "whitesmoke", "yellow", "yellowgreen", "fill", "coordGrammarNumber", "sRGB", "rgb_number", "rgba", "rgba_number", "hex", "component", "parseInt", "collapse", "round", "collapsible", "padStart", "keyword", "P3", "supportsNone", "display_space", "CSS", "supports", "getLuminance", "setLuminance", "Color", "blkThrs", "blkClmp", "fclamp", "Y", "linearize", "lab_d65", "phi", "background", "foreground", "S", "Sapc", "R", "lumTxt", "lumBg", "Ytxt", "Ybg", "BoW", "Lstr1", "Lstr2", "deltaPhiStar", "contrast", "SQRT2", "Y1", "Y2", "denom", "uv", "X", "Z", "xy", "sum", "rest", "amount", "mix", "steps", "colorRange", "isRange", "rangeArgs", "colors", "maxDeltaE", "maxSteps", "rangeOptions", "totalDelta", "actualSteps", "step", "max<PERSON><PERSON><PERSON>", "cur", "ΔΕ", "prev", "splice", "outputSpace", "progression", "premultiplied", "interpolationSpace", "arc", "θ1", "θ2", "angles", "angleDiff", "defineFunction", "HSL", "hsl", "hsla", "HSV", "hsv", "hwb", "w", "A98Linear", "a98rgb", "ProPhotoLinear", "prophoto", "oklch", "U_PRIME_WHITE", "V_PRIME_WHITE", "<PERSON><PERSON>", "u", "up", "vp", "isFinite", "LCHuv", "m_r0", "m_r1", "m_r2", "m_g0", "m_g1", "m_g2", "m_b0", "m_b1", "m_b2", "distanceFromOriginAngle", "slope", "intercept", "calculateBoundingLines", "sub1", "sub2", "s1r", "s2r", "s3r", "s1g", "s2g", "s3g", "s1b", "s2b", "s3b", "r0s", "r0i", "r1s", "r1i", "g0s", "g0i", "g1s", "g1i", "b0s", "b0i", "b1s", "b1i", "calcMaxChromaHsluv", "lines", "hueRad", "r0", "r1", "g0", "g1", "b0", "hsluv", "distanceFromOrigin", "calcMaxChromaHpluv", "hpluv", "minv", "rec2100Pq", "scale", "rec2100Hlg", "CATs", "defineCAT", "toCone_M", "fromCone_M", "ρs", "γs", "βs", "ρd", "γd", "βd", "scaled_cone_M", "D55", "D75", "F2", "F7", "F11", "ACES", "ACEScg", "ACES_min_nonzero", "ACES_cc_max", "log2", "acescc", "toJSON", "display", "_len2", "_key2", "fallbackColor", "_supportsNone", "some", "_len3", "_key3", "code", "instance", "_len4", "_key4", "defineFunctions", "extend", "Space", "spaces", "setToStringTag", "TAG", "addSpaceAccessors", "propId", "Proxy", "property", "receiver", "_space$aliases", "variations", "algorithm", "algorithms", "contrastAlgorithms", "chromaticity", "luminance", "interpolation", "contrastMethods"], "mappings": "iQACA,IAAIA,EAAQ,SAAUC,GACpB,OAAOA,GAAMA,EAAGC,OAASA,MAAQD,CACnC,SAGcE,EAEZH,EAA2B,iBAAdI,YAA0BA,aACvCJ,EAAuB,iBAAVK,QAAsBA,SAEnCL,EAAqB,iBAARM,MAAoBA,OACjCN,EAAuB,iBAAVG,GAAsBA,IACnCH,EAAqB,iBAARO,GAAoBA,IAEjC,WAAe,OAAOA,IAAO,CAA7B,IAAoCC,SAAS,cAATA,yDCdxBC,EAAG,SAAUC,GACzB,IACE,QAASA,GACV,CAAC,MAAOC,GACP,OAAO,CACR,mCCLH,IAAIF,EAAQG,WAGZC,GAAkBJ,GAAM,WAEtB,OAA+E,IAAxEK,OAAOC,eAAe,GAAI,EAAG,CAAEC,IAAK,WAAc,OAAO,CAAI,IAAI,EAC1E,mCCNA,IAAIP,EAAQG,WAEZK,GAAkBR,GAAM,WAEtB,IAAIS,EAAO,WAA4B,EAAEC,OAEzC,MAAsB,mBAARD,GAAsBA,EAAKE,eAAe,YAC1D,mCCPA,IAAIC,EAAcT,IAEdU,EAAOd,SAASe,UAAUD,YAEhBE,EAAGH,EAAcC,EAAKH,KAAKG,GAAQ,WAC/C,OAAOA,EAAKG,MAAMH,EAAMI,6ICL1BC,EAAiB,SAAUC,EAAQC,GACjC,MAAO,CACLC,aAAuB,EAATF,GACdG,eAAyB,EAATH,GAChBI,WAAqB,EAATJ,GACZC,MAAOA,qCCLX,IAAIR,EAAcT,IAEdqB,EAAoBzB,SAASe,UAC7BD,EAAOW,EAAkBX,KACzBY,EAAsBb,GAAeY,EAAkBd,KAAKA,KAAKG,EAAMA,UAE3Ea,EAAiBd,EAAca,EAAsB,SAAUE,GAC7D,OAAO,WACL,OAAOd,EAAKG,MAAMW,EAAIV,UAC1B,qCCTA,IAAIW,EAAczB,KAEd0B,EAAWD,EAAY,CAAE,EAACC,UAC1BC,EAAcF,EAAY,GAAGG,cAEnBC,EAAG,SAAUxC,GACzB,OAAOsC,EAAYD,EAASrC,GAAK,GAAI,kCCJzByC,EAAG,SAAUzC,GACzB,OAAOA,0CCHT,IAAIyC,EAAoB9B,KAEpB+B,EAAaC,iBAIHC,EAAG,SAAU5C,GACzB,GAAIyC,EAAkBzC,GAAK,MAAM,IAAI0C,EAAW,wBAA0B1C,GAC1E,OAAOA,mCCPT,IAAI6C,+BCDJ,IAAIT,EAAczB,KACdH,EAAQsC,IACRC,EAAUC,KAEVC,EAAUpC,OACVqC,EAAQd,EAAY,GAAGc,cAGbC,EAAG3C,GAAM,WAGrB,OAAQyC,EAAQ,KAAKG,qBAAqB,EAC5C,IAAK,SAAUpD,GACb,MAAuB,WAAhB+C,EAAQ/C,GAAmBkD,EAAMlD,EAAI,IAAMiD,EAAQjD,EAC3D,EAAGiD,EDbgBtC,GAChBiC,EAAyBE,YAEfO,EAAG,SAAUrD,GACzB,OAAO6C,EAAcD,EAAuB5C,qCEJ9C,IAAIsD,EAAiC,iBAAZC,UAAwBA,SAASC,WAK1DC,OAAuC,IAAfH,QAA8CI,IAAhBJ,EAA4B,SAAUK,GAC1F,MAA0B,mBAAZA,GAA0BA,IAAaL,CACtD,EAAG,SAAUK,GACZ,MAA0B,mBAAZA,mCCThB,IAAIF,EAAa9C,YAEHiD,EAAG,SAAU5D,GACzB,MAAoB,iBAANA,EAAwB,OAAPA,EAAcyD,EAAWzD,oCCH1D,IAAIE,EAASS,IACT8C,EAAaX,YAMjBe,EAAiB,SAAUC,EAAWC,GACpC,OAAOtC,UAAUuC,OAAS,GALFL,EAKgBzD,EAAO4D,GAJxCL,EAAWE,GAAYA,OAAWD,GAIoBxD,EAAO4D,IAAc5D,EAAO4D,GAAWC,GALtF,IAAUJ,qCCH1B,IAAIvB,EAAczB,YAElBsD,EAAiB7B,EAAY,GAAG8B,+CCFhC,IAOIC,EAAOC,EAPPlE,EAASS,IACT0D,WCDJC,EAAqC,oBAAbC,WAA4BC,OAAOD,UAAUF,YAAc,IDG/EI,EAAUvE,EAAOuE,QACjBC,EAAOxE,EAAOwE,KACdC,EAAWF,GAAWA,EAAQE,UAAYD,GAAQA,EAAKN,QACvDQ,EAAKD,GAAYA,EAASC,UAG1BA,IAIFR,GAHAD,EAAQS,EAAG1B,MAAM,MAGD,GAAK,GAAKiB,EAAM,GAAK,EAAI,IAAMA,EAAM,GAAKA,EAAM,MAK7DC,GAAWC,MACdF,EAAQE,EAAUF,MAAM,iBACVA,EAAM,IAAM,MACxBA,EAAQE,EAAUF,MAAM,oBACbC,GAAWD,EAAM,IAIhCU,EAAiBT,kCEzBjB,IAAIU,EAAanE,KACbH,EAAQsC,IAGRiC,EAFS/B,IAEQwB,cAGPQ,IAAKnE,OAAOoE,wBAA0BzE,GAAM,WACxD,IAAI0E,EAASC,OAAO,oBAKpB,OAAQJ,EAAQG,MAAarE,OAAOqE,aAAmBC,UAEpDA,OAAOC,MAAQN,GAAcA,EAAa,EAC/C,oCChBA,IAAIO,EAAgB1E,YAEpB2E,EAAiBD,IACXF,OAAOC,MACkB,iBAAnBD,OAAOI,yCCLnB,IAAI1B,EAAalD,KACb8C,EAAaX,KACboB,EAAgBlB,KAChBwC,EAAoBC,KAEpBxC,EAAUpC,cAEd6E,EAAiBF,EAAoB,SAAUxF,GAC7C,MAAoB,iBAANA,CACf,EAAG,SAAUA,GACZ,IAAI2F,EAAU9B,EAAW,UACzB,OAAOJ,EAAWkC,IAAYzB,EAAcyB,EAAQrE,UAAW2B,EAAQjD,qCCXzE,IAAI+E,EAAUP,cAEAoB,EAAG,SAAUjC,GACzB,IACE,OAAOoB,EAAQpB,EAChB,CAAC,MAAOjD,GACP,MAAO,QACR,qCCPH,IAAI+C,EAAa9C,KACbiF,EAAc9C,KAEdJ,EAAaC,iBAGHkD,EAAG,SAAUlC,GACzB,GAAIF,EAAWE,GAAW,OAAOA,EACjC,MAAM,IAAIjB,EAAWkD,EAAYjC,GAAY,0DCR/C,IAAIkC,EAAYlF,KACZ8B,EAAoBK,YAIxBgD,GAAiB,SAAUC,EAAGC,GAC5B,IAAIC,EAAOF,EAAEC,GACb,OAAOvD,EAAkBwD,QAAQvC,EAAYmC,EAAUI,uCCPzD,IAAI5E,EAAOV,IACP8C,EAAaX,KACbc,EAAWZ,KAEXN,EAAaC,iBAIjBuD,GAAiB,SAAUC,EAAOC,GAChC,IAAIjE,EAAIkE,EACR,GAAa,WAATD,GAAqB3C,EAAWtB,EAAKgE,EAAM9D,YAAcuB,EAASyC,EAAMhF,EAAKc,EAAIgE,IAAS,OAAOE,EACrG,GAAI5C,EAAWtB,EAAKgE,EAAMG,WAAa1C,EAASyC,EAAMhF,EAAKc,EAAIgE,IAAS,OAAOE,EAC/E,GAAa,WAATD,GAAqB3C,EAAWtB,EAAKgE,EAAM9D,YAAcuB,EAASyC,EAAMhF,EAAKc,EAAIgE,IAAS,OAAOE,EACrG,MAAM,IAAI3D,EAAW,yKCbvB6D,IAAiB,sCCAjB,IAAIrG,EAASS,IAGTG,EAAiBD,OAAOC,sBAE5B0F,GAAiB,SAAUC,EAAK7E,GAC9B,IACEd,EAAeZ,EAAQuG,EAAK,CAAE7E,MAAOA,EAAOE,cAAc,EAAMC,UAAU,GAC3E,CAAC,MAAOrB,GACPR,EAAOuG,GAAO7E,CACf,CAAC,OAAOA,8CCVX,IAAI8E,EAAU/F,KACVR,EAAa2C,IACb0D,EAAuBxD,KAEvB2D,EAAS,qBACTC,EAAQC,GAAcC,QAAG3G,EAAWwG,IAAWH,EAAqBG,EAAQ,CAAA,UAE/EC,EAAMjC,WAAaiC,EAAMjC,SAAW,KAAKoC,KAAK,CAC7C3C,QAAS,SACT4C,KAAMN,EAAU,OAAS,SACzBO,UAAW,4CACXC,QAAS,2DACTC,OAAQ,sFCZV,IAAIP,EAAQjG,YAEZyG,GAAiB,SAAUX,EAAK7E,GAC9B,OAAOgF,EAAMH,KAASG,EAAMH,GAAO7E,GAAS,CAAA,uCCH9C,IAAIgB,EAAyBjC,KAEzBsC,EAAUpC,cAIAwG,GAAG,SAAU1D,GACzB,OAAOV,EAAQL,EAAuBe,wCCPxC,IAAIvB,EAAczB,KACd0G,EAAWvE,KAEX3B,EAAiBiB,EAAY,CAAE,EAACjB,uBAKtBmG,GAAGzG,OAAO0G,QAAU,SAAgBvH,EAAIyG,GACpD,OAAOtF,EAAekG,EAASrH,GAAKyG,uCCTtC,IAAIrE,EAAczB,KAEd6G,EAAK,EACLC,EAAUxH,KAAKyH,SACfrF,EAAWD,EAAY,GAAIC,iBAEjBsF,GAAG,SAAUlB,GACzB,MAAO,gBAAqB/C,IAAR+C,EAAoB,GAAKA,GAAO,KAAOpE,IAAWmF,EAAKC,EAAS,wCCPtF,IAAIvH,EAASS,IACTyG,EAAStE,KACTyE,EAASvE,KACT2E,EAAMlC,KACNJ,EAAgBuC,KAChBpC,EAAoBqC,KAEpB1C,EAASjF,EAAOiF,OAChB2C,EAAwBV,EAAO,OAC/BW,EAAwBvC,EAAoBL,EAAY,KAAKA,EAASA,GAAUA,EAAO6C,eAAiBL,SAE9FM,GAAG,SAAUC,GAKvB,OAJGX,EAAOO,EAAuBI,KACjCJ,EAAsBI,GAAQ7C,GAAiBkC,EAAOpC,EAAQ+C,GAC1D/C,EAAO+C,GACPH,EAAsB,UAAYG,IAC/BJ,EAAsBI,uCChBjC,IAAI7G,EAAOV,IACPiD,EAAWd,KACX4C,EAAW1C,KACX8C,EAAYL,KACZS,EAAsB0B,KACtBK,EAAkBJ,KAElBnF,EAAaC,UACbwF,EAAeF,EAAgB,sBAInCG,GAAiB,SAAUjC,EAAOC,GAChC,IAAKxC,EAASuC,IAAUT,EAASS,GAAQ,OAAOA,EAChD,IACIkC,EADAC,EAAexC,EAAUK,EAAOgC,GAEpC,GAAIG,EAAc,CAGhB,QAFa5E,IAAT0C,IAAoBA,EAAO,WAC/BiC,EAAShH,EAAKiH,EAAcnC,EAAOC,IAC9BxC,EAASyE,IAAW3C,EAAS2C,GAAS,OAAOA,EAClD,MAAM,IAAI3F,EAAW,0CACtB,CAED,YADagB,IAAT0C,IAAoBA,EAAO,UACxBF,EAAoBC,EAAOC,uCCvBpC,IAAIgC,EAAczH,KACd+E,EAAW5C,YAIDyF,GAAG,SAAU5E,GACzB,IAAI8C,EAAM2B,EAAYzE,EAAU,UAChC,OAAO+B,EAASe,GAAOA,EAAMA,EAAM,uCCPrC,IAAI+B,EAAc7H,IACdH,EAAQsC,IACR2F,kCCFJ,IAAIvI,EAASS,IACTiD,EAAWd,KAEXS,EAAWrD,EAAOqD,SAElBmF,EAAS9E,EAASL,IAAaK,EAASL,EAASkF,sBAEvCE,GAAG,SAAU3I,GACzB,OAAO0I,EAASnF,EAASkF,cAAczI,GAAM,CAAA,GDN3BgD,UAGpB4F,IAAkBJ,IAAgBhI,GAAM,WAEtC,OAES,IAFFK,OAAOC,eAAe2H,EAAc,OAAQ,IAAK,CACtD1H,IAAK,WAAc,OAAO,CAAI,IAC7B8H,CACL,sCEVA,IAAIL,EAAc7H,IACdU,EAAOyB,IACPgG,gCCFJ,IAAIC,EAAwB,CAAE,EAAC3F,qBAE3B4F,EAA2BnI,OAAOmI,yBAGlCC,EAAcD,IAA6BD,EAAsB1H,KAAK,CAAE,EAAG,GAAK,UAIpF6H,GAAAC,EAAYF,EAAc,SAA8BlD,GACtD,IAAIqD,EAAaJ,EAAyB1I,KAAMyF,GAChD,QAASqD,GAAcA,EAAWvH,UACnC,EAAGkH,KDV6B/F,GAC7BtB,EAA2B+D,KAC3BpC,EAAkBuE,KAClBW,EAAgBV,KAChBN,EAAS8B,KACTC,EAAiBC,KAGjBC,EAA4B3I,OAAOmI,gCAI9BS,EAAAN,EAAGX,EAAcgB,EAA4B,SAAkCE,EAAG1D,GAGzF,GAFA0D,EAAIrG,EAAgBqG,GACpB1D,EAAIuC,EAAcvC,GACdsD,EAAgB,IAClB,OAAOE,EAA0BE,EAAG1D,EACxC,CAAI,MAAOtF,GAAsB,CAC/B,GAAI6G,EAAOmC,EAAG1D,GAAI,OAAOtE,GAA0BL,EAAKyH,EAA2BK,EAAGO,EAAG1D,GAAI0D,EAAE1D,yEEpBjG,IAAIpC,EAAWjD,KAEXoE,EAAUP,OACV9B,EAAaC,iBAGHgH,GAAG,SAAUhG,GACzB,GAAIC,EAASD,GAAW,OAAOA,EAC/B,MAAM,IAAIjB,EAAWqC,EAAQpB,GAAY,yDCR3C,IAAI6E,EAAc7H,IACd2I,EAAiBxG,KACjB8G,kCCFJ,IAAIpB,EAAc7H,IACdH,EAAQsC,WAIZ+G,GAAiBrB,GAAehI,GAAM,WAEpC,OAGiB,KAHVK,OAAOC,gBAAe,WAAY,GAAiB,YAAa,CACrEc,MAAO,GACPG,UAAU,IACTT,SACL,IDT8B0B,GAC1B2G,EAAWlE,KACX8C,EAAgBX,KAEhBlF,EAAaC,UAEbmH,EAAkBjJ,OAAOC,eAEzB0I,EAA4B3I,OAAOmI,yBACnCe,EAAa,aACbC,EAAe,eACfC,EAAW,kBAIfC,GAAAf,EAAYX,EAAcoB,EAA0B,SAAwBF,EAAG1D,EAAGmE,GAIhF,GAHAR,EAASD,GACT1D,EAAIuC,EAAcvC,GAClB2D,EAASQ,GACQ,mBAANT,GAA0B,cAAN1D,GAAqB,UAAWmE,GAAcF,KAAYE,IAAeA,EAAWF,GAAW,CAC5H,IAAIG,EAAUZ,EAA0BE,EAAG1D,GACvCoE,GAAWA,EAAQH,KACrBP,EAAE1D,GAAKmE,EAAWvI,MAClBuI,EAAa,CACXrI,aAAckI,KAAgBG,EAAaA,EAAWH,GAAgBI,EAAQJ,GAC9EnI,WAAYkI,KAAcI,EAAaA,EAAWJ,GAAcK,EAAQL,GACxEhI,UAAU,GAGf,CAAC,OAAO+H,EAAgBJ,EAAG1D,EAAGmE,EAChC,EAAGL,EAAkB,SAAwBJ,EAAG1D,EAAGmE,GAIlD,GAHAR,EAASD,GACT1D,EAAIuC,EAAcvC,GAClB2D,EAASQ,GACLb,EAAgB,IAClB,OAAOQ,EAAgBJ,EAAG1D,EAAGmE,EACjC,CAAI,MAAOzJ,GAAsB,CAC/B,GAAI,QAASyJ,GAAc,QAASA,EAAY,MAAM,IAAIzH,EAAW,2BAErE,MADI,UAAWyH,IAAYT,EAAE1D,GAAKmE,EAAWvI,OACtC8H,yCEzCT,IAAIlB,EAAc7H,IACd0J,EAAuBvH,KACvBpB,EAA2BsB,YAEjBsH,GAAG9B,EAAc,SAAU+B,EAAQ9D,EAAK7E,GACpD,OAAOyI,EAAqBlB,EAAEoB,EAAQ9D,EAAK/E,EAAyB,EAAGE,GACzE,EAAI,SAAU2I,EAAQ9D,EAAK7E,GAEzB,OADA2I,EAAO9D,GAAO7E,EACP2I,uGCRT,IAAInI,EAAczB,KACd8C,EAAaX,KACb8D,EAAQ5D,KAERwH,EAAmBpI,EAAY7B,SAAS8B,iBAGvCoB,EAAWmD,EAAM6D,iBACpB7D,EAAM6D,cAAgB,SAAUzK,GAC9B,OAAOwK,EAAiBxK,EAC5B,GAGcyK,GAAG7D,EAAM6D,iDCbvB,IAAIrD,EAASzG,KACTgH,EAAM7E,KAEN4H,EAAOtD,EAAO,eAEJuD,GAAG,SAAUlE,GACzB,OAAOiE,EAAKjE,KAASiE,EAAKjE,GAAOkB,EAAIlB,sCCNvCmE,GAAiB,CAAA,sCCAjB,IAYIC,EAAK9J,EAAK+J,EAZVC,kCCAJ,IAAI7K,EAASS,IACT8C,EAAaX,KAEbkI,EAAU9K,EAAO8K,eAErBC,GAAiBxH,EAAWuH,IAAY,cAAc/J,KAAKuD,OAAOwG,IDL5CrK,GAClBT,EAAS4C,IACTc,EAAWZ,KACXsH,EAA8B7E,KAC9B8B,EAASK,KACTR,EAASS,KACT8C,EAAYtB,KACZuB,EAAarB,KAEb2B,EAA6B,6BAC7BvI,EAAYzC,EAAOyC,UACnBqI,EAAU9K,EAAO8K,QAgBrB,GAAID,GAAmB3D,EAAO+D,MAAO,CACnC,IAAIvE,EAAQQ,EAAO+D,QAAU/D,EAAO+D,MAAQ,IAAIH,GAEhDpE,EAAM7F,IAAM6F,EAAM7F,IAClB6F,EAAMkE,IAAMlE,EAAMkE,IAClBlE,EAAMiE,IAAMjE,EAAMiE,IAElBA,EAAM,SAAU7K,EAAIoL,GAClB,GAAIxE,EAAMkE,IAAI9K,GAAK,MAAM,IAAI2C,EAAUuI,GAGvC,OAFAE,EAASC,OAASrL,EAClB4G,EAAMiE,IAAI7K,EAAIoL,GACPA,CACX,EACErK,EAAM,SAAUf,GACd,OAAO4G,EAAM7F,IAAIf,IAAO,CAAA,CAC5B,EACE8K,EAAM,SAAU9K,GACd,OAAO4G,EAAMkE,IAAI9K,EACrB,CACA,KAAO,CACL,IAAIsL,EAAQX,EAAU,SACtBC,EAAWU,IAAS,EACpBT,EAAM,SAAU7K,EAAIoL,GAClB,GAAI7D,EAAOvH,EAAIsL,GAAQ,MAAM,IAAI3I,EAAUuI,GAG3C,OAFAE,EAASC,OAASrL,EAClBsK,EAA4BtK,EAAIsL,EAAOF,GAChCA,CACX,EACErK,EAAM,SAAUf,GACd,OAAOuH,EAAOvH,EAAIsL,GAAStL,EAAGsL,GAAS,EAC3C,EACER,EAAM,SAAU9K,GACd,OAAOuH,EAAOvH,EAAIsL,EACtB,CACC,QAEDC,GAAiB,CACfV,IAAKA,EACL9J,IAAKA,EACL+J,IAAKA,EACLU,QArDY,SAAUxL,GACtB,OAAO8K,EAAI9K,GAAMe,EAAIf,GAAM6K,EAAI7K,EAAI,CAAA,EACrC,EAoDEyL,UAlDc,SAAUC,GACxB,OAAO,SAAU1L,GACf,IAAImL,EACJ,IAAKvH,EAAS5D,KAAQmL,EAAQpK,EAAIf,IAAK2L,OAASD,EAC9C,MAAM,IAAI/I,EAAU,0BAA4B+I,EAAO,aACvD,OAAOP,CACb,CACA,8CEzBA,IAAI/I,EAAczB,KACdH,EAAQsC,IACRW,EAAaT,KACbuE,EAAS9B,KACT+C,EAAcZ,IACdgE,kCCLJ,IAAIpD,EAAc7H,IACd4G,EAASzE,KAETd,EAAoBzB,SAASe,UAE7BuK,EAAgBrD,GAAe3H,OAAOmI,yBAEtCN,EAASnB,EAAOvF,EAAmB,QAEnC8J,EAASpD,GAA0D,cAAhD,WAAqC,EAAER,KAC1D8B,EAAetB,KAAYF,GAAgBA,GAAeqD,EAAc7J,EAAmB,QAAQF,qBAEvGiK,GAAiB,CACfrD,OAAQA,EACRoD,OAAQA,EACR9B,aAAcA,GDViBnC,GAAsCmC,aACnES,EAAgBpB,KAChB2C,EAAsBzC,KAEtB0C,EAAuBD,EAAoBR,QAC3CU,EAAmBF,EAAoBjL,IACvCgE,EAAUP,OAEV1D,EAAiBD,OAAOC,eACxBwB,EAAcF,EAAY,GAAGG,OAC7B4J,EAAU/J,EAAY,GAAG+J,SACzBC,EAAOhK,EAAY,GAAGgK,MAEtBC,EAAsB7D,IAAgBhI,GAAM,WAC9C,OAAsF,IAA/EM,GAAe,WAA2B,GAAE,SAAU,CAAEc,MAAO,IAAKoC,MAC7E,IAEIsI,EAAW9H,OAAOA,QAAQtB,MAAM,UAEhCqJ,EAAcC,GAAA1F,QAAiB,SAAUlF,EAAOsG,EAAMuE,GACf,YAArCnK,EAAYyC,EAAQmD,GAAO,EAAG,KAChCA,EAAO,IAAMiE,EAAQpH,EAAQmD,GAAO,wBAAyB,MAAQ,KAEnEuE,GAAWA,EAAQC,SAAQxE,EAAO,OAASA,GAC3CuE,GAAWA,EAAQE,SAAQzE,EAAO,OAASA,KAC1CX,EAAO3F,EAAO,SAAYgK,GAA8BhK,EAAMsG,OAASA,KACtEM,EAAa1H,EAAec,EAAO,OAAQ,CAAEA,MAAOsG,EAAMpG,cAAc,IACvEF,EAAMsG,KAAOA,GAEhBmE,GAAuBI,GAAWlF,EAAOkF,EAAS,UAAY7K,EAAMoC,SAAWyI,EAAQG,OACzF9L,EAAec,EAAO,SAAU,CAAEA,MAAO6K,EAAQG,QAEnD,IACMH,GAAWlF,EAAOkF,EAAS,gBAAkBA,EAAQI,YACnDrE,GAAa1H,EAAec,EAAO,YAAa,CAAEG,UAAU,IAEvDH,EAAMN,YAAWM,EAAMN,eAAYoC,EAClD,CAAI,MAAOhD,GAAsB,CAC/B,IAAIyK,EAAQc,EAAqBrK,GAG/B,OAFG2F,EAAO4D,EAAO,YACjBA,EAAMhE,OAASiF,EAAKE,EAAyB,iBAARpE,EAAmBA,EAAO,KACxDtG,CACX,SAIArB,SAASe,UAAUe,SAAWkK,GAAY,WACxC,OAAO9I,EAAWnD,OAAS4L,EAAiB5L,MAAM6G,QAAUsD,EAAcnK,KAC3E,GAAE,0DErDH,IAAImD,EAAa9C,KACb0J,EAAuBvH,KACvByJ,EAAcvJ,KACdwD,EAAuBf,YAEbqH,GAAG,SAAUpD,EAAGjD,EAAK7E,EAAO6K,GACnCA,IAASA,EAAU,IACxB,IAAIM,EAASN,EAAQ5K,WACjBqG,OAAwBxE,IAAjB+I,EAAQvE,KAAqBuE,EAAQvE,KAAOzB,EAEvD,GADIhD,EAAW7B,IAAQ2K,EAAY3K,EAAOsG,EAAMuE,GAC5CA,EAAQvM,OACN6M,EAAQrD,EAAEjD,GAAO7E,EAChB4E,EAAqBC,EAAK7E,OAC1B,CACL,IACO6K,EAAQO,OACJtD,EAAEjD,KAAMsG,GAAS,UADErD,EAAEjD,EAEpC,CAAM,MAAO/F,GAAsB,CAC3BqM,EAAQrD,EAAEjD,GAAO7E,EAChByI,EAAqBlB,EAAEO,EAAGjD,EAAK,CAClC7E,MAAOA,EACPC,YAAY,EACZC,cAAe2K,EAAQQ,gBACvBlL,UAAW0K,EAAQS,aAEtB,CAAC,OAAOxD,mGCzBX,IAAIyD,kCCAJ,IAAIC,EAAOnN,KAAKmN,KACZC,EAAQpN,KAAKoN,aAKHC,GAAGrN,KAAKkN,OAAS,SAAeI,GAC5C,IAAIC,GAAKD,EACT,OAAQC,EAAI,EAAIH,EAAQD,GAAMI,ODRpB7M,UAIE8M,GAAG,SAAU9J,GACzB,IAAI+J,GAAU/J,EAEd,OAAO+J,GAAWA,GAAqB,IAAXA,EAAe,EAAIP,EAAMO,uCEPvD,IAAID,EAAsB9M,KAEtBgN,EAAM1N,KAAK0N,IACXC,EAAM3N,KAAK2N,WAKfC,GAAiB,SAAUC,EAAO9J,GAChC,IAAI+J,EAAUN,EAAoBK,GAClC,OAAOC,EAAU,EAAIJ,EAAII,EAAU/J,EAAQ,GAAK4J,EAAIG,EAAS/J,uCCV/D,IAAIyJ,EAAsB9M,KAEtBiN,EAAM3N,KAAK2N,WAIDI,GAAG,SAAUrK,GACzB,IAAIsK,EAAMR,EAAoB9J,GAC9B,OAAOsK,EAAM,EAAIL,EAAIK,EAAK,kBAAoB,sCCRhD,IAAID,EAAWrN,YAIDuN,GAAG,SAAUC,GACzB,OAAOH,EAASG,EAAInK,4CCLtB,IAAI5B,EAAczB,KACd4G,EAASzE,KACTO,EAAkBL,KAClBoL,kCCHJ,IAAI/K,EAAkB1C,KAClBkN,EAAkB/K,KAClBoL,EAAoBlL,KAGpBqL,EAAe,SAAUC,GAC3B,OAAO,SAAUC,EAAOC,EAAIC,GAC1B,IAAI/E,EAAIrG,EAAgBkL,GACpBvK,EAASkK,EAAkBxE,GAC/B,GAAe,IAAX1F,EAAc,OAAQsK,IAAgB,EAC1C,IACI1M,EADAkM,EAAQD,EAAgBY,EAAWzK,GAIvC,GAAIsK,GAAeE,GAAOA,GAAI,KAAOxK,EAAS8J,GAG5C,IAFAlM,EAAQ8H,EAAEoE,OAEIlM,EAAO,OAAO,OAEvB,KAAMoC,EAAS8J,EAAOA,IAC3B,IAAKQ,GAAeR,KAASpE,IAAMA,EAAEoE,KAAWU,EAAI,OAAOF,GAAeR,GAAS,EACnF,OAAQQ,IAAgB,CAC9B,CACA,SAEAI,GAAiB,CAGfC,SAAUN,GAAa,GAGvBD,QAASC,GAAa,ID5BV5I,GAAuC2I,QACjDxD,EAAahD,KAEbb,EAAO3E,EAAY,GAAG2E,aAE1B6H,GAAiB,SAAUrE,EAAQsE,GACjC,IAGIpI,EAHAiD,EAAIrG,EAAgBkH,GACpBuE,EAAI,EACJzG,EAAS,GAEb,IAAK5B,KAAOiD,GAAInC,EAAOqD,EAAYnE,IAAQc,EAAOmC,EAAGjD,IAAQM,EAAKsB,EAAQ5B,GAE1E,KAAOoI,EAAM7K,OAAS8K,GAAOvH,EAAOmC,EAAGjD,EAAMoI,EAAMC,SAChDV,EAAQ/F,EAAQ5B,IAAQM,EAAKsB,EAAQ5B,IAExC,OAAO4B,gGElBT,IAAIxE,EAAalD,KACbyB,EAAcU,KACdiM,kCCFJ,IAAIC,EAAqBrO,KAGrBiK,eCFJqE,GAAiB,CACf,cACA,iBACA,gBACA,uBACA,iBACA,WACA,aDL2BC,OAAO,SAAU,oBAKrCC,GAAAhG,EAAGtI,OAAOuO,qBAAuB,SAA6B1F,GACrE,OAAOsF,EAAmBtF,EAAGkB,ODPC5H,GAC5BqM,aGFKC,GAAAnG,EAAGtI,OAAOoE,2BHGf0E,EAAW/B,KAEXsH,EAAS9M,EAAY,GAAG8M,eAG5BK,GAAiB1L,EAAW,UAAW,YAAc,SAAiB7D,GACpE,IAAI0K,EAAOqE,EAA0B5F,EAAEQ,EAAS3J,IAC5CiF,EAAwBoK,EAA4BlG,EACxD,OAAOlE,EAAwBiK,EAAOxE,EAAMzF,EAAsBjF,IAAO0K,sCIZ3E,IAAInD,EAAS5G,KACT4O,EAAUzM,KACV0M,EAAiCxM,KACjCqH,EAAuB5E,YAE3BgK,GAAiB,SAAUC,EAAQvI,EAAQwI,GAIzC,IAHA,IAAIjF,EAAO6E,EAAQpI,GACfrG,EAAiBuJ,EAAqBlB,EACtCH,EAA2BwG,EAA+BrG,EACrD2F,EAAI,EAAGA,EAAIpE,EAAK1G,OAAQ8K,IAAK,CACpC,IAAIrI,EAAMiE,EAAKoE,GACVvH,EAAOmI,EAAQjJ,IAAUkJ,GAAcpI,EAAOoI,EAAYlJ,IAC7D3F,EAAe4O,EAAQjJ,EAAKuC,EAAyB7B,EAAQV,GAEhE,sCCdH,IAAIvG,EAASS,IACTqI,EAA2BlG,KAA2DqG,EACtFmB,EAA8BtH,KAC9B8J,EAAgBrH,KAChBe,EAAuBoB,KACvB6H,EAA4B5H,KAC5B+H,kCCNJ,IAAIpP,EAAQG,IACR8C,EAAaX,KAEb+M,EAAc,kBAEdD,EAAW,SAAUE,EAASC,GAChC,IAAInO,EAAQoO,EAAKC,EAAUH,IAC3B,OAAOlO,IAAUsO,GACbtO,IAAUuO,IACV1M,EAAWsM,GAAavP,EAAMuP,KAC5BA,EACR,EAEIE,EAAYL,EAASK,UAAY,SAAUG,GAC7C,OAAO5L,OAAO4L,GAAQjE,QAAQ0D,EAAa,KAAKQ,aAClD,EAEIL,EAAOJ,EAASI,KAAO,GACvBG,EAASP,EAASO,OAAS,IAC3BD,EAAWN,EAASM,SAAW,WAEnCI,GAAiBV,EDfFvG,UAiBfkH,GAAiB,SAAU9D,EAAStF,GAClC,IAGYuI,EAAQjJ,EAAK+J,EAAgBC,EAAgBrH,EAHrDsH,EAASjE,EAAQiD,OACjBiB,EAASlE,EAAQvM,OACjB0Q,EAASnE,EAAQoE,KASrB,GANEnB,EADEiB,EACOzQ,EACA0Q,EACA1Q,EAAOwQ,IAAWlK,EAAqBkK,EAAQ,CAAA,GAE/CxQ,EAAOwQ,IAAWxQ,EAAOwQ,GAAQpP,UAEhC,IAAKmF,KAAOU,EAAQ,CAQ9B,GAPAsJ,EAAiBtJ,EAAOV,GAGtB+J,EAFE/D,EAAQqE,gBACV1H,EAAaJ,EAAyB0G,EAAQjJ,KACf2C,EAAWxH,MACpB8N,EAAOjJ,IACtBmJ,EAASe,EAASlK,EAAMiK,GAAUE,EAAS,IAAM,KAAOnK,EAAKgG,EAAQsE,cAE5CrN,IAAnB8M,EAA8B,CAC3C,UAAWC,UAAyBD,EAAgB,SACpDf,EAA0BgB,EAAgBD,EAC3C,EAEG/D,EAAQrH,MAASoL,GAAkBA,EAAepL,OACpDkF,EAA4BmG,EAAgB,QAAQ,GAEtD3D,EAAc4C,EAAQjJ,EAAKgK,EAAgBhE,EAC5C,sCEpDH,IAAI1J,EAAUpC,YAKAqQ,GAAGC,MAAMD,SAAW,SAAiBrN,GACjD,MAA6B,UAAtBZ,EAAQY,uCCNjB,IAAI6E,EAAc7H,IACdqQ,EAAUlO,KAEVJ,EAAaC,UAEbqG,EAA2BnI,OAAOmI,yBAGlCkI,EAAoC1I,IAAgB,WAEtD,QAAa9E,IAATpD,KAAoB,OAAO,EAC/B,IAEEO,OAAOC,eAAe,GAAI,SAAU,CAAEiB,UAAU,IAASiC,OAAS,CACnE,CAAC,MAAOtD,GACP,OAAOA,aAAiBiC,SACzB,CACH,CATwD,UAWxDwO,GAAiBD,EAAoC,SAAUxH,EAAG1F,GAChE,GAAIgN,EAAQtH,KAAOV,EAAyBU,EAAG,UAAU3H,SACvD,MAAM,IAAIW,EAAW,gCACrB,OAAOgH,EAAE1F,OAASA,CACtB,EAAI,SAAU0F,EAAG1F,GACf,OAAO0F,EAAE1F,OAASA,sCCxBpB,IAAItB,EAAaC,iBAGHyO,GAAG,SAAUpR,GACzB,GAAIA,EAHiB,iBAGM,MAAM0C,EAAW,kCAC5C,OAAO1C,GCLM,SAASqR,GAAkBC,EAAGC,GAC5C,IAAIC,EAAIF,EAAEtN,OAELiN,MAAMD,QAAQM,EAAE,MAEpBA,EAAI,CAACA,IAGDL,MAAMD,QAAQO,EAAE,MAEpBA,EAAIA,EAAEE,KAAIlE,GAAK,CAACA,MAGjB,IAAImE,EAAIH,EAAE,GAAGvN,OACT2N,EAASJ,EAAE,GAAGE,KAAI,CAACG,EAAG9C,IAAMyC,EAAEE,KAAIlE,GAAKA,EAAEuB,OACzC+C,EAAUP,EAAEG,KAAIK,GAAOH,EAAOF,KAAIM,IACrC,IAAIC,EAAM,EAEV,IAAKf,MAAMD,QAAQc,GAAM,CACxB,IAAK,IAAIG,KAAKF,EACbC,GAAOF,EAAMG,EAGd,OAAOD,CACR,CAEA,IAAK,IAAIlD,EAAI,EAAGA,EAAIgD,EAAI9N,OAAQ8K,IAC/BkD,GAAOF,EAAIhD,IAAMiD,EAAIjD,IAAM,GAG5B,OAAOkD,CAAG,MAOX,OAJU,IAANR,IACHK,EAAUA,EAAQ,IAGT,IAANH,EACIG,EAAQJ,KAAIlE,GAAKA,EAAE,KAGpBsE,CACR,CChCO,SAASK,GAAUC,GACzB,MAAqB,WAAdxG,GAAKwG,EACb,CAOO,SAASxG,GAAMyG,GAGrB,OAFUvR,OAAOS,UAAUe,SAAShB,KAAK+Q,GAE7BjO,MAAM,wBAAwB,IAAM,IAAIkM,aACrD,CAEO,SAASgC,GAAiB7E,EAAC8E,GAAsB,IAApBC,UAACA,EAASC,KAAEA,GAAMF,EACrD,OAAIG,GAAOjF,GACH,OAGDkF,GAAYlF,EAAG+E,IAAcC,QAAAA,EAAQ,GAC7C,CAOO,SAASC,GAAQjF,GACvB,OAAOmF,OAAOC,MAAMpF,IAAOA,aAAamF,SAAUnF,eAAAA,EAAGqF,KACtD,CAKO,SAASC,GAAUtF,GACzB,OAAOiF,GAAOjF,GAAK,EAAIA,CACxB,CAOO,SAASkF,GAAalF,EAAG+E,GAC/B,GAAU,IAAN/E,EACH,OAAO,EAER,IAAIO,IAAYP,EACZuF,EAAS,EACThF,GAAWwE,IACdQ,EAA2C,IAAhC9S,KAAK+S,MAAM/S,KAAKgT,IAAIlF,KAEhC,MAAMmF,EAAa,KAASX,EAAYQ,GACxC,OAAO9S,KAAKoN,MAAMG,EAAI0F,EAAa,IAAOA,CAC3C,iCCjEA,IAAIC,EAAIxS,KACJ0G,EAAWvE,KACXoL,EAAoBlL,KACpBoQ,EAAiB3N,KACjB2L,EAA2BxJ,KAsB/BuL,EAAE,CAAEzD,OAAQ,QAAS2D,OAAO,EAAMzG,MAAO,EAAGmE,OArBhClJ,GAEcrH,EAAM,WAC9B,OAAoD,aAA7C,GAAGuG,KAAK1F,KAAK,CAAE2C,OAAQ,YAAe,EAC/C,MAIqC,WACnC,IAEEnD,OAAOC,eAAe,GAAI,SAAU,CAAEiB,UAAU,IAASgF,MAC1D,CAAC,MAAOrG,GACP,OAAOA,aAAiBiC,SACzB,CACH,CAEqC2Q,IAIyB,CAE5DvM,KAAM,SAAcwM,GAClB,IAAI7J,EAAIrC,EAAS/G,MACb2N,EAAMC,EAAkBxE,GACxB8J,EAAW/R,UAAUuC,OACzBoN,EAAyBnD,EAAMuF,GAC/B,IAAK,IAAI1E,EAAI,EAAGA,EAAI0E,EAAU1E,IAC5BpF,EAAEuE,GAAOxM,UAAUqN,GACnBb,IAGF,OADAmF,EAAe1J,EAAGuE,GACXA,CACR,OD4BH,MAAMwF,GAAc,CACnBC,IAAK,EACLC,KAAM,GACNC,IAAK,IAAM3T,KAAK4T,GAChBC,KAAM,KAQA,SAASC,GAAe5B,GAC9B,IAAKA,EACJ,OAGDA,EAAMA,EAAI6B,OAEV,MACMC,EAAgB,aAChBC,EAAiB,oBACjBC,EAAiB,6CACvB,IAAIC,EAAQjC,EAAIhO,MAJQ,wBAMxB,GAAIiQ,EAAO,CAEV,IAAIC,EAAO,GA6CX,OA5CAD,EAAM,GAAGjI,QAAQgI,GAAgB,CAACG,EAAIC,KACrC,IAAIpQ,EAAQoQ,EAAOpQ,MAAM+P,GACrBM,EAAMD,EAEV,GAAIpQ,EAAO,CACV,IAAIqO,EAAOrO,EAAM,GAEbsQ,EAAcD,EAAIjS,MAAM,GAAIiQ,EAAKxO,QAExB,MAATwO,GAEHgC,EAAM,IAAI7B,OAAO8B,EAAc,KAC/BD,EAAI7I,KAAO,iBAIX6I,EAAM,IAAI7B,OAAO8B,EAAchB,GAAYjB,IAC3CgC,EAAI7I,KAAO,UACX6I,EAAIhC,KAAOA,EAEZ,MACQyB,EAAchT,KAAKuT,IAE3BA,EAAM,IAAI7B,OAAO6B,GACjBA,EAAI7I,KAAO,YAEK,SAAR6I,IACRA,EAAM,IAAI7B,OAAO+B,KACjBF,EAAI3B,MAAO,GAGRyB,EAAGK,WAAW,OAEjBH,EAAMA,aAAe7B,OAAS6B,EAAM,IAAI7B,OAAO6B,GAC/CA,EAAII,OAAQ,GAGM,iBAARJ,GAAoBA,aAAe7B,SAC7C6B,EAAIK,IAAMN,GAGXF,EAAKtN,KAAKyN,EAAI,IAGR,CACNtM,KAAMkM,EAAM,GAAG/D,cACfyE,QAASV,EAAM,GACfW,QAASX,EAAM,GAGfC,OAEF,CACD,CAEO,SAASW,GAAMC,GACrB,OAAOA,EAAIA,EAAIjR,OAAS,EACzB,CAEO,SAASkR,GAAaC,EAAOC,EAAK1D,GACxC,OAAIkB,MAAMuC,GACFC,EAGJxC,MAAMwC,GACFD,EAGDA,GAASC,EAAMD,GAASzD,CAChC,CAEO,SAAS2D,GAAgBF,EAAOC,EAAKxT,GAC3C,OAAQA,EAAQuT,IAAUC,EAAMD,EACjC,CAEO,SAASG,GAAUC,EAAMC,EAAI5T,GACnC,OAAOsT,GAAYM,EAAG,GAAIA,EAAG,GAAIH,GAAeE,EAAK,GAAIA,EAAK,GAAI3T,GACnE,CAEO,SAAS6T,GAAmBC,GAClC,OAAOA,EAAcjE,KAAIkE,GACjBA,EAAazS,MAAM,KAAKuO,KAAI9F,IAElC,IAAIiK,GADJjK,EAAOA,EAAKqI,QACK7P,MAAM,6CAEvB,GAAIyR,EAAO,CACV,IAAI5D,EAAM,IAAIxN,OAAOoR,EAAM,IAE3B,OADA5D,EAAI4D,MAAQ,EAAEA,EAAM,IAAKA,EAAM,IACxB5D,CACR,CAEA,OAAOrG,CAAI,KAGd,CASO,SAASkK,GAAOjI,EAAKvH,EAAKsH,GAChC,OAAO1N,KAAK0N,IAAI1N,KAAK2N,IAAID,EAAKtH,GAAMuH,EACrC,CAQO,SAASkI,GAAUN,EAAID,GAC7B,OAAOtV,KAAK8V,KAAKP,KAAQvV,KAAK8V,KAAKR,GAAQC,GAAMA,CAClD,CAQO,SAASQ,GAAMC,EAAMC,GAC3B,OAAOJ,GAAS7V,KAAKgT,IAAIgD,IAASC,EAAKD,EACxC,CAQO,SAASE,GAAM3I,EAAG4I,GACxB,OAAc,IAANA,EAAW,EAAI5I,EAAI4I,CAC5B,CAWO,SAASC,GAAYpB,EAAKrT,GAAgC,IAAzB0U,EAAE7U,UAAAuC,OAAA,QAAAN,IAAAjC,UAAA,GAAAA,UAAA,GAAG,EAAG8U,EAAE9U,UAAAuC,OAAAvC,QAAAiC,IAAAjC,UAAAiC,GAAAjC,UAAGwT,GAAAA,EAAIjR,OACxD,KAAOsS,EAAKC,GAAI,CACf,MAAMC,EAAOF,EAAKC,GAAO,EACrBtB,EAAIuB,GAAO5U,EACd0U,EAAKE,EAAM,EAGXD,EAAKC,CAEP,CACA,OAAOF,CACR,sRE3NA,MAAMG,GAAQ,IA/BP,MACNC,GAAAA,CAAKxO,EAAMyO,EAAUC,GACpB,GAA2B,iBAAhBnV,UAAU,IASpBwP,MAAMD,QAAQ9I,GAAQA,EAAO,CAACA,IAAO2O,SAAQ,SAAU3O,GACvD5H,KAAK4H,GAAQ5H,KAAK4H,IAAS,GAEvByO,GACHrW,KAAK4H,GAAM0O,EAAQ,UAAY,QAAQD,EAExC,GAAErW,WAbF,IAAK,IAAI4H,KAAQzG,UAAU,GAC1BnB,KAAKoW,IAAIxO,EAAMzG,UAAU,GAAGyG,GAAOzG,UAAU,GAahD,CAEAqV,GAAAA,CAAK5O,EAAM6O,GACVzW,KAAK4H,GAAQ5H,KAAK4H,IAAS,GAC3B5H,KAAK4H,GAAM2O,SAAQ,SAAUF,GAC5BA,EAAStV,KAAK0V,GAAOA,EAAIC,QAAUD,EAAIC,QAAUD,EAAKA,EACvD,GACD,6GC3BcE,GAAA,CACdC,cAAe,MACf3E,UAAW,EACX4E,OAAQ,KACRC,QAA+D,UAAtDjX,OAAAA,qBAAAA,YAAmBkX,QAATA,GAAVlX,WAAYsE,eAAO4S,IAAAA,IAAK,QAALA,GAAnBA,GAAqBN,WAAGM,IAAAA,IAAU,QAAVA,GAAxBA,GAA0BC,gBAAQ,IAAAD,QAAA,EAAlCA,GAAoChH,eAC7CkH,KAAM,SAAeC,GACF,IAAAC,EAAAC,EAAdpX,KAAK8W,UACE,OAAVjX,iBAAU,IAAVA,YAAmB,QAATsX,EAAVtX,WAAYwX,eAAO,IAAAF,GAAMC,QAANA,EAAnBD,EAAqBF,YAArBG,IAAyBA,GAAzBA,EAAArW,KAAAoW,EAA4BD,GAE9B,4CCTD,IAAI5T,EAAWjD,YAEDiX,GAAG,SAAUjU,GACzB,OAAOC,EAASD,IAA0B,OAAbA,sCCH/B,IAAIiU,EAAsBjX,KAEtBoE,EAAUP,OACV9B,EAAaC,iBAEHkV,GAAG,SAAUlU,GACzB,GAAIiU,EAAoBjU,GAAW,OAAOA,EAC1C,MAAM,IAAIjB,EAAW,aAAeqC,EAAQpB,GAAY,uDCN1D,IAAImU,kCCDJ,IAAI1V,EAAczB,KACdkF,EAAY/C,YAEhBiV,GAAiB,SAAUxN,EAAQ9D,EAAK1C,GACtC,IAEE,OAAO3B,EAAYyD,EAAUhF,OAAOmI,yBAAyBuB,EAAQ9D,GAAK1C,IAC9E,CAAI,MAAOrD,GAAsB,GDNPC,GACtBgJ,EAAW7G,KACX+U,EAAqB7U,YAMXgV,GAAGnX,OAAOoX,iBAAmB,aAAe,CAAE,EAAG,WAC7D,IAEItL,EAFAuL,GAAiB,EACjBjX,EAAO,CAAA,EAEX,KACE0L,EAASmL,EAAoBjX,OAAOS,UAAW,YAAa,QACrDL,EAAM,IACbiX,EAAiBjX,aAAgBgQ,KACrC,CAAI,MAAOvQ,GAAsB,CAC/B,OAAO,SAAwBgJ,EAAG2J,GAKhC,OAJA1J,EAASD,GACTmO,EAAmBxE,GACf6E,EAAgBvL,EAAOjD,EAAG2J,GACzB3J,EAAEyO,UAAY9E,EACZ3J,CACX,EAf+D,QAgBzDhG,sCEzBN,IAAI5C,EAAiBH,KAA+CwI,SAEpEiP,GAAiB,SAAUC,EAAQC,EAAQ7R,GACzCA,KAAO4R,GAAUvX,EAAeuX,EAAQ5R,EAAK,CAC3C3E,cAAc,EACdf,IAAK,WAAc,OAAOuX,EAAO7R,EAAO,EACxCoE,IAAK,SAAU7K,GAAMsY,EAAO7R,GAAOzG,CAAK,wCCN5C,IAAIyD,EAAa9C,KACbiD,EAAWd,KACXmV,EAAiBjV,YAGrBuV,GAAiB,SAAUhK,EAAOiK,EAAOC,GACvC,IAAIC,EAAWC,EAUf,OAPEV,GAEAxU,EAAWiV,EAAYF,EAAM3L,cAC7B6L,IAAcD,GACd7U,EAAS+U,EAAqBD,EAAUpX,YACxCqX,IAAuBF,EAAQnX,WAC/B2W,EAAe1J,EAAOoK,GACjBpK,sCChBT,IAAIqK,kCCAJ,IAGI3X,EAAO,CAAA,SAEXA,EALsBN,IAEFsH,CAAgB,gBAGd,IAEtB4Q,GAAkC,eAAjBrU,OAAOvD,GDPIN,GACxB8C,EAAaX,KACbN,EAAaQ,KAGb8V,EAFkBrT,IAEFwC,CAAgB,eAChChF,EAAUpC,OAGVkY,EAAwE,cAApDvW,EAAW,WAAc,OAAOf,SAAY,CAAjC,WAUnCsB,GAAiB6V,EAAwBpW,EAAa,SAAUxC,GAC9D,IAAI0J,EAAGsP,EAAK3Q,EACZ,YAAc3E,IAAP1D,EAAmB,YAAqB,OAAPA,EAAc,OAEO,iBAAjDgZ,EAXD,SAAUhZ,EAAIyG,GACzB,IACE,OAAOzG,EAAGyG,EACd,CAAI,MAAO/F,GAAsB,CACjC,CAOoBuY,CAAOvP,EAAIzG,EAAQjD,GAAK8Y,IAA8BE,EAEpED,EAAoBvW,EAAWkH,GAEF,YAA5BrB,EAAS7F,EAAWkH,KAAoBjG,EAAWiG,EAAEwP,QAAU,YAAc7Q,sCE3BpF,IAAItF,EAAUpC,KAEVoE,EAAUP,cAEAnC,GAAG,SAAUsB,GACzB,GAA0B,WAAtBZ,EAAQY,GAAwB,MAAM,IAAIhB,UAAU,6CACxD,OAAOoC,EAAQpB,uCCNjB,IAAItB,EAAW1B,YAEfwY,GAAiB,SAAUxV,EAAUyV,GACnC,YAAoB1V,IAAbC,EAAyBlC,UAAUuC,OAAS,EAAI,GAAKoV,EAAW/W,EAASsB,0CCHlF,IAAIC,EAAWjD,KACX2J,EAA8BxH,YAIlCuW,GAAiB,SAAU3P,EAAG+C,GACxB7I,EAAS6I,IAAY,UAAWA,GAClCnC,EAA4BZ,EAAG,QAAS+C,EAAQ6M,2CCPpD,IAAIhP,EAA8B3J,KAC9B4Y,kCCDJ,IAAInX,EAAczB,KAEd6Y,EAASC,MACTtN,EAAU/J,EAAY,GAAG+J,SAEzBuN,EAAgClV,OAAO,IAAIgV,EAAuB,UAAXG,OAEvDC,EAA2B,uBAC3BC,EAAwBD,EAAyB3Y,KAAKyY,UAE1DI,GAAiB,SAAUH,EAAOI,GAChC,GAAIF,GAAyC,iBAATF,IAAsBH,EAAOQ,kBAC/D,KAAOD,KAAeJ,EAAQxN,EAAQwN,EAAOC,EAA0B,IACvE,OAAOD,GDZW7W,GAClBmX,kCEFJ,IAAIzZ,EAAQG,IACRe,EAA2BoB,YAE/BoX,IAAkB1Z,GAAM,WACtB,IAAIE,EAAQ,IAAI+Y,MAAM,KACtB,QAAM,UAAW/Y,KAEjBG,OAAOC,eAAeJ,EAAO,QAASgB,EAAyB,EAAG,IAC3C,IAAhBhB,EAAMiZ,MACf,IFP8B3W,GAG1BmX,EAAoBV,MAAMU,yBAEhBC,GAAG,SAAU1Z,EAAO2Z,EAAGV,EAAOI,GACtCE,IACEE,EAAmBA,EAAkBzZ,EAAO2Z,GAC3C/P,EAA4B5J,EAAO,QAAS6Y,EAAgBI,EAAOI,yCGV5E,IAAIlW,EAAalD,KACb4G,EAASzE,KACTwH,EAA8BtH,KAC9BkB,EAAgBuB,KAChBwS,EAAiBrQ,KACjB6H,EAA4B5H,KAC5BuQ,EAAgB/O,KAChBkP,EAAoBhP,KACpB4P,EAA0BmB,KAC1BjB,EAAoBkB,KACpBC,EAAoBC,KACpBjS,EAAckS,IACdhU,EAAUiU,YAEAC,GAAG,SAAUC,EAAWC,EAASC,EAAQC,GACrD,IAAIC,EAAoB,kBACpBC,EAAmBF,EAAqB,EAAI,EAC5CG,EAAON,EAAU3X,MAAM,KACvBkY,EAAaD,EAAKA,EAAKnX,OAAS,GAChCqX,EAAgBxX,EAAWrC,MAAM,KAAM2Z,GAE3C,GAAKE,EAAL,CAEA,IAAIC,EAAyBD,EAAc/Z,UAK3C,IAFKoF,GAAWa,EAAO+T,EAAwB,iBAAiBA,EAAuBhC,OAElFyB,EAAQ,OAAOM,EAEpB,IAAIE,EAAY1X,EAAW,SAEvB2X,EAAeV,GAAQ,SAAUjS,EAAG4S,GACtC,IAAIC,EAAUvC,EAAwB6B,EAAqBS,EAAI5S,OAAGnF,GAC9D2E,EAAS2S,EAAqB,IAAIK,EAAcxS,GAAK,IAAIwS,EAK7D,YAJgB3X,IAAZgY,GAAuBpR,EAA4BjC,EAAQ,UAAWqT,GAC1ElB,EAAkBnS,EAAQmT,EAAcnT,EAAOsR,MAAO,GAClDrZ,MAAQ4D,EAAcoX,EAAwBhb,OAAOiY,EAAkBlQ,EAAQ/H,KAAMkb,GACrF/Z,UAAUuC,OAASkX,GAAkB7B,EAAkBhR,EAAQ5G,UAAUyZ,IACtE7S,CACX,IAcE,GAZAmT,EAAala,UAAYga,EAEN,UAAfF,EACEnD,EAAgBA,EAAeuD,EAAcD,GAC5C9L,EAA0B+L,EAAcD,EAAW,CAAErT,MAAM,IACvDM,GAAeyS,KAAqBI,IAC7CjD,EAAcoD,EAAcH,EAAeJ,GAC3C7C,EAAcoD,EAAcH,EAAe,sBAG7C5L,EAA0B+L,EAAcH,IAEnC3U,EAAS,IAER4U,EAAuBpT,OAASkT,GAClC9Q,EAA4BgR,EAAwB,OAAQF,GAE9DE,EAAuBzO,YAAc2O,CACzC,CAAI,MAAO9a,GAAsB,CAE/B,OAAO8a,CAzCoB,uCCpB7B,IAAIrI,EAAIxS,KACJT,EAAS4C,IACTtB,kCCHJ,IAAIJ,EAAcT,IAEdqB,EAAoBzB,SAASe,UAC7BE,EAAQQ,EAAkBR,MAC1BH,EAAOW,EAAkBX,YAG7Bsa,GAAmC,iBAAXC,SAAuBA,QAAQpa,QAAUJ,EAAcC,EAAKH,KAAKM,GAAS,WAChG,OAAOH,EAAKG,MAAMA,EAAOC,UAC3B,MDNYuB,GACR4X,EAAgCnV,KAEhCoW,EAAe,cACfC,EAAc5b,EAAO2b,GAGrBd,EAAgD,IAAvC,IAAItB,MAAM,IAAK,CAAEH,MAAO,IAAKA,MAEtCyC,EAAgC,SAAUX,EAAYN,GACxD,IAAIpR,EAAI,CAAA,EACRA,EAAE0R,GAAcR,EAA8BQ,EAAYN,EAASC,GACnE5H,EAAE,CAAEjT,QAAQ,EAAM2M,aAAa,EAAMD,MAAO,EAAGmE,OAAQgK,GAAUrR,EACnE,EAEIsS,EAAqC,SAAUZ,EAAYN,GAC7D,GAAIgB,GAAeA,EAAYV,GAAa,CAC1C,IAAI1R,EAAI,CAAA,EACRA,EAAE0R,GAAcR,EAA8BiB,EAAe,IAAMT,EAAYN,EAASC,GACxF5H,EAAE,CAAEzD,OAAQmM,EAAchL,MAAM,EAAMhE,aAAa,EAAMD,MAAO,EAAGmE,OAAQgK,GAAUrR,EACtF,CACH,EAGAqS,EAA8B,SAAS,SAAUE,GAC/C,OAAO,SAAeP,GAAW,OAAOla,EAAMya,EAAM3b,KAAMmB,WAC5D,IACAsa,EAA8B,aAAa,SAAUE,GACnD,OAAO,SAAmBP,GAAW,OAAOla,EAAMya,EAAM3b,KAAMmB,WAChE,IACAsa,EAA8B,cAAc,SAAUE,GACpD,OAAO,SAAoBP,GAAW,OAAOla,EAAMya,EAAM3b,KAAMmB,WACjE,IACAsa,EAA8B,kBAAkB,SAAUE,GACxD,OAAO,SAAwBP,GAAW,OAAOla,EAAMya,EAAM3b,KAAMmB,WACrE,IACAsa,EAA8B,eAAe,SAAUE,GACrD,OAAO,SAAqBP,GAAW,OAAOla,EAAMya,EAAM3b,KAAMmB,WAClE,IACAsa,EAA8B,aAAa,SAAUE,GACnD,OAAO,SAAmBP,GAAW,OAAOla,EAAMya,EAAM3b,KAAMmB,WAChE,IACAsa,EAA8B,YAAY,SAAUE,GAClD,OAAO,SAAkBP,GAAW,OAAOla,EAAMya,EAAM3b,KAAMmB,WAC/D,IACAua,EAAmC,gBAAgB,SAAUC,GAC3D,OAAO,SAAsBP,GAAW,OAAOla,EAAMya,EAAM3b,KAAMmB,WACnE,IACAua,EAAmC,aAAa,SAAUC,GACxD,OAAO,SAAmBP,GAAW,OAAOla,EAAMya,EAAM3b,KAAMmB,WAChE,IACAua,EAAmC,gBAAgB,SAAUC,GAC3D,OAAO,SAAsBP,GAAW,OAAOla,EAAMya,EAAM3b,KAAMmB,WACnE,OEtDO,MAAMya,GAAS,CAErBC,IAAK,CAAC,MAAS,MAAQ,EAAS,MAA0B,OAC1DC,IAAK,CAAC,MAAS,KAAQ,EAAS,MAA0B,OAGpD,SAASC,GAAUnU,GACzB,OAAI+I,MAAMD,QAAQ9I,GACVA,EAGDgU,GAAOhU,EACf,CAGe,SAASoU,GAAOC,EAAIC,EAAIC,GAAmB,IAAdhQ,EAAOhL,UAAAuC,OAAA,QAAAN,IAAAjC,UAAA,GAAAA,UAAA,GAAG,CAAA,EAIrD,GAHA8a,EAAKF,GAASE,GACdC,EAAKH,GAASG,IAETD,IAAOC,EACX,MAAM,IAAI7Z,UAAW,kCAAkC4Z,EAAc,GAAT,SAAeA,GAAOC,EAAW,GAAN,MAAYA,EAAY,GAAP,QAGzG,GAAID,IAAOC,EAEV,OAAOC,EAGR,IAAI1F,EAAM,CAACwF,KAAIC,KAAIC,MAAKhQ,WAwBxB,GAtBAgK,GAAMK,IAAI,6BAA8BC,GAEnCA,EAAI2F,IACJ3F,EAAIwF,KAAOL,GAAOE,KAAOrF,EAAIyF,KAAON,GAAOC,IAC9CpF,EAAI2F,EAAI,CACP,CAAE,mBAAoB,qBAAuB,oBAC7C,CAAE,mBAAqB,mBAAqB,qBAC5C,EAAG,oBAAsB,oBAAsB,oBAGxC3F,EAAIwF,KAAOL,GAAOC,KAAOpF,EAAIyF,KAAON,GAAOE,MAEnDrF,EAAI2F,EAAI,CACP,CAAE,kBAAoB,mBAAqB,oBAC3C,EAAG,kBAAoB,mBAAoB,qBAC3C,CAAE,qBAAuB,oBAAsB,sBAKlDjG,GAAMK,IAAI,2BAA4BC,GAElCA,EAAI2F,EACP,OAAOrL,GAAiB0F,EAAI2F,EAAG3F,EAAI0F,KAGnC,MAAM,IAAI9Z,UAAU,qEAEtB,CCxDA,MAAMga,GAAY,IAAIC,IAAI,CAAC,WAAY,eAAgB,YAUvD,SAASC,GAAcC,EAAOC,EAAQ7U,EAAM8U,GAC3C,IAAIC,EAAQpc,OAAOqc,QAAQJ,EAAME,QAAQvL,KAAI,CAAAa,EAAkBxD,KAAM,IAOhEnD,GAP0CnE,EAAI2V,GAAU7K,EACxDqD,EAAeoH,EAAOpH,aAAa7G,GACnC0F,EAAMwI,EAAOlO,GACbsO,EAAe5I,aAAAA,EAAAA,EAAK7I,KAaxB,GAPCA,EADG6I,EAAI3B,KACA8C,EAAa0H,MAAKpL,GAAK0K,GAAU7R,IAAImH,KAGrC0D,EAAa0H,MAAKpL,GAAKA,GAAKmL,KAI/BzR,EAAM,CAEV,IAAI2R,EAAYH,EAAUjV,MAAQV,EAClC,MAAM,IAAI7E,UAAW,GAAEya,QAAAA,EAAgB5I,EAAIK,uBAAuByI,QAAgBpV,MACnF,CAEA,IAAIqV,EAAY5R,EAAKiK,MAEA,iBAAjBwH,IACHG,IAAAA,EAAc,CAAC,EAAG,KAGnB,IAAIC,EAAUL,EAAUvH,OAASuH,EAAUM,SAM3C,OAJIF,GAAaC,IAChBR,EAAOlO,GAAK4O,GAAcH,EAAWC,EAASR,EAAOlO,KAG/CnD,CAAI,IAGZ,OAAOsR,CACR,CAUe,SAASU,GAAOxL,GAAkB,IAAAyL,EAAA,IAAbC,KAACA,GAAKpc,UAAAuC,OAAAvC,QAAAiC,IAAAjC,UAAAiC,GAAAjC,UAAG,GAAA,GACxCsV,EAAM,CAAC5E,IAAkB,QAAbyL,EAAEpZ,OAAO2N,UAAI,IAAAyL,OAAA,EAAXA,EAAa5J,QAG/B,GAFAyC,GAAMK,IAAI,cAAeC,GAErBA,EAAI+G,MACP,OAAO/G,EAAI+G,MAKZ,GAFA/G,EAAIgH,OAASL,GAAmB3G,EAAI5E,KAEhC4E,EAAIgH,OAAQ,CAEf,IAAI7V,EAAO6O,EAAIgH,OAAO7V,KAEtB,GAAa,UAATA,EAAkB,CAErB,IAAIV,EAAKuP,EAAIgH,OAAO1J,KAAK2J,QAErBC,EAAczW,EAAGmN,WAAW,MAAQnN,EAAG0W,UAAU,GAAM,KAAI1W,IAC3D2W,EAAM,CAAC3W,EAAIyW,GACXrJ,EAAQmC,EAAIgH,OAAOhJ,QAAQ3G,QAAQ,KAAO,EAAI2I,EAAIgH,OAAO1J,KAAK+J,MAAQ,EAE1E,IAAK,IAAItB,KAASuB,GAAW7a,IAAK,CACjC,IAAI8a,EAAYxB,EAAMyB,UAAU,SAEjB,IAAAC,EAAf,GAAIF,EACH,GAAIH,EAAIxP,SAAS2P,EAAU9W,KAAoBgX,QAAjBA,EAAIF,EAAUH,WAAVK,IAAaA,GAAbA,EAAeC,QAAQC,GAAWP,EAAIxP,SAAS+P,KAAS1a,OAAQ,CAIjG,MAAMgZ,EAASnc,OAAO6J,KAAKoS,EAAME,QAAQvL,KAAI,CAACG,EAAG9C,IAAMiI,EAAIgH,OAAO1J,KAAKvF,IAAM,IAE7E,IAAImO,EAmBJ,OAjBIqB,EAAU3I,eACbsH,EAAQJ,GAAaC,EAAOwB,EAAW,QAAStB,IAG7Ca,GACHhd,OAAO8d,OAAOd,EAAM,CAACe,SAAU,QAAS3B,UAGrCqB,EAAU9W,GAAGmN,WAAW,QAAUnN,EAAGmN,WAAW,OACnDsC,GAASM,KAAM,GAAEuF,EAAM5U,gGACaoW,EAAU9W,wBAAwBA,OAEnEA,EAAGmN,WAAW,QAAU2J,EAAU9W,GAAGmN,WAAW,OACnDsC,GAASM,KAAM,GAAEuF,EAAM5U,qEACIoW,EAAU9W,iCAAiCA,OAGhE,CAACqX,QAAS/B,EAAMtV,GAAIwV,SAAQpI,QACpC,CAEF,CAGA,IAAIkK,EAAa,GACbC,EAAavX,KAAM6W,GAAWW,SAAWxX,EAAKyW,EAClD,GAAIc,KAAcV,GAAWW,SAAU,CAAA,IAAAC,EAEtC,IAAIC,UAAKD,EAAGZ,GAAWW,SAASD,GAAYI,eAAO,IAAAF,GAAO,QAAPA,EAAvCA,EAAyCnB,aAAK,IAAAmB,OAAA,EAA9CA,EAAgDzX,GAExD0X,IACHJ,EAAc,sBAAqBI,MAErC,CAEA,MAAM,IAAIvc,UAAW,sBAAqB6E,QAAWsX,GAAc,qBACpE,CAEC,IAAK,IAAIhC,KAASuB,GAAW7a,IAAK,CAEjC,IAAIuZ,EAASD,EAAMyB,UAAUrW,GAC7B,GAAI6U,GAA0B,aAAhBA,EAAOpR,KAAqB,CACzC,IAAIiJ,EAAQ,GAERmI,EAAOqC,WAAa1B,GAAU3G,EAAIgH,OAAO1J,MAAMO,SAClDA,EAAQmC,EAAIgH,OAAO1J,KAAK+J,OAGzB,IAEInB,EAFAD,EAASjG,EAAIgH,OAAO1J,KAYxB,OARI0I,EAAOpH,eACVsH,EAAQJ,GAAaC,EAAOC,EAAQ7U,EAAM8U,IAGvCa,GACHhd,OAAO8d,OAAOd,EAAM,CAACe,SAAU7B,EAAO7U,KAAM+U,UAGtC,CACN4B,QAAS/B,EAAMtV,GACfwV,SAAQpI,QAEV,CACD,CAEF,MAGC,IAAK,IAAIkI,KAASuB,GAAW7a,IAC5B,IAAK,IAAIob,KAAY9B,EAAMqC,QAAS,CACnC,IAAIpC,EAASD,EAAMqC,QAAQP,GAE3B,GAAoB,WAAhB7B,EAAOpR,KACV,SAGD,GAAIoR,EAAO9b,OAAS8b,EAAO9b,KAAK8V,EAAI5E,KACnC,SAGD,IAAI2L,EAAQf,EAAOY,MAAM5G,EAAI5E,KAElB,IAAAkN,EAAX,GAAIvB,EAOH,OANWuB,QAAXA,EAAAvB,EAAMlJ,aAAKyK,IAAAA,IAAXvB,EAAMlJ,MAAU,GAEZiJ,IACHA,EAAKe,SAAWA,GAGVd,CAET,CAMF,MAAM,IAAInb,UAAW,mBAAkBwP,kCACxC,CC5Le,SAASmN,GAAUxB,GACjC,GAAI7M,MAAMD,QAAQ8M,GACjB,OAAOA,EAAMrM,IAAI6N,IAGlB,IAAKxB,EACJ,MAAM,IAAInb,UAAU,yBAGjBuP,GAAS4L,KACZA,EAAQH,GAAMG,IAIf,IAAIhB,EAAQgB,EAAMhB,OAASgB,EAAMe,QAWjC,OATM/B,aAAiBuB,KAEtBP,EAAMhB,MAAQuB,GAAWtd,IAAI+b,SAGVpZ,IAAhBoa,EAAMlJ,QACTkJ,EAAMlJ,MAAQ,GAGRkJ,CACR,CC9BA,MAAMyB,GAAI,MAKK,MAAMlB,GACpBxR,WAAAA,CAAaJ,GAAS,IAAA+S,EAAAlN,EAAAmN,EAAAC,EAAAC,EACrBrf,KAAKkH,GAAKiF,EAAQjF,GAClBlH,KAAK4H,KAAOuE,EAAQvE,KACpB5H,KAAK2V,KAAOxJ,EAAQwJ,KAAOoI,GAAWtd,IAAI0L,EAAQwJ,MAAQ,KAC1D3V,KAAKsf,QAAUnT,EAAQmT,QAEnBtf,KAAK2V,OACR3V,KAAKuf,SAAWpT,EAAQoT,SACxBvf,KAAKwf,OAASrT,EAAQqT,QAKvB,IAAI9C,EAAuB,QAAjBwC,EAAG/S,EAAQuQ,cAAM,IAAAwC,EAAAA,EAAIlf,KAAK2V,KAAK+G,OAEzC,IAAK,IAAI9U,KAAQ8U,EACV,SAAUA,EAAO9U,KACtB8U,EAAO9U,GAAMA,KAAOA,GAGtB5H,KAAK0c,OAASA,EAId,IAAI+C,UAAKzN,EAAgB,QAAhBmN,EAAGhT,EAAQsT,aAAK,IAAAN,EAAAA,EAAInf,KAAK2V,KAAK8J,aAAK,IAAAzN,EAAAA,EAAI,MAChDhS,KAAKyf,MAAQ1D,GAAS0D,GAItBzf,KAAK6e,QAAyB,QAAlBO,EAAGjT,EAAQ0S,eAAO,IAAAO,EAAAA,EAAI,CAAA,EAElC,IAAK,IAAIxX,KAAQ5H,KAAK6e,QAAS,CAC9B,IAAIpC,EAASzc,KAAK6e,QAAQjX,GAC1B6U,EAAOpR,OAAPoR,EAAOpR,KAAS,YAChBoR,EAAO7U,OAAP6U,EAAO7U,KAASA,EACjB,CAE6B,IAAA8X,EAAN,QAAnBL,EAACrf,KAAK6e,QAAQrB,aAAK,IAAA6B,GAAlBA,EAAoBnY,KACxBlH,KAAK6e,QAAQrB,MAAQ,IACC,QAArBkC,EAAG1f,KAAK6e,QAAQrB,aAAK,IAAAkC,EAAAA,EAAI,CAAE,EAC3BxY,GAAIiF,EAAQyS,OAAS5e,KAAKkH,KAMxBiF,EAAQwT,WAEX3f,KAAK2f,WAAoC,SAAvBxT,EAAQwT,WAAwB3f,KAAO+d,GAAWtd,IAAI0L,EAAQwT,YAI5E3f,KAAK4f,QAER5f,KAAK2f,WAAa3f,KAAK2V,KAGvB3V,KAAK2f,WAAc3f,KAKjBA,KAAK2f,WAAWE,cACnB7f,KAAK8f,QAAU,CAACpD,EAAQvQ,KAChB,GAKTnM,KAAK+f,SAAW5T,EAAQ4T,SAGxBxf,OAAOC,eAAeR,KAAM,OAAQ,CACnCsB,MAAO0e,GAAQhgB,MAAMigB,UACrBxe,UAAU,EACVF,YAAY,EACZC,cAAc,IAGf2U,GAAMK,IAAI,sBAAuBxW,KAClC,CAEA8f,OAAAA,CAASpD,GAA4B,IAApBwD,QAACA,EAAUjB,IAAE9d,UAAAuC,OAAAvC,QAAAiC,IAAAjC,UAAAiC,GAAAjC,UAAG,GAAA,GAChC,IAAKnB,KAAKmgB,OAAOngB,KAAK2f,YAErB,OADAjD,EAAS1c,KAAKkV,GAAGlV,KAAK2f,WAAYjD,GAC3B1c,KAAK2f,WAAWG,QAAQpD,EAAQ,CAACwD,YAGzC,IAAIrD,EAAYtc,OAAO6f,OAAOpgB,KAAK0c,QAEnC,OAAOA,EAAO2D,OAAM,CAAC1O,EAAGnD,KACvB,IAAI+O,EAAOV,EAAUrO,GAErB,GAAkB,UAAd+O,EAAKlS,MAAoBkS,EAAKjI,MAAO,CACxC,GAAIjD,OAAOC,MAAMX,GAEhB,OAAO,EAGR,IAAKrE,EAAKD,GAAOkQ,EAAKjI,MACtB,YAAgBlS,IAARkK,GAAqBqE,GAAKrE,EAAM4S,UACxB9c,IAARiK,GAAqBsE,GAAKtE,EAAM6S,EACzC,CAEA,OAAO,CAAI,GAEb,CAEA,eAAIL,GACH,OAAOtf,OAAO6f,OAAOpgB,KAAK0c,QAAQ2D,OAAMC,KAAW,UAAWA,IAC/D,CAEA,SAAI1B,GAAS,IAAA2B,EACZ,OAAmBA,QAAZA,EAAIvgB,KAAC6e,eAAO0B,IAAAA,GAAO,QAAPA,EAAZA,EAAc/C,aAAd+C,IAAmBA,OAAnBA,EAAAA,EAAqBrZ,KAAMlH,KAAKkH,EACxC,CAEA,WAAI0Y,GACH,IAAK,IAAI1Y,KAAMlH,KAAK0c,OACnB,GAA6B,UAAzB1c,KAAK0c,OAAOxV,GAAImE,KACnB,OAAO,EAIT,OAAO,CACR,CAEA4S,SAAAA,CAAWxB,GACV,GAAsB,iBAAXA,EAEV,OADAA,EAAS+D,GAAc/D,EAAQzc,MAIhC,IAAI0R,EASJ,OANCA,EAFc,YAAX+K,EAEGlc,OAAO6f,OAAOpgB,KAAK6e,SAAS,GAG5B7e,KAAK6e,QAAQpC,GAGhB/K,GACHA,EAAM8O,GAAc9O,EAAK1R,MAClB0R,GAGD,IACR,CAQAyO,MAAAA,CAAQ3D,GACP,QAAKA,IAIExc,OAASwc,GAASxc,KAAKkH,KAAOsV,GAASxc,KAAKkH,KAAOsV,EAAMtV,GACjE,CAEAgO,EAAAA,CAAIsH,EAAOE,GACV,GAAyB,IAArBvb,UAAUuC,OAAc,CAC3B,MAAM8Z,EAAQwB,GAASxC,IACtBA,EAAOE,GAAU,CAACc,EAAMhB,MAAOgB,EAAMd,OACvC,CAIA,GAFAF,EAAQuB,GAAWtd,IAAI+b,GAEnBxc,KAAKmgB,OAAO3D,GAEf,OAAOE,EAIRA,EAASA,EAAOvL,KAAIQ,GAAKU,OAAOC,MAAMX,GAAK,EAAIA,IAG/C,IAGI8O,EAAiBC,EAHjBC,EAAS3gB,KAAK6a,KACd+F,EAAYpE,EAAM3B,KAItB,IAAK,IAAIrM,EAAI,EAAGA,EAAImS,EAAOjd,QACtBid,EAAOnS,GAAG2R,OAAOS,EAAUpS,IADGA,IAEjCiS,EAAkBE,EAAOnS,GACzBkS,EAAuBlS,EAOzB,IAAKiS,EAEJ,MAAM,IAAItH,MAAO,uCAAsCnZ,YAAYwc,oCAIpE,IAAK,IAAIhO,EAAImS,EAAOjd,OAAS,EAAG8K,EAAIkS,EAAsBlS,IACzDkO,EAASiE,EAAOnS,GAAGgR,OAAO9C,GAI3B,IAAK,IAAIlO,EAAIkS,EAAuB,EAAGlS,EAAIoS,EAAUld,OAAQ8K,IAC5DkO,EAASkE,EAAUpS,GAAG+Q,SAAS7C,GAGhC,OAAOA,CACR,CAEAzH,IAAAA,CAAMuH,EAAOE,GACZ,GAAyB,IAArBvb,UAAUuC,OAAc,CAC3B,MAAM8Z,EAAQwB,GAASxC,IACtBA,EAAOE,GAAU,CAACc,EAAMhB,MAAOgB,EAAMd,OACvC,CAIA,OAFAF,EAAQuB,GAAWtd,IAAI+b,IAEVtH,GAAGlV,KAAM0c,EACvB,CAEA3a,QAAAA,GACC,MAAQ,GAAE/B,KAAK4H,SAAS5H,KAAKkH,KAC9B,CAEA2Z,YAAAA,GACC,IAAInP,EAAM,GAEV,IAAK,IAAIxK,KAAMlH,KAAK0c,OAAQ,CAAA,IAAAoE,EAC3B,IAAIvD,EAAOvd,KAAK0c,OAAOxV,GACnBoO,EAAQiI,EAAKjI,OAASiI,EAAKJ,SAC/BzL,EAAIjL,KAAeqa,QAAXA,EAACxL,aAAK,EAALA,EAAOhI,eAAGwT,EAAAA,EAAI,EACxB,CAEA,OAAOpP,CACR,CAEAqP,gBAAkB,CAAA,EAGlB,cAAW7d,GACV,MAAO,IAAI,IAAIoZ,IAAI/b,OAAO6f,OAAOrC,GAAWW,WAC7C,CAEA,eAAOsC,CAAU9Z,EAAIsV,GAQpB,GAPyB,IAArBrb,UAAUuC,SAEbwD,GADAsV,EAAQrb,UAAU,IACP+F,IAGZsV,EAAQxc,KAAKS,IAAI+b,GAEbxc,KAAK0e,SAASxX,IAAOlH,KAAK0e,SAASxX,KAAQsV,EAC9C,MAAM,IAAIrD,MAAO,wCAAuCjS,MAKzD,GAHAlH,KAAK0e,SAASxX,GAAMsV,EAGK,IAArBrb,UAAUuC,QAAgB8Y,EAAM8C,QACnC,IAAK,IAAI2B,KAASzE,EAAM8C,QACvBtf,KAAKghB,SAASC,EAAOzE,GAIvB,OAAOA,CACR,CAMA,UAAO/b,CAAK+b,GACX,IAAKA,GAASA,aAAiBuB,GAC9B,OAAOvB,EAKR,GAAgB,WAFFnR,GAAKmR,GAEO,CAEzB,IAAI9K,EAAMqM,GAAWW,SAASlC,EAAMzM,eAEpC,IAAK2B,EACJ,MAAM,IAAIrP,UAAW,mCAAkCma,MAGxD,OAAO9K,CACR,CAAC,IAAAwP,IAAAA,EAAA/f,UAAAuC,OAhBoByd,MAAYxQ,MAAAuQ,EAAAA,EAAAA,OAAAE,EAAA,EAAAA,EAAAF,EAAAE,IAAZD,EAAYC,EAAAjgB,GAAAA,UAAAigB,GAkBjC,GAAID,EAAazd,OAChB,OAAOqa,GAAWtd,OAAO0gB,GAG1B,MAAM,IAAI9e,UAAW,GAAEma,+BACxB,CAUA,mBAAO6E,CAAcC,EAAKC,GACzB,IACI/E,EAAO8D,EADPkB,EAAYnW,GAAKiW,GA4BrB,GAzBkB,WAAdE,EACCF,EAAIjT,SAAS,MAEfmO,EAAO8D,GAASgB,EAAI1e,MAAM,MAI1B4Z,EAAO8D,GAAS,CAAA,CAAGgB,GAGb3Q,MAAMD,QAAQ4Q,IACrB9E,EAAO8D,GAASgB,GAIjB9E,EAAQ8E,EAAI9E,MACZ8D,EAAQgB,EAAIG,SAGbjF,EAAQuB,GAAWtd,IAAI+b,GAElBA,IACJA,EAAQ+E,IAGJ/E,EACJ,MAAM,IAAIna,UAAW,uCAAsCif,4EAK5D,GAFAE,EAAYnW,GAAKiV,GAEC,WAAdkB,GAAwC,WAAdA,GAA0BlB,GAAS,EAAG,CAEnE,IAAI/C,EAAOhd,OAAOqc,QAAQJ,EAAME,QAAQ4D,GAExC,GAAI/C,EACH,MAAO,CAACf,QAAOtV,GAAIqW,EAAK,GAAI/P,MAAO8S,KAAU/C,EAAK,GAEpD,CAEAf,EAAQuB,GAAWtd,IAAI+b,GAEvB,IAAIkF,EAAkBpB,EAAMvQ,cAExBvB,EAAI,EACR,IAAK,IAAItH,KAAMsV,EAAME,OAAQ,CAAA,IAAAiF,EAC5B,IAAIpE,EAAOf,EAAME,OAAOxV,GAExB,GAAIA,EAAG6I,gBAAkB2R,IAA4B,QAATC,EAAApE,EAAK3V,YAAI,IAAA+Z,OAAA,EAATA,EAAW5R,iBAAkB2R,EACxE,MAAO,CAAClF,QAAOtV,KAAIsG,MAAOgB,KAAM+O,GAGjC/O,GACD,CAEA,MAAM,IAAInM,UAAW,OAAMie,0BAA8B9D,EAAM5U,8BAA8BrH,OAAO6J,KAAKoS,EAAME,QAAQ5Q,KAAK,QAC7H,CAEAiV,sBAAwB,CACvB1V,KAAM,YACNzD,KAAM,SAIR,SAASoY,GAASxD,GACjB,IAAI9K,EAAM,CAAC8K,GAEX,IAAK,IAAIoF,EAAIpF,EAAOoF,EAAIA,EAAEjM,MACzBjE,EAAIjL,KAAKmb,GAGV,OAAOlQ,CACR,CAEA,SAAS8O,GAAe/D,GAAuB,IAAfC,OAACA,GAAOvb,UAAAuC,OAAAvC,QAAAiC,IAAAjC,UAAAiC,GAAAjC,UAAG,GAAA,GAC1C,GAAIsb,EAAOC,SAAWD,EAAOpH,aAAc,CAC1CoH,EAAOpR,OAAPoR,EAAOpR,KAAS,YAChBoR,EAAO7U,OAAP6U,EAAO7U,KAAS,SAGhB6U,EAAOpH,aAAeF,GAAkBsH,EAAOC,QAE/C,IAAImF,EAAethB,OAAOqc,QAAQF,GAAQvL,KAAI,CAAA2Q,EAAkBtT,KAAM,IAAtBtH,EAAI2V,GAAUiF,EAEzDC,EAAatF,EAAOpH,aAAa7G,GAAG,GAEpCyO,EAAYJ,EAAUvH,OAASuH,EAAUM,SACzCD,EAAU6E,EAAWzM,MAAO0M,EAAS,GAWzC,MARkB,gBAAdD,GACH7E,EAAU,CAAC,EAAG,KACd8E,EAAS,KAEa,WAAdD,IACRC,EAAS,OAGF,CAAC/E,YAAWC,UAAS8E,SAAO,IAGrCvF,EAAOwF,gBAAkB,CAACvF,EAAQzK,IAC1ByK,EAAOvL,KAAI,CAACQ,EAAGnD,KACrB,IAAIyO,UAACA,EAASC,QAAEA,EAAO8E,OAAEA,GAAUH,EAAarT,GAQhD,OANIyO,GAAaC,IAChBvL,EAAIqD,GAASiI,EAAWC,EAASvL,IAGlCA,EAAII,GAAgBJ,EAAG,CAACM,YAAWC,KAAM8P,GAEjC,GAGX,CAEA,OAAOvF,CACR,CCrbA,IAAeyF,GAAA,IAAInE,GAAW,CAC7B7W,GAAI,UACJU,KAAM,UACN8U,OAAQ,CACPzP,EAAG,CAACrF,KAAM,KACVua,EAAG,CAACva,KAAM,KACVwa,EAAG,CAACxa,KAAM,MAEX6X,MAAO,MACPZ,QAAS,CACRrB,MAAO,CACNK,IAAK,CAAC,UAAW,SAGnByB,QAAS,CAAC,SCPI,MAAM+C,WAAsBtE,GAU1CxR,WAAAA,CAAaJ,GAAS,IAAAmW,EAsBqBC,EAAAC,GArBrCrW,EAAQuQ,SACZvQ,EAAQuQ,OAAS,CAChB+F,EAAG,CACFnN,MAAO,CAAC,EAAG,GACX1N,KAAM,OAEP8a,EAAG,CACFpN,MAAO,CAAC,EAAG,GACX1N,KAAM,SAEPuT,EAAG,CACF7F,MAAO,CAAC,EAAG,GACX1N,KAAM,UAKJuE,EAAQwJ,OACZxJ,EAAQwJ,KAAOgN,IAGZxW,EAAQyW,SAAWzW,EAAQ0W,aAChBN,QAAdA,EAAApW,EAAQqT,cAAM+C,IAAAA,IAAdpW,EAAQqT,OAAWsD,IAClB,IAAIC,EAAMhS,GAAiB5E,EAAQyW,QAASE,GAO5C,OALI9iB,KAAKyf,QAAUzf,KAAK2V,KAAK8J,QAE5BsD,EAAM/G,GAAMhc,KAAKyf,MAAOzf,KAAK2V,KAAK8J,MAAOsD,IAGnCA,CAAG,GAGKP,QAAhBA,EAAArW,EAAQoT,gBAAQiD,IAAAA,IAAhBrW,EAAQoT,SAAawD,IACpBA,EAAM/G,GAAMhc,KAAK2V,KAAK8J,MAAOzf,KAAKyf,MAAOsD,GAClChS,GAAiB5E,EAAQ0W,UAAWE,MAI7BT,QAAhBA,EAAAnW,EAAQ4T,gBAAQuC,IAAAA,IAAhBnW,EAAQ4T,SAAa,WAErBiD,MAAM7W,EACP,ECrDc,SAAS8W,GAAQzF,EAAOhB,GAGtC,OAFAgB,EAAQwB,GAASxB,IAEZhB,GAASgB,EAAMhB,MAAM2D,OAAO3D,GAEzBgB,EAAMd,OAAOza,SAGrBua,EAAQuB,GAAWtd,IAAI+b,IACVvH,KAAKuI,EACnB,CCfe,SAAS/c,GAAK+c,EAAO0F,GACnC1F,EAAQwB,GAASxB,GAEjB,IAAIhB,MAACA,EAAKhP,MAAEA,GAASuQ,GAAWsD,aAAa6B,EAAM1F,EAAMhB,OAEzD,OADayG,GAAOzF,EAAOhB,GACbhP,EACf,CCPe,SAAS2V,GAAQ3F,EAAOhB,EAAOE,GAK7C,OAJAc,EAAQwB,GAASxB,GAEjBhB,EAAQuB,GAAWtd,IAAI+b,GACvBgB,EAAMd,OAASF,EAAMtH,GAAGsI,EAAMhB,MAAOE,GAC9Bc,CACR,CCDe,SAASjT,GAAKiT,EAAO0F,EAAM5hB,GAGzC,GAFAkc,EAAQwB,GAASxB,GAEQ,IAArBrc,UAAUuC,QAAuC,WAAvB2H,GAAKlK,UAAU,IAAkB,CAE9D,IAAI8I,EAAS9I,UAAU,GACvB,IAAK,IAAIiQ,KAAKnH,EACbM,GAAIiT,EAAOpM,EAAGnH,EAAOmH,GAEvB,KACK,CACiB,mBAAV9P,IACVA,EAAQA,EAAMb,GAAI+c,EAAO0F,KAG1B,IAAI1G,MAACA,EAAKhP,MAAEA,GAASuQ,GAAWsD,aAAa6B,EAAM1F,EAAMhB,OACrDE,EAASuG,GAAOzF,EAAOhB,GAC3BE,EAAOlP,GAASlM,EAChB6hB,GAAO3F,EAAOhB,EAAOE,EACtB,CAEA,OAAOc,CACR,CDnBA2F,GAAOC,QAAU,QCqBjB7Y,GAAI6Y,QAAU,QC5Bd,IAAeC,GAAA,IAAItF,GAAW,CAC7B7W,GAAI,UACJU,KAAM,UACN6X,MAAO,MACP9J,KAAMgN,GACNpD,SAAU7C,GAAUV,GAAM2G,GAAQlD,MAAO,MAAO/C,GAChD8C,OAAQ9C,GAAUV,GAAM,MAAO2G,GAAQlD,MAAO/C,KCL/C,MACM4G,GAAK,GAAK,IACVC,GAAI,MAAQ,GAElB,IAAI9D,GAAQ7D,GAAOC,IAEnB,IAAe2H,GAAA,IAAIzF,GAAW,CAC7B7W,GAAI,MACJU,KAAM,MACN8U,OAAQ,CACP+G,EAAG,CACFtG,SAAU,CAAC,EAAG,KACdvV,KAAM,aAEPW,EAAG,CACF4U,SAAU,EAAE,IAAK,MAElBhC,EAAG,CACFgC,SAAU,EAAE,IAAK,aAMnBsC,GAEA9J,KAAM+N,GAGNnE,QAAAA,CAAUpD,GAET,IAGItT,EAHMsT,EAAIhL,KAAI,CAAC7P,EAAOkN,IAAMlN,EAAQme,GAAMjR,KAGlC2C,KAAI7P,GAASA,EAlCjB,oBAkC6B3B,KAAKgkB,KAAKriB,IAAUiiB,GAAIjiB,EAAQ,IAAM,MAE3E,MAAO,CACL,IAAMuH,EAAE,GAAM,GACf,KAAOA,EAAE,GAAKA,EAAE,IAChB,KAAOA,EAAE,GAAKA,EAAE,IAEjB,EAID2W,MAAAA,CAAQoE,GAEP,IAAI/a,EAAI,GAaR,OAZAA,EAAE,IAAM+a,EAAI,GAAK,IAAM,IACvB/a,EAAE,GAAK+a,EAAI,GAAK,IAAM/a,EAAE,GACxBA,EAAE,GAAKA,EAAE,GAAK+a,EAAI,GAAK,IAGb,CACT/a,EAAE,GAAOya,GAAK3jB,KAAKkkB,IAAIhb,EAAE,GAAI,IAAqB,IAAMA,EAAE,GAAK,IAAM0a,GACrEK,EAAI,GAAK,EAAKjkB,KAAKkkB,KAAKD,EAAI,GAAK,IAAM,IAAK,GAAKA,EAAI,GAAKL,GAC1D1a,EAAE,GAAOya,GAAK3jB,KAAKkkB,IAAIhb,EAAE,GAAI,IAAqB,IAAMA,EAAE,GAAK,IAAM0a,IAI3DpS,KAAI,CAAC7P,EAAOkN,IAAMlN,EAAQme,GAAMjR,IAC3C,EAEDqQ,QAAS,CACR2E,IAAO,CACN9G,OAAQ,CAAC,0BAA2B,gCAAiC,qCCtEjE,SAASoH,GAAWC,GAC1B,OAASA,EAAQ,IAAO,KAAO,GAChC,CCEA,IAAeC,GAAA,IAAIjG,GAAW,CAC7B7W,GAAI,MACJU,KAAM,MACN8U,OAAQ,CACP+G,EAAG,CACFtG,SAAU,CAAC,EAAG,KACdvV,KAAM,aAEP+J,EAAG,CACFwL,SAAU,CAAC,EAAG,KACdvV,KAAM,UAEPqc,EAAG,CACF9G,SAAU,CAAC,EAAG,KACd9R,KAAM,QACNzD,KAAM,QAIR+N,KAAMiO,GACNrE,QAAAA,CAAUqE,GAET,IACIM,GADCC,EAAG5b,EAAG4S,GAAKyI,EAWhB,OANCM,EADGvkB,KAAKgT,IAAIpK,GAFH,KAEa5I,KAAKgT,IAAIwI,GAFtB,IAGH/G,IAGmB,IAAnBzU,KAAKykB,MAAMjJ,EAAG5S,GAAW5I,KAAK4T,GAG9B,CACN4Q,EACAxkB,KAAK0kB,KAAK9b,GAAK,EAAI4S,GAAK,GACxBmJ,GAAeJ,GAEhB,EACD1E,MAAAA,CAAQ+E,GAEP,IAAKC,EAAWC,EAAQC,GAAOH,EAS/B,OAPIE,EAAS,IACZA,EAAS,GAGNnS,MAAMoS,KACTA,EAAM,GAEA,CACNF,EACAC,EAAS9kB,KAAKglB,IAAID,EAAM/kB,KAAK4T,GAAK,KAClCkR,EAAS9kB,KAAKilB,IAAIF,EAAM/kB,KAAK4T,GAAK,KAEnC,EAEDsL,QAAS,CACRmF,IAAO,CACNtH,OAAQ,CAAC,0BAA2B,0BAA2B,0BClDlE,MAAMmI,GAAU,IAAM,EAChBC,GAAInlB,KAAK4T,GACTwR,GAAM,IAAMD,GACZE,GAAMF,GAAI,IAEhB,SAASG,GAAMhY,GAGd,MAAMiY,EAAKjY,EAAIA,EAGf,OAFWiY,EAAKA,EAAKA,EAAKjY,CAG3B,CAEe,SAAAkY,GAAU3H,EAAO4H,GAAuC,IAA/BC,GAACA,EAAK,EAACC,GAAEA,EAAK,EAACC,GAAEA,EAAK,GAAEpkB,UAAAuC,OAAAvC,QAAAiC,IAAAjC,UAAAiC,GAAAjC,UAAG,GAAA,IACjEqc,EAAO4H,GAAUpG,GAAS,CAACxB,EAAO4H,IAanC,IAAKI,EAAIC,EAAIC,GAAMlC,GAAIvO,KAAKuI,GACxBmI,EAAK3B,GAAI/O,KAAKuO,GAAK,CAACgC,EAAIC,EAAIC,IAAK,IAChCE,EAAIC,EAAIC,GAAMtC,GAAIvO,KAAKmQ,GACxBW,EAAK/B,GAAI/O,KAAKuO,GAAK,CAACoC,EAAIC,EAAIC,IAAK,GAMjCH,EAAK,IACRA,EAAK,GAEFI,EAAK,IACRA,EAAK,GAGN,IAIIC,EAAKf,IAJGU,EAAKI,GAAM,GAMnBE,EAAI,IAAO,EAAItmB,KAAK0kB,KAAK2B,GAAMA,EAAKnB,MAIpCqB,GAAU,EAAID,GAAKR,EACnBU,GAAU,EAAIF,GAAKJ,EAGnBO,EAASzmB,KAAK0kB,KAAK6B,GAAU,EAAIR,GAAM,GACvCW,EAAS1mB,KAAK0kB,KAAK8B,GAAU,EAAIL,GAAM,GAKvCQ,EAAiB,IAAXJ,GAAuB,IAAPR,EAAY,EAAI/lB,KAAKykB,MAAMsB,EAAIQ,GACrDK,EAAiB,IAAXJ,GAAuB,IAAPL,EAAY,EAAInmB,KAAKykB,MAAM0B,EAAIK,GAErDG,EAAK,IACRA,GAAM,EAAIxB,IAEPyB,EAAK,IACRA,GAAM,EAAIzB,IAGXwB,GAAMvB,GACNwB,GAAMxB,GAGN,IAOIyB,EAPAC,EAAKb,EAAKJ,EACVkB,EAAKL,EAASD,EAGdO,EAAQJ,EAAKD,EACbM,EAAON,EAAKC,EACZM,EAAOlnB,KAAKgT,IAAIgU,GAGhBP,EAASC,GAAW,EACvBG,EAAK,EAEGK,GAAQ,IAChBL,EAAKG,EAEGA,EAAQ,IAChBH,EAAKG,EAAQ,IAELA,GAAS,IACjBH,EAAKG,EAAQ,IAGbhQ,GAASM,KAAK,gCAIf,IAUI6P,EAVAC,EAAK,EAAIpnB,KAAK0kB,KAAKgC,EAASD,GAAUzmB,KAAKilB,IAAI4B,EAAKxB,GAAM,GAG1DgC,GAASxB,EAAKI,GAAM,EACpBqB,GAASb,EAASC,GAAU,EAC5Ba,EAASjC,GAAKgC,GAOjBH,EADGV,EAASC,GAAW,EACfO,EAEAC,GAAQ,IACRD,EAAO,EAEPA,EAAO,KACNA,EAAO,KAAO,GAGdA,EAAO,KAAO,EAQxB,IAAIO,GAAOH,EAAQ,KAAO,EACtBI,EAAK,EAAM,KAAQD,EAAOxnB,KAAK0kB,KAAK,GAAK8C,GAGzCE,EAAK,EAAI,KAAQJ,EAGjBK,EAAI,EACRA,GAAM,IAAO3nB,KAAKglB,KAAUmC,EAAQ,IAAO9B,IAC3CsC,GAAM,IAAO3nB,KAAKglB,IAAM,EAAImC,EAAe9B,IAC3CsC,GAAM,IAAO3nB,KAAKglB,KAAM,EAAImC,EAAS,GAAM9B,IAC3CsC,GAAM,GAAO3nB,KAAKglB,KAAM,EAAImC,EAAS,IAAM9B,IAI3C,IAAIuC,EAAK,EAAI,KAAQN,EAAQK,EAMzBE,EAAK,GAAK7nB,KAAKiW,KAAK,IAAOkR,EAAQ,KAAO,KAAO,GACjDW,EAAK,EAAI9nB,KAAK0kB,KAAK6C,GAAUA,EAASrC,KAItC6C,GAAMjB,GAAMpB,EAAK+B,KAAQ,EAI7B,OAHAM,IAAOhB,GAAMpB,EAAK+B,KAAQ,EAC1BK,IAAOX,GAAMxB,EAAKgC,KAAQ,EAC1BG,IANU,EAAI/nB,KAAKilB,IAAI,EAAI4C,EAAKxC,IAAOyC,GAM3Bf,GAAMpB,EAAK+B,KAAQN,GAAMxB,EAAKgC,IACnC5nB,KAAK0kB,KAAKqD,EAElB,CC5KA,MAAMC,GAAa,CAClB,CAAE,iBAAoB,mBAAqB,mBAC3C,CAAE,kBAAoB,kBAAqB,mBAC3C,CAAE,kBAAoB,kBAAqB,oBAGtCC,GAAa,CAClB,CAAG,oBAAqB,kBAAqB,mBAC7C,EAAG,kBAAqB,mBAAqB,mBAC7C,EAAG,mBAAqB,kBAAqB,qBAExCC,GAAa,CAClB,CAAE,iBAAqB,mBAAqB,mBAC5C,CAAE,oBAAqB,iBAAqB,kBAC5C,CAAE,kBAAqB,mBAAqB,oBAGvCC,GAAa,CAClB,CAAE,EAAqB,kBAAqB,mBAC5C,CAAE,GAAqB,mBAAqB,mBAC5C,CAAE,GAAqB,mBAAqB,qBAG7C,IAAeC,GAAA,IAAIhK,GAAW,CAC7B7W,GAAI,QACJU,KAAM,QACN8U,OAAQ,CACP+G,EAAG,CACFtG,SAAU,CAAC,EAAG,GACdvV,KAAM,aAEPW,EAAG,CACF4U,SAAU,EAAE,GAAK,KAElBhC,EAAG,CACFgC,SAAU,EAAE,GAAK,MAKnBsC,MAAO,MACP9J,KAAMgN,GACNpD,QAAAA,CAAUpD,GAET,IAGI6L,EAHMjX,GAAiB4W,GAAYxL,GAGxBhL,KAAIpL,GAAOpG,KAAKgkB,KAAK5d,KAEpC,OAAOgL,GAAiB8W,GAAYG,EAEpC,EACDxI,MAAAA,CAAQuI,GAEP,IAGIE,EAHOlX,GAAiB+W,GAAYC,GAGzB5W,KAAIpL,GAAOA,GAAO,IAEjC,OAAOgL,GAAiB6W,GAAYK,EACpC,EAEDpJ,QAAS,CACRqJ,MAAS,CACRxL,OAAQ,CAAC,0BAA2B,gCAAiC,qCChEzD,SAAAyL,GAAU3K,EAAO4H,IAC9B5H,EAAO4H,GAAUpG,GAAS,CAACxB,EAAO4H,IAKnC,IAAKI,EAAIC,EAAIC,GAAMwC,GAAMjT,KAAKuI,IACzBoI,EAAIC,EAAIC,GAAMoC,GAAMjT,KAAKmQ,GAC1BqB,EAAKjB,EAAKI,EACVwC,EAAK3C,EAAKI,EACVwC,EAAK3C,EAAKI,EACd,OAAOnmB,KAAK0kB,KAAKoC,GAAM,EAAI2B,GAAM,EAAIC,GAAM,EAC5C,CCfA,MAAMpJ,GAAI,MAMK,SAASa,GAAStC,EAAOhB,GAA2B,IAApB0D,QAACA,EAAUjB,IAAE9d,UAAAuC,OAAAvC,QAAAiC,IAAAjC,UAAAiC,GAAAjC,UAAG,GAAA,GAC9Dqc,EAAQwB,GAASxB,GAEZhB,IACJA,EAAQgB,EAAMhB,OAGfA,EAAQuB,GAAWtd,IAAI+b,GACvB,IAAIE,EAASc,EAAMd,OAMnB,OAJIF,IAAUgB,EAAMhB,QACnBE,EAASF,EAAMvH,KAAKuI,IAGdhB,EAAMsD,QAAQpD,EAAQ,CAACwD,WAC/B,CCxBe,SAASoI,GAAO9K,GAC9B,MAAO,CACNhB,MAAOgB,EAAMhB,MACbE,OAAQc,EAAMd,OAAOza,QACrBqS,MAAOkJ,EAAMlJ,MAEf,CCDe,SAASiU,GAAUC,EAAQC,GAAuB,IAAfjM,EAAKrb,UAAAuC,OAAA,QAAAN,IAAAjC,UAAA,GAAAA,UAAA,GAAG,MACzDqb,EAAQuB,GAAWtd,IAAI+b,GAGvB,IAAIkM,EAAUlM,EAAMvH,KAAKuT,GACrBG,EAAUnM,EAAMvH,KAAKwT,GAEzB,OAAO9oB,KAAK0kB,KAAKqE,EAAQE,QAAO,CAACC,EAAKC,EAAIta,KACzC,IAAIua,EAAKJ,EAAQna,GACjB,OAAI8D,MAAMwW,IAAOxW,MAAMyW,GACfF,EAGDA,GAAOE,EAAKD,IAAO,CAAC,GACzB,GACJ,CCRA,MACM9D,GADIrlB,KAAK4T,GACC,ICRhB,IAAeyV,GAAA,IAAIjL,GAAW,CAK7B7W,GAAI,cACJ0X,MAAO,gBACPhX,KAAM,mBACN8U,OAAQ,CACPzP,EAAG,CACFkQ,SAAU,CAAC,EAAG,QACdvV,KAAM,MAEPua,EAAG,CACFhF,SAAU,CAAC,EAAG,KACdvV,KAAM,MAEPwa,EAAG,CACFjF,SAAU,CAAC,EAAG,SACdvV,KAAM,OAIR+N,KAAMgN,GACNpD,SAAUpD,GAIFA,EAAIhL,KAAK8X,GAAKtpB,KAAK0N,IA9BjB,IA8BqB4b,EAAQ,KAEvCzJ,OAAQ0J,GAEAA,EAAO/X,KAAI8X,GAAKtpB,KAAK0N,IAAI4b,EAlCvB,IAkC+B,OCjC1C,MAAM9N,GAAI,KACJuH,GAAI,IACJxV,GAAI,KAAQ,MAEZ4b,GAAK,SACLC,GAAK,KAAQ,IACbI,GAAK,QAELC,GAAQ,IAAW,IAAM,MACzBtT,IAAK,IACLuT,GAAK,sBAELC,GAAc,CACnB,CAAG,UAAY,QAAW,SAC1B,EAAG,OAAY,SAAW,UAC1B,EAAG,SAAY,MAAW,WAGrBC,GAAc,CACnB,CAAG,oBAAsB,mBAAqB,kBAC9C,CAAG,mBAAsB,mBAAqB,oBAC9C,EAAG,oBAAsB,kBAAqB,qBAEzCC,GAAc,CACnB,CAAG,GAAW,GAAW,GACzB,CAAG,OAAW,SAAW,SACzB,CAAG,QAAW,UAAW,WAGpBC,GAAc,CACnB,CAAE,EAAqB,kBAAsB,oBAC7C,CAAE,mBAAqB,mBAAsB,oBAC7C,CAAE,mBAAqB,oBAAsB,oBAG9C,IAAeC,GAAA,IAAI3L,GAAW,CAC7B7W,GAAI,SACJU,KAAM,SACN8U,OAAQ,CACPiN,GAAI,CACHxM,SAAU,CAAC,EAAG,GACdvV,KAAM,MAEPgiB,GAAI,CACHzM,SAAU,EAAE,GAAK,KAElB0M,GAAI,CACH1M,SAAU,EAAE,GAAK,MAInBxH,KAAMqT,GACNzJ,QAAAA,CAAUpD,GAMT,IAAM2N,EAAIC,EAAIC,GAAO7N,EAUjB8N,EAHMlZ,GAAiBuY,GAAa,CAJ9BnO,GAAI2O,GAAQ3O,GAAI,GAAK6O,EACrBtH,GAAIqH,GAAQrH,GAAI,GAAKoH,EAGmBE,IAGlC7Y,KAAK,SAAUpL,GAI9B,QAHU+iB,GAAMC,IAAOhjB,EAAM,MAAUmH,KAC3B,EAAKic,IAAOpjB,EAAM,MAAUmH,MA/DjC,kBAkER,KAGMgd,EAAIN,EAAIC,GAAM9Y,GAAiByY,GAAaS,GAIlD,MAAO,EADI,EAAInU,IAAKoU,GAAO,EAAKpU,GAAIoU,GAAOb,GAC/BO,EAAIC,EAChB,EACDrK,MAAAA,CAAQkK,GACP,IAAKS,EAAIP,EAAIC,GAAMH,EAOfzB,EAHQlX,GAAiB0Y,GAAa,EAHhCU,EAAKd,KAAO,EAAIvT,GAAIA,IAAKqU,EAAKd,KAGQO,EAAIC,IAGpC1Y,KAAI,SAAUpL,GAK7B,OAFQ,MAFG+iB,GAAM/iB,GAAOqjB,KACXD,GAAMpjB,GAAOqjB,GAASL,MAzFxB,iBA6FZ,KAGMqB,EAAIC,EAAIL,GAAOjZ,GAAiBwY,GAAatB,GAG/C6B,GAAMM,GAAOjP,GAAI,GAAK6O,GAAO7O,GAEjC,MAAO,CAAE2O,GADCO,GAAO3H,GAAI,GAAKoH,GAAOpH,GAChBsH,EACjB,EAEDnL,QAAS,CAERrB,MAAS,CACRd,OAAQ,CAAC,0BAA2B,gCAAiC,qCC9GzD4N,GAAA,IAAIvM,GAAW,CAC7B7W,GAAI,SACJU,KAAM,SACN8U,OAAQ,CACPiN,GAAI,CACHxM,SAAU,CAAC,EAAG,GACdvV,KAAM,MAEP2iB,GAAI,CACHpN,SAAU,CAAC,EAAG,GACdvV,KAAM,UAEP4iB,GAAI,CACHrN,SAAU,CAAC,EAAG,KACd9R,KAAM,QACNzD,KAAM,QAIR+N,KAAM+T,GACNnK,QAAAA,CAAUkL,GAET,IACIvG,GADCiG,EAAIP,EAAIC,GAAMY,EAEnB,MAAMxL,EAAI,KASV,OANCiF,EADGvkB,KAAKgT,IAAIiX,GAAM3K,GAAKtf,KAAKgT,IAAIkX,GAAM5K,EAChC7K,IAGqB,IAArBzU,KAAKykB,MAAMyF,EAAID,GAAYjqB,KAAK4T,GAGhC,CACN4W,EACAxqB,KAAK0kB,KAAKuF,GAAM,EAAIC,GAAM,GAC1BvF,GAAeJ,GAEhB,EACD1E,OAAQ8K,GAGA,CACNA,EAAO,GACPA,EAAO,GAAK3qB,KAAKglB,IAAI2F,EAAO,GAAK3qB,KAAK4T,GAAK,KAC3C+W,EAAO,GAAK3qB,KAAKilB,IAAI0F,EAAO,GAAK3qB,KAAK4T,GAAK,QC7C9C,MAAMuV,GAAK,SACLC,GAAK,KAAO,IACZI,GAAK,QACLuB,GAAK,KAAO,MACZC,GAAK,KAAO,GACZC,GAAM,MAAQ,KACdC,GAAM,GAAK,KAIXlD,GAAa,CAClB,CAAG,kBAAqB,mBAAqB,kBAC7C,EAAG,kBAAqB,kBAAqB,mBAC7C,CAAG,kBAAqB,kBAAqB,oBAiBxCmD,GAAa,CAClB,CAAG,GAAe,GAAmB,GACrC,CAAG,KAAO,MAAO,MAAQ,KAAO,KAAO,MACvC,CAAE,MAAQ,MAAO,MAAQ,MAAQ,IAAM,OAIlCC,GAAa,CAClB,CAAE,kBAAqB,kBAAqB,kBAC5C,CAAE,mBAAqB,mBAAqB,mBAC5C,CAAE,kBAAqB,mBAAqB,oBASvCnD,GAAa,CAClB,CAAG,oBAAqB,mBAAqB,mBAC7C,CAAG,kBAAqB,mBAAqB,mBAC7C,EAAG,mBAAqB,kBAAqB,qBAU9C,IAAeoD,GAAA,IAAIjN,GAAW,CAC7B7W,GAAI,QACJU,KAAM,QAUN8U,OAAQ,CACPlO,EAAG,CACF2O,SAAU,CAAC,EAAG,GACdvV,KAAM,KAEPqjB,GAAI,CACH9N,SAAU,EAAE,GAAK,IACjBvV,KAAM,MAEPsjB,GAAI,CACH/N,SAAU,EAAE,GAAK,IACjBvV,KAAM,OAIR+N,KAAMqT,GACNzJ,SAAUpD,GAaX,SAAqB8L,GAGpB,IAAIgC,EAAQhC,EAAI9W,KAAK,SAAUpL,GAI9B,QAHU+iB,GAAMC,IAAOhjB,EAAM,MAAU2kB,KAC3B,EAAKvB,IAAOpjB,EAAM,MAAU2kB,MAEfC,EAC1B,IAGA,OAAO5Z,GAAiB+Z,GAAYb,EACrC,CArBSkB,CAFGpa,GAAiB4W,GAAYxL,IAIxCqD,MAAAA,CAAQ4L,GACP,IAAInD,EAoBN,SAAqBmD,GACpB,IAAInB,EAAQlZ,GAAiBga,GAAYK,GAGrCnD,EAAMgC,EAAM9Y,KAAK,SAAUpL,GAG9B,OAAO,KAFIpG,KAAK0N,IAAKtH,GAAO8kB,GAAO/B,GAAI,IAC1BC,GAAMI,GAAMpjB,GAAO8kB,MACCD,EAClC,IAEA,OAAO3C,CACR,CA/BYoD,CAAWD,GAErB,OAAOra,GAAiB6W,GAAYK,EACrC,IClGD,MAAMxI,GAAQ7D,GAAOE,IACfwP,GAAc,IACdC,GAAiB,EAAID,GACrBE,GAAM,EAAI7rB,KAAK4T,GAEfkY,GAAQ,CACb,CAAG,QAAW,SAAW,SACzB,EAAG,QAAW,SAAW,SACzB,EAAG,QAAW,QAAW,UAGpBC,GAAW,CAChB,CAAC,oBAAqB,mBAAoB,oBAC1C,CAAC,mBAAqB,mBAAqB,qBAC3C,EAAE,qBAAuB,mBAAqB,qBAGzChB,GAAK,CACV,CAAC,IAAO,IAAO,KACf,CAAC,KAAQ,KAAQ,KACjB,CAAC,KAAQ,KAAQ,OAGZiB,GAAc,CACnBC,KAAM,CAAC,GAAK,KAAO,IACnBC,IAAK,CAAC,GAAK,IAAM,IACjBC,QAAS,CAAC,EAAG,IAAM,IAGdC,GAAa,CAElB9H,EAAG,CAAC,MAAO,GAAO,OAAQ,OAAQ,QAClC+H,EAAG,CAAC,GAAK,GAAK,EAAK,IAAK,IACxBC,EAAG,CAAC,EAAK,IAAO,IAAO,IAAO,MAGzBC,GAAU,IAAMvsB,KAAK4T,GACrB4Y,GAAUxsB,KAAK4T,GAAK,IAEnB,SAASyI,GAAOU,EAAQ0P,GAC9B,MAAMC,EAAO3P,EAAOvL,KAAIQ,IACvB,MAAM1E,EAAIyI,GAAK0W,EAAKzsB,KAAKgT,IAAIhB,GAAK,IAAM2Z,IACxC,OAAO,IAAM9V,GAASvI,EAAG0E,IAAM1E,EAAI,MAAM,IAE1C,OAAOof,CACR,CAsCO,SAASC,GACfC,EACAC,EACAC,EACAC,EACAC,GAGA,MAAMlW,EAAM,CAAA,EAEZA,EAAIkW,YAAcA,EAClBlW,EAAI8V,SAAWA,EACf9V,EAAIiW,SAAWA,EACf,MAAME,EAAOL,EAASpb,KAAIQ,GACd,IAAJA,IAIR8E,EAAIoW,GAAKL,EAET/V,EAAIqW,GAAKL,EAET,MAAMM,EAAKH,EAAK,GAGVI,EAAOjc,GAAiB0a,GAAOmB,GAI/B/jB,GADN6jB,EAAWf,GAAYlV,EAAIiW,WACR,GACnBjW,EAAI9E,EAAI+a,EAAS,GACjBjW,EAAIwW,GAAKP,EAAS,GAElB,MACMQ,GADI,GAAK,EAAIzW,EAAIoW,GAAK,KACZ,EAGhBpW,EAAI2V,GAAMc,EAAKzW,EAAIoW,GAAK,IAAO,EAAIK,IAAO,EAAIA,GAAMvtB,KAAKgkB,KAAK,EAAIlN,EAAIoW,IACtEpW,EAAI0W,OAAS1W,EAAI2V,IAAM,IAEvB3V,EAAIvJ,EAAIuJ,EAAIqW,GAAKC,EACjBtW,EAAI2L,EAAI,KAAOziB,KAAK0kB,KAAK5N,EAAIvJ,GAC7BuJ,EAAI2W,IAAM,KAAS3W,EAAIvJ,IAAM,GAC7BuJ,EAAI4W,IAAM5W,EAAI2W,IAId,MAAMtX,EAAK6W,EACV,EACAhtB,KAAK0N,IACJ1N,KAAK2N,IAAIzE,GAAK,EAAI,EAAI,IAAMlJ,KAAKiW,MAAMa,EAAIoW,GAAK,IAAM,KAAM,GAC5D,GAEFpW,EAAI6W,KAAON,EAAK7b,KAAIQ,GACZiD,GAAY,EAAGmY,EAAKpb,EAAGmE,KAE/BW,EAAI8W,QAAU9W,EAAI6W,KAAKnc,KAAIQ,GACnB,EAAIA,IAIZ,MAAM6b,EAAQR,EAAK7b,KAAI,CAACQ,EAAGnD,IACnBmD,EAAI8E,EAAI6W,KAAK9e,KAEfif,EAAQzR,GAAMwR,EAAO/W,EAAI2V,IAK/B,OAJA3V,EAAIiX,GAAKjX,EAAI2W,KAAO,EAAIK,EAAM,GAAKA,EAAM,GAAK,IAAOA,EAAM,IAIpDhX,CACR,CAGA,MAAMkX,GAAoBrB,GACzB7M,GACA,GAAK9f,KAAK4T,GAAK,GAAK,GACpB,WACA,GAGM,SAASqa,GAAWC,EAAOpX,GAIjC,UAAmBrT,IAAZyqB,EAAMC,OAAgC1qB,IAAZyqB,EAAME,GACtC,MAAM,IAAI5U,MAAM,oDAGjB,UAAmB/V,IAAZyqB,EAAM9T,OAAgC3W,IAAZyqB,EAAMzR,OAAgChZ,IAAZyqB,EAAMjM,GAChE,MAAM,IAAIzI,MAAM,yDAIjB,UAAmB/V,IAAZyqB,EAAM5J,OAAgC7gB,IAAZyqB,EAAM5B,GACtC,MAAM,IAAI9S,MAAM,oDAIjB,GAAgB,IAAZ0U,EAAMC,GAAyB,IAAZD,EAAME,EAC5B,MAAO,CAAC,EAAK,EAAK,GAInB,IAAIC,EAAO,EAEVA,OADe5qB,IAAZyqB,EAAM5J,EACFH,GAAU+J,EAAM5J,GAAKkI,GAtHvB,SAA2BF,GACjC,IAAIgC,GAAOhC,EAAI,IAAM,KAAO,IAC5B,MAAMzd,EAAI7O,KAAKoN,MAAM,IAAOkhB,GAC5BA,GAAU,IACV,MAAOhY,EAAIiY,GAAOnC,GAAW9H,EAAEhiB,MAAMuM,EAAGA,EAAI,IACrC2f,EAAIC,GAAOrC,GAAWC,EAAE/pB,MAAMuM,EAAGA,EAAI,GAE5C,OAAOsV,IACLmK,GAAMG,EAAMnY,EAAKkY,EAAKD,GAAO,IAAMjY,EAAKmY,IACxCH,GAAMG,EAAMD,GAAM,IAAMC,GAE3B,CA8GSC,CAAiBR,EAAM5B,GAAKE,GAGpC,MAAMmC,EAAO3uB,KAAKglB,IAAIqJ,GAChBO,EAAO5uB,KAAKilB,IAAIoJ,GAGtB,IAAIQ,EAAQ,OACIprB,IAAZyqB,EAAMC,EACTU,EAA+B,GAAvB9Y,GAAKmY,EAAMC,EAAG,SAEF1qB,IAAZyqB,EAAME,IACdS,EAAQ,IAAO/X,EAAI9E,EAAIkc,EAAME,IAAMtX,EAAIiX,GAAK,GAAKjX,EAAI0W,SAItD,IAAI7Y,EAAQ,OACIlR,IAAZyqB,EAAM9T,EACTzF,EAAQuZ,EAAM9T,EAAIyU,OAEEprB,IAAZyqB,EAAMzR,EACd9H,EAASuZ,EAAMzR,EAAI3F,EAAI0W,OAAUqB,OAEbprB,IAAZyqB,EAAMjM,IACdtN,EAAQ,KAAUuZ,EAAMjM,GAAK,GAAMnL,EAAIiX,GAAK,GAAKjX,EAAI9E,GAEtD,MAAM8c,EAAI/Y,GACTpB,EAAQ3U,KAAKkkB,IAAI,KAAOlkB,KAAKkkB,IAAI,IAAMpN,EAAIvJ,IAAK,KAChD,GAAK,GAIAwhB,EAAK,KAAQ/uB,KAAKglB,IAAIqJ,EAAO,GAAK,KAGlChd,EAAIyF,EAAIiX,GAAKhY,GAAK8Y,EAAO,EAAI/X,EAAI9E,EAAI8E,EAAI2L,GAGzCuM,EAAK,IAAM,GAAKlY,EAAIwW,GAAKxW,EAAI4W,IAAMqB,EACnCE,EAAK5d,EAAIyF,EAAI2W,IACb3K,EACL,IAAMmM,EAAK,MACX/Y,GAAK4Y,EAAG,GAAKE,EAAKF,GAAK,GAAKH,EAAO,IAAMC,IAMpCM,EAhMA,SAAkBC,EAAS1C,GACjC,MAAM2C,EAAW,IAAM3C,EAAM,OAASb,GACtC,OAAOuD,EAAQ3d,KAAIQ,IAClB,MAAMqd,EAAOrvB,KAAKgT,IAAIhB,GACtB,OAAO6D,GAASuZ,EAAWrZ,GAAKsZ,GAAQ,IAAMA,GAAOzD,IAAiB5Z,EAAE,GAE1E,CA0Lesd,CACble,GAAiB2Z,GAAI,CAACkE,EALbnM,EAAI6L,EACJ7L,EAAI8L,IAIoBpd,KAAIQ,GACzB,EAAJA,EAAQ,OAEhB8E,EAAI2V,IAEL,OAAOrb,GACN2a,GACAmD,EAAM1d,KAAI,CAACQ,EAAGnD,IACNmD,EAAI8E,EAAI8W,QAAQ/e,MAEvB2C,KAAIQ,GACEA,EAAI,KAEb,CAGO,SAASud,GAASC,EAAQ1Y,GAEhC,MAAM2Y,EAASD,EAAOhe,KAAIQ,GACd,IAAJA,IAEF0d,EAAOrT,GACZjL,GAAiB0a,GAAO2D,GAAQje,KAAI,CAACQ,EAAGnD,IAChCmD,EAAI8E,EAAI6W,KAAK9e,KAErBiI,EAAI2V,IAIC7jB,EAAI8mB,EAAK,KAAO,GAAKA,EAAK,GAAKA,EAAK,IAAM,GAC1ClU,GAAKkU,EAAK,GAAKA,EAAK,GAAK,EAAIA,EAAK,IAAM,EACxCrB,GAASruB,KAAKykB,MAAMjJ,EAAG5S,GAAKijB,GAAOA,IAAOA,GAG1CkD,EAAK,KAAQ/uB,KAAKglB,IAAIqJ,EAAO,GAAK,KASlC1Z,EAAQoB,GANb,IAAM,GAAKe,EAAIwW,GAAKxW,EAAI4W,IACxBxX,GACC6Y,EAAK/uB,KAAK0kB,KAAK9b,GAAK,EAAI4S,GAAK,GAC7BkU,EAAK,GAAKA,EAAK,GAAK,KAAOA,EAAK,GAAK,MAGjB,IAAO1vB,KAAKkkB,IAAI,KAAOlkB,KAAKkkB,IAAI,IAAMpN,EAAIvJ,GAAI,KAK9DshB,EAAQ9Y,GAFJe,EAAI2W,KAAO,EAAIiC,EAAK,GAAKA,EAAK,GAAK,IAAOA,EAAK,IAElC5Y,EAAIiX,GAAI,GAAMjX,EAAI9E,EAAI8E,EAAI2L,GAG3C0L,EAAI,IAAMpY,GAAK8Y,EAAO,GAGtBT,EAAK,EAAItX,EAAI9E,EAAI6c,GAAS/X,EAAIiX,GAAK,GAAKjX,EAAI0W,OAG5CpT,EAAIzF,EAAQka,EAGZpS,EAAIrC,EAAItD,EAAI0W,OAGZlJ,EAAIH,GAAUkK,EAAO9B,IAGrBD,EA3PA,SAAwBhI,GAC9B,IAAIqL,EAAKxL,GAAUG,GACfqL,GAAMvD,GAAW9H,EAAE,KACtBqL,GAAM,KAGP,MAAM9gB,EAAIuH,GAAWgW,GAAW9H,EAAGqL,GAAM,GAClCrZ,EAAIiY,GAAOnC,GAAW9H,EAAEhiB,MAAMuM,EAAGA,EAAI,IACrC2f,EAAIC,GAAOrC,GAAWC,EAAE/pB,MAAMuM,EAAGA,EAAI,GAGtCigB,GAAKa,EAAKrZ,GAAMkY,EACtB,OAHWpC,GAAWE,EAAEzd,GAGX,IAAMigB,GAAMA,GAAKP,EAAMoB,GAAMlB,EAC3C,CA8OWmB,CAActL,GAOxB,MAAO,CAAC6J,EAAGA,EAAG/T,EAAGA,EAAGkK,EAAGA,EAAGrC,EAJhB,GAAKlM,GAAKe,EAAI9E,EAAI2C,GAASmC,EAAIiX,GAAK,GAAI,IAIlBK,EAAGA,EAAG3R,EAAGA,EAAG6P,EAAGA,EAChD,CASA,IAAe4B,GAAA,IAAI9P,GAAW,CAC7B7W,GAAI,YACJ0X,MAAO,cACPhX,KAAM,YACN8U,OAAQ,CACP8S,EAAG,CACFrS,SAAU,CAAC,EAAG,KACdvV,KAAM,KAEPsJ,EAAG,CACFiM,SAAU,CAAC,EAAG,KACdvV,KAAM,gBAEPqc,EAAG,CACF9G,SAAU,CAAC,EAAG,KACd9R,KAAM,QACNzD,KAAM,QAIR+N,KAAMuM,GAEN3C,QAAAA,CAAUwD,GACT,MAAM8K,EAAQqB,GAAQnM,EAAK4K,IAC3B,MAAO,CAACE,EAAMC,EAAGD,EAAMzR,EAAGyR,EAAM5J,EAChC,EACDzE,OAAQqO,GACAD,GACN,CAACE,EAAGD,EAAM,GAAIzR,EAAGyR,EAAM,GAAI5J,EAAG4J,EAAM,IACpCF,MChWH,MAAMlO,GAAQ7D,GAAOE,IACfmD,GAAI,IAAM,MACVsE,GAAI,MAAQ,GASlB,SAASkM,GAAWC,GAGnB,OAAQA,EAAQ,EAAM/vB,KAAKkkB,KAAK6L,EAAQ,IAAM,IAAK,GAAKA,EAAQnM,EACjE,CA0EA,SAASoM,GAAO5M,EAAKtM,GAGpB,MAAMgY,EApFE,MAJStM,EAwFCY,EAAI,IArFN9D,GAAKtf,KAAKgkB,KAAKxB,IAAMoB,GAAIpB,EAAI,IAAM,KAC7B,GAJvB,IAAkBA,EAyFjB,GAAU,IAANsM,EACH,MAAO,CAAC,EAAK,EAAK,GAEnB,MAAMZ,EAAQqB,GAAQnM,EAAK4K,IAC3B,MAAO,CAAC7J,GAAU+J,EAAM5J,GAAI4J,EAAM9T,EAAG0U,EACtC,CAGO,MAAMd,GAAoBrB,GAChC7M,GAAO,IAAM9f,KAAK4T,GAAKkc,GAAU,IACf,IAAlBA,GAAU,IACV,WACA,GAYD,IAAeG,GAAA,IAAI7R,GAAW,CAC7B7W,GAAI,MACJU,KAAM,MACN8U,OAAQ,CACPuH,EAAG,CACF9G,SAAU,CAAC,EAAG,KACd9R,KAAM,QACNzD,KAAM,OAEP+J,EAAG,CACFwL,SAAU,CAAC,EAAG,KACdvV,KAAM,gBAEP6mB,EAAG,CACFtR,SAAU,CAAC,EAAG,KACdvV,KAAM,SAIR+N,KAAMuM,GAEN3C,SAAUwD,GACF4M,GAAM5M,GAEdvD,OAAQoQ,GA5HT,SAAkBlT,EAAQjG,GASzB,IAAKwN,EAAGtS,EAAG8c,GAAK/R,EACZqG,EAAM,GACNyM,EAAI,EAGR,GAAU,IAANf,EACH,MAAO,CAAC,EAAK,EAAK,GAInB,IAAItM,EAAIsN,GAAUhB,GAKjBe,EADGf,EAAI,EACH,mBAAsBA,GAAK,EAAI,iBAAoBA,EAAI,kBAGvD,qBAAwBA,GAAK,EAAI,mBAAsBA,EAAI,mBAWhE,IAAIoB,EAAU,EACVnb,EAAOob,IAIX,KAAOD,GAPc,IAOW,CAC/B9M,EAAM6K,GAAU,CAACE,EAAG0B,EAAGzV,EAAGpI,EAAGsS,EAAGA,GAAIxN,GAIpC,MAAMsZ,EAAQpwB,KAAKgT,IAAIoQ,EAAI,GAAKZ,GAChC,GAAI4N,EAAQrb,EAAM,CACjB,GAAIqb,GAfY,MAgBf,OAAOhN,EAGRrO,EAAOqb,CACR,CAOAP,IAASzM,EAAI,GAAKZ,GAAKqN,GAAK,EAAIzM,EAAI,IAEpC8M,GAAW,CACZ,CAIA,OAAOjC,GAAU,CAACE,EAAG0B,EAAGzV,EAAGpI,EAAGsS,EAAGA,GAAIxN,EACtC,CAuDSuZ,CAAQJ,EAAKjC,IAErB9O,QAAS,CACRrB,MAAO,CACNtW,GAAI,QACJwV,OAAQ,CAAC,qBAAsB,0BAA2B,+BCpJ7D,MAAMyP,GAAUxsB,KAAK4T,GAAK,IACpB0c,GAAW,CAAC,EAAM,KAAO,OAO/B,SAASC,GAAcxT,GAMlBA,EAAO,GAAK,IACfA,EAASkT,GAAIrQ,SAASqQ,GAAIpQ,OAAO9C,KAMlC,MAAMN,EAAIzc,KAAKwwB,IAAIxwB,KAAK0N,IAAI,EAAI4iB,GAAS,GAAKvT,EAAO,GAAKiR,GAAkBR,OAAQ,IAAQ8C,GAAS,GAC/FG,EAAO1T,EAAO,GAAKyP,GACnB5jB,EAAI6T,EAAIzc,KAAKglB,IAAIyL,GACjBjV,EAAIiB,EAAIzc,KAAKilB,IAAIwL,GAEvB,MAAO,CAAC1T,EAAO,GAAInU,EAAG4S,EACvB,CCdA,IAAekV,GAAA,CACdC,SChBc,SAAmB9S,EAAO4H,GAExC,OAAOmD,GAAS/K,EAAO4H,EAAQ,MAChC,EDcCmL,URLc,SAAU/S,EAAO4H,GAA6B,IAArB3B,EAACA,EAAI,EAAC9R,EAAEA,EAAI,GAAExQ,UAAAuC,OAAAvC,QAAAiC,IAAAjC,UAAAiC,GAAAjC,UAAG,GAAA,IACvDqc,EAAO4H,GAAUpG,GAAS,CAACxB,EAAO4H,IAUnC,IAAKI,EAAIC,EAAIC,GAAMlC,GAAIvO,KAAKuI,KACrBmI,EAAI6K,GAAMxM,GAAI/O,KAAKuO,GAAK,CAACgC,EAAIC,EAAIC,KACnCE,EAAIC,EAAIC,GAAMtC,GAAIvO,KAAKmQ,GACxBW,EAAK/B,GAAI/O,KAAKuO,GAAK,CAACoC,EAAIC,EAAIC,IAAK,GAYjCH,EAAK,IACRA,EAAK,GAEFI,EAAK,IACRA,EAAK,GAON,IAAIU,EAAKjB,EAAKI,EACVc,EAAKf,EAAKI,EAOV0K,GALKhL,EAAKI,IAKE,GAJPH,EAAKI,IAIc,EAAMY,GAAM,EAmBpCU,EAAK,KACL5B,GAAM,KACT4B,EAAM,QAAW5B,GAAO,EAAI,OAAUA,IAIvC,IAGI8B,EAHAD,EAAO,MAAS1B,GAAO,EAAI,MAASA,GAAO,KAI3CtT,OAAOC,MAAMke,KAChBA,EAAK,GAILlJ,EADGkJ,GAAM,KAAOA,GAAM,IAClB,IAAO7wB,KAAKgT,IAAI,GAAMhT,KAAKglB,KAAK6L,EAAK,KAAOxL,KAG5C,IAAOrlB,KAAKgT,IAAI,GAAMhT,KAAKglB,KAAK6L,EAAK,IAAMxL,KAKhD,IAAI0L,EAAK/wB,KAAKkkB,IAAI8B,EAAI,GAClBgL,EAAIhxB,KAAK0kB,KAAKqM,GAAMA,EAAK,OAIzBhJ,GAAMjB,GAAMhD,EAAI2D,KAAQ,EAI5B,OAHAM,IAAOhB,GAAM/U,EAAI0V,KAAQ,EACzBK,GAAO+I,GALEpJ,GAAOsJ,EAAIrJ,EAAK,EAAIqJ,KAKV,EAEZhxB,KAAK0kB,KAAKqD,EAElB,EQ5FCvC,cACAyL,SEZc,SAAUpT,EAAO4H,IAC9B5H,EAAO4H,GAAUpG,GAAS,CAACxB,EAAO4H,IAKnC,IAAKyL,EAAKC,EAAKC,GAAOzG,GAAOrV,KAAKuI,IAC7BwT,EAAKC,EAAKC,GAAO5G,GAAOrV,KAAKmQ,GAI9B+L,EAAKN,EAAMG,EACXtK,EAAKoK,EAAMG,EAGV5e,OAAOC,MAAMye,IAAU1e,OAAOC,MAAM4e,IAExCH,EAAM,EACNG,EAAM,GAEE7e,OAAOC,MAAMye,GAErBA,EAAMG,EAEE7e,OAAOC,MAAM4e,KACrBA,EAAMH,GAGP,IAAIvK,EAAKuK,EAAMG,EACXnK,EAAK,EAAIpnB,KAAK0kB,KAAKyM,EAAMG,GAAOtxB,KAAKilB,IAAK4B,EAAK,GAAM7mB,KAAK4T,GAAK,MAEnE,OAAO5T,KAAK0kB,KAAK8M,GAAM,EAAIzK,GAAM,EAAIK,GAAM,EAC5C,EFnBCqK,UGhBc,SAAU5T,EAAO4H,IAC9B5H,EAAO4H,GAAUpG,GAAS,CAACxB,EAAO4H,IAOnC,IAAMiM,EAAIC,EAAIC,GAAOvG,GAAM/V,KAAKuI,IAC1BgU,EAAIC,EAAIC,GAAO1G,GAAM/V,KAAKmQ,GAMhC,OAAO,IAAMzlB,KAAK0kB,MAAMgN,EAAKG,IAAO,EAAK,KAAQF,EAAKG,IAAO,GAAMF,EAAKG,IAAO,EAChF,EHCCvJ,YACAwJ,UDgBc,SAAUnU,EAAO4H,IAC9B5H,EAAO4H,GAAUpG,GAAS,CAACxB,EAAO4H,IAEnC,IAAMwM,EAAInM,EAAIC,GAAOwK,GAAaN,GAAI3a,KAAKuI,KACrCqU,EAAIhM,EAAIC,GAAOoK,GAAaN,GAAI3a,KAAKmQ,IAI3C,OAAOzlB,KAAK0kB,MAAMuN,EAAKC,IAAO,GAAKpM,EAAKI,IAAO,GAAKH,EAAKI,IAAO,EACjE,GKtBA,MAAMgM,GAAa,CAClBlC,IAAO,CACNnsB,OAAQ,QACRsuB,IAAK,EACLC,aAAc,MACdC,gBAAiB,CAAC,GAEnB,YAAa,CACZxuB,OAAQ,QACRsuB,IAAK,EACLC,aAAc,MACdC,gBAAiB,CAAEC,QAAS,QAAS5kB,IAAK,EAAGD,IAAK,OAwBrC,SAAS8kB,GACvB3U,GAQC,IAqBG4U,GA5BJ3uB,OACCA,EAASkT,GAASC,cAAa4F,MAC/BA,EAAiBwV,aACjBA,EAAe,GAAED,IACjBA,EAAM,EAACE,gBACPA,EAAkB,CAAC,GACnB9wB,UAAAuC,OAAAvC,QAAAiC,IAAAjC,UAAAiC,GAAAjC,UAAG,GAAA,GAkBJ,GAhBAqc,EAAQwB,GAASxB,GAEbJ,GAAcjc,UAAU,IAC3Bqb,EAAQrb,UAAU,GAETqb,IACTA,EAAQgB,EAAMhB,OAGfA,EAAQuB,GAAWtd,IAAI+b,GAOnBsD,GAAQtC,EAAOhB,EAAO,CAAE0D,QAAS,IACpC,OAAO1C,EAIR,GAAe,QAAX/Z,EACH2uB,EAmIK,SAAqBC,GAAsB,IAAd7V,MAACA,GAAMrb,UAAAuC,OAAAvC,QAAAiC,IAAAjC,UAAAiC,GAAAjC,UAAG,GAAA,GAC7C,MAAMmxB,EAAM,IACNrT,EAAI,KAEVoT,EAASrT,GAASqT,GAEb7V,IACJA,EAAQ6V,EAAO7V,OAGhBA,EAAQuB,GAAWtd,IAAI+b,GACvB,MAAM+V,EAAaxU,GAAWtd,IAAI,SAElC,GAAI+b,EAAMqD,YACT,OAAO3K,GAAGmd,EAAQ7V,GAGnB,MAAMgW,EAAetd,GAAGmd,EAAQE,GAChC,IAAIpO,EAAIqO,EAAa9V,OAAO,GAG5B,GAAIyH,GAAK,EAAG,CACX,MAAM1E,EAAQvK,GAAGud,GAAOC,MAAOlW,GAE/B,OADAiD,EAAMnL,MAAQ+d,EAAO/d,MACdY,GAAGuK,EAAOjD,EAClB,CACA,GAAI2H,GAAK,EAAG,CACX,MAAMwO,EAAQzd,GAAGud,GAAOG,MAAOpW,GAE/B,OADAmW,EAAMre,MAAQ+d,EAAO/d,MACdY,GAAGyd,EAAOnW,EAClB,CAEA,GAAIsD,GAAQ0S,EAAchW,EAAO,CAAC0D,QAAS,IAC1C,OAAOhL,GAAGsd,EAAchW,GAGzB,SAASqW,EAAMC,GACd,MAAMC,EAAY7d,GAAG4d,EAAQtW,GACvBwW,EAAczyB,OAAO6f,OAAO5D,EAAME,QAQxC,OAPAqW,EAAUrW,OAASqW,EAAUrW,OAAOvL,KAAI,CAACmP,EAAO9S,KAC/C,GAAI,UAAWwlB,EAAYxlB,GAAQ,CAClC,MAAOF,EAAKD,GAAQ2lB,EAAYxlB,GAAO8H,MACvC,OAAO8H,GAAW9P,EAAKgT,EAAOjT,EAC/B,CACA,OAAOiT,CAAK,IAENyS,CACR,CACA,IAAIzlB,EAAM,EACND,EAAMmlB,EAAa9V,OAAO,GAC1BuW,GAAc,EACdnpB,EAAUwe,GAAMkK,GAChBU,EAAUL,EAAK/oB,GAEfqpB,EAAIhL,GAAS+K,EAASppB,GAC1B,GAAIqpB,EAAIb,EACP,OAAOY,EAGR,KAAQ7lB,EAAMC,EAAO2R,GAAG,CACvB,MAAMmU,GAAU9lB,EAAMD,GAAO,EAE7B,GADAvD,EAAQ4S,OAAO,GAAK0W,EAChBH,GAAenT,GAAQhW,EAAS0S,EAAO,CAAC0D,QAAS,IACpD5S,EAAM8lB,OAKN,GAFAF,EAAUL,EAAK/oB,GACfqpB,EAAIhL,GAAS+K,EAASppB,GAClBqpB,EAAIb,EAAK,CACZ,GAAKA,EAAMa,EAAIlU,EACd,MAGAgU,GAAc,EACd3lB,EAAM8lB,CAER,MAEC/lB,EAAM+lB,CAGT,CACA,OAAOF,CACR,CAtNeG,CAAW7V,EAAO,CAAEhB,cAE7B,CACJ,GAAe,SAAX/Y,GAAsBqc,GAAQtC,EAAOhB,GA2ExC4V,EAAald,GAAGsI,EAAOhB,OA3EyB,CAE5Cjc,OAAOS,UAAUH,eAAeE,KAAK+wB,GAAYruB,MAClDA,SAAQsuB,MAAKC,eAAcC,mBAAmBH,GAAWruB,IAI5D,IAAI6vB,EAAKnO,GACT,GAAqB,KAAjB6M,EACH,IAAK,IAAI9gB,KAAKmf,GACb,GAAI,SAAW2B,EAAajiB,gBAAkBmB,EAAEnB,cAAe,CAC9DujB,EAAKjD,GAAcnf,GACnB,KACD,CAIF,IAAIgiB,EAAUf,GAAQjd,GAAGsI,EAAOhB,GAAQ,CAAE/Y,OAAQ,OAAQ+Y,UAC1D,GAAI8W,EAAG9V,EAAO0V,GAAWnB,EAAK,CAG7B,GAA4C,IAAxCxxB,OAAO6J,KAAK6nB,GAAiBvuB,OAAc,CAC9C,IAAI6vB,EAAcxV,GAAWsD,aAAa4Q,EAAgBC,SACtDA,EAAUzxB,GAAIyU,GAAGsI,EAAO+V,EAAY/W,OAAQ+W,EAAYrsB,IAI5D,GAHIkW,GAAY8U,KACfA,EAAU,GAEPA,GAAWD,EAAgB5kB,IAC9B,OAAO6H,GAAG,CAAEsH,MAAO,UAAWE,OAAQd,GAAY,KAAK4B,EAAMhB,OAEzD,GAAI0V,GAAWD,EAAgB3kB,IACnC,OAAO4H,GAAG,CAAEsH,MAAO,UAAWE,OAAQ,CAAC,EAAG,EAAG,IAAMc,EAAMhB,MAE3D,CAGA,IAAIK,EAAYkB,GAAWsD,aAAa5d,GACpC+vB,EAAW3W,EAAUL,MACrBiF,EAAU5E,EAAU3V,GAEpBusB,EAAcve,GAAGsI,EAAOgW,GAE5BC,EAAY/W,OAAOnG,SAAQ,CAAC5E,EAAGnD,KAC1B4O,GAAYzL,KACf8hB,EAAY/W,OAAOlO,GAAK,EACzB,IAED,IACIlB,GADSuP,EAAUvH,OAASuH,EAAUM,UACzB,GACb8B,EA/HR,SAAsB8S,GAGrB,MAAM2B,EAAU3B,EAAWpyB,KAAKoN,MAAMpN,KAAK+S,MAAM/S,KAAKgT,IAAIof,KAAnC,EAEvB,OAAOpyB,KAAK0N,IAAIsmB,WAAY,MAAID,EAAQ,IAAM,KAC/C,CAyHYE,CAAY7B,GAChB8B,EAAMvmB,EACNwmB,EAAOrzB,GAAIgzB,EAAahS,GAE5B,KAAOqS,EAAOD,EAAM5U,GAAG,CACtB,IAAIiU,EAAU5K,GAAMmL,GACpBP,EAAUf,GAAQe,EAAS,CAAE1W,QAAO/Y,OAAQ,SAC/B6vB,EAAGG,EAAaP,GAEhBnB,EAAM9S,EAClB4U,EAAMpzB,GAAIgzB,EAAahS,GAGvBqS,EAAOrzB,GAAIgzB,EAAahS,GAGzBlX,GAAIkpB,EAAahS,GAAUoS,EAAMC,GAAQ,EAC1C,CAEA1B,EAAald,GAAGue,EAAajX,EAC9B,MAEC4V,EAAac,CAEf,CAKA,GAAe,SAAXzvB,IAECqc,GAAQsS,EAAY5V,EAAO,CAAE0D,QAAS,IACzC,CACD,IAAI6T,EAASxzB,OAAO6f,OAAO5D,EAAME,QAAQvL,KAAIQ,GAAKA,EAAE2D,OAAS,KAE7D8c,EAAW1V,OAAS0V,EAAW1V,OAAOvL,KAAI,CAACQ,EAAGnD,KAC7C,IAAKlB,EAAKD,GAAO0mB,EAAOvlB,GAUxB,YARYpL,IAARkK,IACHqE,EAAIhS,KAAK0N,IAAIC,EAAKqE,SAGPvO,IAARiK,IACHsE,EAAIhS,KAAK2N,IAAIqE,EAAGtE,IAGVsE,CAAC,GAEV,CACD,CAOA,OALI6K,IAAUgB,EAAMhB,QACnB4V,EAAald,GAAGkd,EAAY5U,EAAMhB,QAGnCgB,EAAMd,OAAS0V,EAAW1V,OACnBc,CACR,CAEA2U,GAAQ/O,QAAU,QAKlB,MAAMqP,GAAS,CACdC,MAAO,CAAElW,MAAO0L,GAAOxL,OAAQ,CAAC,EAAG,EAAG,IACtCkW,MAAO,CAAEpW,MAAO0L,GAAOxL,OAAQ,CAAC,EAAG,EAAG,KC1MxB,SAASxH,GAAIsI,EAAOhB,GAAuB,IAAhBsD,QAACA,GAAQ3e,UAAAuC,OAAAvC,QAAAiC,IAAAjC,UAAAiC,GAAAjC,UAAG,GAAA,GACrDqc,EAAQwB,GAASxB,GAGjB,IAAId,GAFJF,EAAQuB,GAAWtd,IAAI+b,IAEJvH,KAAKuI,GACpB9L,EAAM,CAAC8K,QAAOE,SAAQpI,MAAOkJ,EAAMlJ,OAMvC,OAJIwL,IACHpO,EAAMygB,GAAQzgB,GAAiB,IAAZoO,OAAmB1c,EAAY0c,IAG5CpO,CACR,CAEAwD,GAAGkO,QAAU,8DCxBb,IAAI9d,EAAcjF,KAEd+B,EAAaC,iBAEjB2xB,GAAiB,SAAU5qB,EAAG1D,GAC5B,WAAY0D,EAAE1D,GAAI,MAAM,IAAItD,EAAW,0BAA4BkD,EAAYI,GAAK,OAASJ,EAAY8D,KCQ5F,SAAS6qB,GAAWzW,GAK3B,IAAAxL,EAAAkiB,EAAA,IACHxiB,GANqCO,UACzCA,EAAY0E,GAAS1E,UAASwK,OAC9BA,EAAS,UACTqD,QAAAA,GAAU,KACPqU,GACHhzB,UAAAuC,OAAAvC,QAAAiC,IAAAjC,UAAAiC,GAAAjC,UAAG,GAAA,GAKCmd,EAAW7B,EACfA,EAC0C,QADpCzK,UAAAkiB,GAHN1W,EAAQwB,GAASxB,IAGFhB,MAAMyB,UAAUxB,UAAO,IAAAyX,EAAAA,EAC5B1W,EAAMhB,MAAMyB,UAAU,kBAAUjM,IAAAA,EAAAA,EAChC+L,GAAWqW,eAMrB,IAAI1X,EAASc,EAAMd,OAAOza,QAS1B,GAPA6d,IAAAA,EAAYrD,EAAO0V,SAEfrS,IAAYuU,GAAa7W,KAE5Bd,EAASyV,GAAQ7J,GAAM9K,IAAoB,IAAZsC,OAAmB1c,EAAY0c,GAASpD,QAGpD,WAAhBD,EAAOpR,KAAmB,CAG7B,GAFA8oB,EAAcliB,UAAYA,GAEtBwK,EAAOwX,UAIV,MAAM,IAAI5xB,UAAW,UAASic,6DAH9B5M,EAAM+K,EAAOwX,UAAUvX,EAAQc,EAAMlJ,MAAO6f,EAK9C,KACK,CAEJ,IAAIvsB,EAAO6U,EAAO7U,MAAQ,QAEtB6U,EAAOwF,gBACVvF,EAASD,EAAOwF,gBAAgBvF,EAAQzK,GAGtB,OAAdA,IACHyK,EAASA,EAAOvL,KAAIQ,GACZyL,GAAqBzL,EAAG,CAACM,iBAKnC,IAAI8B,EAAO,IAAI2I,GAEf,GAAa,UAAT9U,EAAkB,CAAA,IAAA0sB,EAErB,IAAI1V,EAAQnC,EAAOvV,aAAEotB,EAAI7X,EAAOoB,WAAG,IAAAyW,OAAA,EAAVA,EAAa,KAAM9W,EAAMhB,MAAMtV,GACxD6M,EAAKwgB,QAAQ3V,EACd,CAEA,IAAItK,EAAQkJ,EAAMlJ,MACA,OAAdrC,IACHqC,EAAQ8I,GAAqB9I,EAAO,CAACrC,eAGtC,IAAIuiB,EAAWhX,EAAMlJ,OAAS,GAAKmI,EAAOgY,QAAU,GAAM,GAAEhY,EAAOiY,OAAS,IAAM,QAAQpgB,IAC1F5C,EAAO,GAAE9J,KAAQmM,EAAKjI,KAAK2Q,EAAOiY,OAAS,KAAO,OAAOF,IAC1D,CAEA,OAAO9iB,CACR,kCCpFA,IAAImB,EAAIxS,KACJ0G,EAAWvE,KACXoL,EAAoBlL,KACpBoQ,EAAiB3N,KACjB6uB,EAAwB1sB,KACxBwJ,EAA2BvJ,KAmB/BsL,EAAE,CAAEzD,OAAQ,QAAS2D,OAAO,EAAMzG,MAAO,EAAGmE,OAhBH,IAAlB,GAAG8jB,QAAQ,KAGG,WACnC,IAEEh0B,OAAOC,eAAe,GAAI,SAAU,CAAEiB,UAAU,IAAS8yB,SAC1D,CAAC,MAAOn0B,GACP,OAAOA,aAAiBiC,SACzB,CACH,CAEkC2Q,IAI4B,CAE5DuhB,QAAS,SAAiBthB,GACxB,IAAI7J,EAAIrC,EAAS/G,MACb2N,EAAMC,EAAkBxE,GACxB8J,EAAW/R,UAAUuC,OACzB,GAAIwP,EAAU,CACZpC,EAAyBnD,EAAMuF,GAE/B,IADA,IAAIyhB,EAAIhnB,EACDgnB,KAAK,CACV,IAAIzf,EAAKyf,EAAIzhB,EACTyhB,KAAKvrB,EAAGA,EAAE8L,GAAM9L,EAAEurB,GACjBX,EAAsB5qB,EAAG8L,EAC/B,CACD,IAAK,IAAIsa,EAAI,EAAGA,EAAItc,EAAUsc,IAC5BpmB,EAAEomB,GAAKruB,UAAUquB,EAEpB,CAAC,OAAO1c,EAAe1J,EAAGuE,EAAMuF,EAClC,OCxBH,IAAe0hB,GAAA,IAAIvS,GAAc,CAChCnb,GAAI,iBACJ0X,MAAO,mBACPhX,KAAM,kBACN6X,MAAO,cAjBQ,CACf,CAAE,kBAAoB,mBAAsB,mBAC5C,CAAE,kBAAoB,kBAAsB,oBAC5C,CAAE,EAAoB,oBAAsB,oBAgB5CoD,UAZiB,CACjB,CAAG,mBAAqB,kBAAoB,iBAC5C,EAAG,iBAAqB,kBAAoB,mBAC5C,CAAG,kBAAqB,iBAAoB,qBCZ7C,MAAMgS,GAAI,iBACJC,GAAI,iBAEV,IAAeC,GAAA,IAAI1S,GAAc,CAChCnb,GAAI,UACJU,KAAM,WACN+N,KAAMif,GAENpV,OAAQwV,GACAA,EAAI7jB,KAAI,SAAUpL,GACxB,OAAIA,EAAU,IAAJ+uB,GACF/uB,EAAM,IAGPpG,KAAKkkB,KAAK9d,EAAM8uB,GAAI,GAAKA,GAAG,EAAI,IACxC,IAEDtV,SAAUyV,GACFA,EAAI7jB,KAAI,SAAUpL,GACxB,OAAIA,GAAO+uB,GACHD,GAAIl1B,KAAKkkB,IAAI9d,EAAK,MAAS8uB,GAAI,GAGhC,IAAM9uB,CACd,MCdF,IAAekvB,GAAA,IAAI5S,GAAc,CAChCnb,GAAI,YACJ0X,MAAO,sBACPhX,KAAM,YACN6X,MAAO,cAhBQ,CACf,CAAC,kBAAoB,mBAAqB,mBAC1C,CAAC,kBAAoB,kBAAqB,kBAC1C,CAAC,EAAoB,mBAAqB,oBAe1CoD,UAZiB,CACjB,CAAE,mBAAsB,mBAAqB,oBAC7C,EAAE,kBAAsB,mBAAqB,qBAC7C,CAAE,oBAAsB,mBAAqB,sBCF9C,MAQaA,GAAY,CACxB,CAAG,oBAAsB,mBAAsB,mBAC/C,EAAG,kBAAsB,mBAAsB,oBAC/C,CAAG,oBAAsB,mBAAsB,qBAGhD,IAAeqS,GAAA,IAAI7S,GAAc,CAChCnb,GAAI,cACJU,KAAM,cACN6X,MAAO,cAjBQ,CACf,CAAE,mBAAqB,iBAAqB,mBAC5C,CAAE,mBAAqB,iBAAqB,oBAC5C,CAAE,mBAAqB,mBAAqB,oBAgB5CoD,UAAAA,KCpBcsS,GAAA,CACdC,UAAa,CAAC,IAAM,IAAK,IAAM,IAAK,GACpCC,aAAgB,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KAC7CC,KAAQ,CAAC,EAAG,EAAG,GACfC,WAAc,CAAC,IAAM,IAAK,EAAG,IAAM,KACnCC,MAAS,CAAC,IAAM,IAAK,EAAG,GACxBC,MAAS,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KACtCC,OAAU,CAAC,EAAG,IAAM,IAAK,IAAM,KAC/B/C,MAAS,CAAC,EAAG,EAAG,GAChBgD,eAAkB,CAAC,EAAG,IAAM,IAAK,IAAM,KACvCC,KAAQ,CAAC,EAAG,EAAG,GACfC,WAAc,CAAC,IAAM,IAAK,GAAK,IAAK,IAAM,KAC1CC,MAAS,CAAC,IAAM,IAAK,GAAK,IAAK,GAAK,KACpCC,UAAa,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KAC1CC,UAAa,CAAC,GAAK,IAAK,IAAM,IAAK,IAAM,KACzCC,WAAc,CAAC,IAAM,IAAK,EAAG,GAC7BC,UAAa,CAAC,IAAM,IAAK,IAAM,IAAK,GAAK,KACzCC,MAAS,CAAC,EAAG,IAAM,IAAK,GAAK,KAC7BC,eAAkB,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KAC/CC,SAAY,CAAC,EAAG,IAAM,IAAK,IAAM,KACjCC,QAAW,CAAC,IAAM,IAAK,GAAK,IAAK,GAAK,KACtCC,KAAQ,CAAC,EAAG,EAAG,GACfC,SAAY,CAAC,EAAG,EAAG,IAAM,KACzBC,SAAY,CAAC,EAAG,IAAM,IAAK,IAAM,KACjCC,cAAiB,CAAC,IAAM,IAAK,IAAM,IAAK,GAAK,KAC7CC,SAAY,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KACzCC,UAAa,CAAC,EAAG,IAAM,IAAK,GAC5BC,SAAY,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KACzCC,UAAa,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KAC1CC,YAAe,CAAC,IAAM,IAAK,EAAG,IAAM,KACpCC,eAAkB,CAAC,GAAK,IAAK,IAAM,IAAK,GAAK,KAC7CC,WAAc,CAAC,EAAG,IAAM,IAAK,GAC7BC,WAAc,CAAC,GAAW,GAAK,IAAK,IACpCC,QAAW,CAAC,IAAM,IAAK,EAAG,GAC1BC,WAAc,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KAC3CC,aAAgB,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KAC7CC,cAAiB,CAAC,GAAK,IAAK,GAAK,IAAK,IAAM,KAC5CC,cAAiB,CAAC,GAAK,IAAK,GAAK,IAAK,GAAK,KAC3CC,cAAiB,CAAC,GAAK,IAAK,GAAK,IAAK,GAAK,KAC3CC,cAAiB,CAAC,EAAG,IAAM,IAAK,IAAM,KACtCC,WAAc,CAAC,IAAM,IAAK,EAAG,IAAM,KACnCC,SAAY,CAAC,EAAG,GAAK,IAAK,IAAM,KAChCC,YAAe,CAAC,EAAG,IAAM,IAAK,GAC9BC,QAAW,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KACxCC,QAAW,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KACxCC,WAAc,CAAC,GAAK,IAAK,IAAM,IAAK,GACpCC,UAAa,CAAC,IAAM,IAAK,GAAK,IAAK,GAAK,KACxCC,YAAe,CAAC,EAAG,IAAM,IAAK,IAAM,KACpCC,YAAe,CAAC,GAAK,IAAK,IAAM,IAAK,GAAK,KAC1CC,QAAW,CAAC,EAAG,EAAG,GAClBC,UAAa,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KAC1CC,WAAc,CAAC,IAAM,IAAK,IAAM,IAAK,GACrCC,KAAQ,CAAC,EAAG,IAAM,IAAK,GACvBC,UAAa,CAAC,IAAM,IAAK,IAAM,IAAK,GAAK,KACzCC,KAAQ,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KACrCC,MAAS,CAAC,EAAG,IAAM,IAAK,GACxBC,YAAe,CAAC,IAAM,IAAK,EAAG,GAAK,KACnCC,KAAQ,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KACrCC,SAAY,CAAC,IAAM,IAAK,EAAG,IAAM,KACjCC,QAAW,CAAC,EAAG,IAAM,IAAK,IAAM,KAChCC,UAAa,CAAC,IAAM,IAAK,GAAK,IAAK,GAAK,KACxCC,OAAU,CAAC,GAAK,IAAK,EAAG,IAAM,KAC9BC,MAAS,CAAC,EAAG,EAAG,IAAM,KACtBC,MAAS,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KACtCC,SAAY,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KACzCC,cAAiB,CAAC,EAAG,IAAM,IAAK,IAAM,KACtCC,UAAa,CAAC,IAAM,IAAK,IAAM,IAAK,GACpCC,aAAgB,CAAC,EAAG,IAAM,IAAK,IAAM,KACrCC,UAAa,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KAC1CC,WAAc,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KAC3CC,UAAa,CAAC,IAAM,IAAK,EAAG,GAC5BC,qBAAwB,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KACrDC,UAAa,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KAC1CC,WAAc,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KAC3CC,UAAa,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KAC1CC,UAAa,CAAC,EAAG,IAAM,IAAK,IAAM,KAClCC,YAAe,CAAC,EAAG,IAAM,IAAK,IAAM,KACpCC,cAAiB,CAAC,GAAK,IAAK,IAAM,IAAK,IAAM,KAC7CC,aAAgB,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KAC7CC,eAAkB,CAAC,IAAM,IAAK,IAAM,IAAK,IACzCC,eAAkB,CAAC,IAAM,IAAK,IAAM,IAAK,IACzCC,eAAkB,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KAC/CC,YAAe,CAAC,EAAG,EAAG,IAAM,KAC5BC,KAAQ,CAAC,EAAG,EAAG,GACfC,UAAa,CAAC,GAAK,IAAK,IAAM,IAAK,GAAK,KACxCC,MAAS,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KACtCC,QAAW,CAAC,EAAG,EAAG,GAClBC,OAAU,CAAC,IAAM,IAAK,EAAG,GACzBC,iBAAoB,CAAC,GAAW,IAAM,IAAK,IAAM,KACjDC,WAAc,CAAC,EAAG,EAAG,IAAM,KAC3BC,aAAgB,CAAC,IAAM,IAAK,GAAK,IAAK,IAAM,KAC5CC,aAAgB,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KAC7CC,eAAkB,CAAC,GAAK,IAAK,IAAM,IAAK,IAAM,KAC9CC,gBAAmB,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KAChDC,kBAAqB,CAAC,EAAG,IAAM,IAAK,IAAM,KAC1CC,gBAAmB,CAAC,GAAK,IAAK,IAAM,IAAK,IACzCC,gBAAmB,CAAC,IAAM,IAAK,GAAK,IAAK,IAAM,KAC/CC,aAAgB,CAAC,GAAK,IAAK,GAAK,IAAK,IAAM,KAC3CC,UAAa,CAAC,IAAM,IAAK,EAAG,IAAM,KAClCC,UAAa,CAAC,EAAG,IAAM,IAAK,IAAM,KAClCC,SAAY,CAAC,EAAG,IAAM,IAAK,IAAM,KACjCC,YAAe,CAAC,EAAG,IAAM,IAAK,IAAM,KACpCC,KAAQ,CAAC,EAAG,EAAG,IAAM,KACrBC,QAAW,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KACxCC,MAAS,CAAC,IAAM,IAAK,IAAM,IAAK,GAChCC,UAAa,CAAC,IAAM,IAAK,IAAM,IAAK,GAAK,KACzCC,OAAU,CAAC,EAAG,IAAM,IAAK,GACzBC,UAAa,CAAC,EAAG,GAAK,IAAK,GAC3BC,OAAU,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KACvCC,cAAiB,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KAC9CC,UAAa,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KAC1CC,cAAiB,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KAC9CC,cAAiB,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KAC9CC,WAAc,CAAC,EAAG,IAAM,IAAK,IAAM,KACnCC,UAAa,CAAC,EAAG,IAAM,IAAK,IAAM,KAClCC,KAAQ,CAAC,IAAM,IAAK,IAAM,IAAK,GAAK,KACpCC,KAAQ,CAAC,EAAG,IAAM,IAAK,IAAM,KAC7BC,KAAQ,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KACrCC,WAAc,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KAC3CC,OAAU,CAAC,IAAM,IAAK,EAAG,IAAM,KAC/BC,cAAiB,CAAC,GAAW,GAAU,IACvCC,IAAO,CAAC,EAAG,EAAG,GACdC,UAAa,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KAC1CC,UAAa,CAAC,GAAK,IAAK,IAAM,IAAK,IAAM,KACzCC,YAAe,CAAC,IAAM,IAAK,GAAK,IAAK,GAAK,KAC1CC,OAAU,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KACvCC,WAAc,CAAC,IAAM,IAAK,IAAM,IAAK,GAAK,KAC1CC,SAAY,CAAC,GAAK,IAAK,IAAM,IAAK,GAAK,KACvCC,SAAY,CAAC,EAAG,IAAM,IAAK,IAAM,KACjCC,OAAU,CAAC,IAAM,IAAK,GAAK,IAAK,GAAK,KACrCC,OAAU,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KACvCC,QAAW,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KACxCC,UAAa,CAAC,IAAM,IAAK,GAAK,IAAK,IAAM,KACzCC,UAAa,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KAC1CC,UAAa,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KAC1CC,KAAQ,CAAC,EAAG,IAAM,IAAK,IAAM,KAC7BC,YAAe,CAAC,EAAG,EAAG,IAAM,KAC5BC,UAAa,CAAC,GAAK,IAAK,IAAM,IAAK,IAAM,KACzCC,IAAO,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KACpCC,KAAQ,CAAC,EAAG,IAAM,IAAK,IAAM,KAC7BC,QAAW,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KACxCC,OAAU,CAAC,EAAG,GAAK,IAAK,GAAK,KAC7BC,UAAa,CAAC,GAAK,IAAK,IAAM,IAAK,IAAM,KACzCC,OAAU,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KACvCC,MAAS,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KACtCze,MAAS,CAAC,EAAG,EAAG,GAChB0e,WAAc,CAAC,IAAM,IAAK,IAAM,IAAK,IAAM,KAC3CC,OAAU,CAAC,EAAG,EAAG,GACjBC,YAAe,CAAC,IAAM,IAAK,IAAM,IAAK,GAAK,MCxJ5C,IAAIhpB,GAAe1E,MAAM,GAAG2tB,KAAK,mCAC7BC,GAAqB5tB,MAAM,GAAG2tB,KAAK,oBAEvC,IAAeE,GAAA,IAAInc,GAAc,CAChCnb,GAAI,OACJU,KAAM,OACN+N,KAAMuf,GACN3V,SAAUuD,GAIFA,EAAI3R,KAAIpL,IACd,IAAI0P,EAAO1P,EAAM,GAAK,EAAI,EACtB4M,EAAM5M,EAAM0P,EAEhB,OAAI9C,EAAM,SACF8C,GAAQ,MAAS9C,IAAQ,EAAI,KAAQ,MAGtC,MAAQ5M,CAAG,IAGpByZ,OAAQsD,GAIAA,EAAI3R,KAAIpL,IACd,IAAI0P,EAAO1P,EAAM,GAAK,EAAI,EACtB4M,EAAM5M,EAAM0P,EAEhB,OAAI9C,GAAO,OACH5M,EAAM,MAGP0P,IAAU9C,EAAM,MAAS,QAAU,GAAI,IAGhDkM,QAAS,CACRiE,IAAO,CACNpG,OAAQrH,IAETopB,WAAc,CACb72B,KAAM,MACN8sB,QAAQ,EACRhY,OAAQ6hB,GACR9J,SAAS,GAEVjX,MAAS,CAAsB,EAC/BkhB,KAAQ,CACPhiB,OAAQrH,GACRqf,QAAQ,EACR5V,WAAW,GAEZ6f,YAAe,CACd/2B,KAAM,OACN8sB,QAAQ,EACRhY,OAAQ6hB,IAETK,IAAO,CACNvzB,KAAM,SACN8mB,SAAS,EACTxxB,KAAMkR,GAAO,2BAA2BlR,KAAKkR,GAC7CwL,KAAAA,CAAOxL,GACFA,EAAInO,QAAU,IAEjBmO,EAAMA,EAAIhG,QAAQ,aAAc,SAGjC,IAAI6yB,EAAO,GAKX,OAJA7sB,EAAIhG,QAAQ,iBAAiBgzB,IAC5BH,EAAKj4B,KAAKq4B,SAASD,EAAW,IAAM,IAAI,IAGlC,CACNtgB,QAAS,OACT7B,OAAQgiB,EAAKz8B,MAAM,EAAG,GACtBqS,MAAOoqB,EAAKz8B,MAAM,GAAG,GAEtB,EACDgyB,UAAW,SAACvX,EAAQpI,GAET,IAFgByqB,SAC1BA,GAAW,GACX59B,UAAAuC,OAAAvC,QAAAiC,IAAAjC,UAAAiC,GAAAjC,UAAG,GAAA,GACCmT,EAAQ,GACXoI,EAAOjW,KAAK6N,GAGboI,EAASA,EAAOvL,KAAIQ,GAAKhS,KAAKq/B,MAAU,IAAJrtB,KAEpC,IAAIstB,EAAcF,GAAYriB,EAAO2D,OAAM1O,GAAKA,EAAI,IAAO,IAEvDitB,EAAMliB,EAAOvL,KAAIQ,GAChBstB,GACKttB,EAAI,IAAI5P,SAAS,IAGnB4P,EAAE5P,SAAS,IAAIm9B,SAAS,EAAG,OAChCpzB,KAAK,IAER,MAAO,IAAM8yB,CACd,GAEDO,QAAW,CACV9zB,KAAM,SACN1K,KAAMkR,GAAO,YAAYlR,KAAKkR,GAC9BwL,KAAAA,CAAOxL,GAEN,IAAIH,EAAM,CAAC6M,QAAS,OAAQ7B,OAAQ,KAAMpI,MAAO,GAUjD,GARY,iBAHZzC,EAAMA,EAAI9B,gBAIT2B,EAAIgL,OAASyY,GAASxC,MACtBjhB,EAAI4C,MAAQ,GAGZ5C,EAAIgL,OAASyY,GAAStjB,GAGnBH,EAAIgL,OACP,OAAOhL,CAET,MCvHY0tB,GAAA,IAAI/c,GAAc,CAChCnb,GAAI,KACJ0X,MAAO,aACPhX,KAAM,KACN+N,KAAMsf,GAEN1V,SAAUif,GAAKjf,SACfC,OAAQgf,GAAKhf,SCEd,IAAI6f,GAEJ,GAJA1oB,GAAS2oB,cAAgBd,GAIN,oBAARe,KAAuBA,IAAIC,SAErC,IAAK,IAAIhjB,IAAS,CAACoH,GAAKmR,GAASqK,IAAK,CACrC,IAAI1iB,EAASF,EAAMqE,eAEfhP,EAAMoiB,GADE,CAACzX,QAAOE,SAAQpI,MAAO,IAGnC,GAAIirB,IAAIC,SAAS,QAAS3tB,GAAM,CAC/B8E,GAAS2oB,cAAgB9iB,EACzB,KACD,CACD,CCnBM,SAASijB,GAAcjiB,GAE7B,OAAO/c,GAAI+c,EAAO,CAAC0E,GAAS,KAC7B,CAEO,SAASwd,GAAcliB,EAAOlc,GAEpCiJ,GAAIiT,EAAO,CAAC0E,GAAS,KAAM5gB,EAC5B,+DAEO,SAAmBq+B,GACzBp/B,OAAOC,eAAem/B,EAAM3+B,UAAW,YAAa,CACnDP,GAAAA,GACC,OAAOg/B,GAAaz/B,KACpB,EACDuK,GAAAA,CAAKjJ,GACJo+B,GAAa1/B,KAAMsB,EACpB,GAEF,oBClBA,MAMMs+B,GAAU,KACVC,GAAU,MAWhB,SAASC,GAAQC,GAChB,OAAIA,GAAKH,GACDG,EAEDA,GAAKH,GAAUG,IAAMF,EAC7B,CAEA,SAASG,GAAWj6B,GACnB,IAAI0P,EAAO1P,EAAM,GAAK,EAAI,EACtB4M,EAAMhT,KAAKgT,IAAI5M,GACnB,OAAO0P,EAAO9V,KAAKkkB,IAAIlR,EAAK,IAC7B,CChCA,MACM2Q,GAAK,GAAK,IACVC,GAAI,MAAQ,GAElB,IAAI9D,GAAQ7D,GAAOE,IAEnB,IAAemkB,GAAA,IAAIliB,GAAW,CAC7B7W,GAAI,UACJU,KAAM,UACN8U,OAAQ,CACP+G,EAAG,CACFtG,SAAU,CAAC,EAAG,KACdvV,KAAM,aAEPW,EAAG,CACF4U,SAAU,EAAE,IAAK,MAElBhC,EAAG,CACFgC,SAAU,EAAE,IAAK,aAMnBsC,GAEA9J,KAAMuM,GAGN3C,QAAAA,CAAUpD,GAET,IAGItT,EAHMsT,EAAIhL,KAAI,CAAC7P,EAAOkN,IAAMlN,EAAQme,GAAMjR,KAGlC2C,KAAI7P,GAASA,EAlCjB,oBAkC6B3B,KAAKgkB,KAAKriB,IAAUiiB,GAAIjiB,EAAQ,IAAM,MAE3E,MAAO,CACL,IAAMuH,EAAE,GAAM,GACf,KAAOA,EAAE,GAAKA,EAAE,IAChB,KAAOA,EAAE,GAAKA,EAAE,IAEjB,EAID2W,MAAAA,CAAQoE,GAEP,IAAI/a,EAAI,GAaR,OAZAA,EAAE,IAAM+a,EAAI,GAAK,IAAM,IACvB/a,EAAE,GAAK+a,EAAI,GAAK,IAAM/a,EAAE,GACxBA,EAAE,GAAKA,EAAE,GAAK+a,EAAI,GAAK,IAGb,CACT/a,EAAE,GAAOya,GAAK3jB,KAAKkkB,IAAIhb,EAAE,GAAI,IAAqB,IAAMA,EAAE,GAAK,IAAM0a,GACrEK,EAAI,GAAK,EAAKjkB,KAAKkkB,KAAKD,EAAI,GAAK,IAAM,IAAK,GAAKA,EAAI,GAAKL,GAC1D1a,EAAE,GAAOya,GAAK3jB,KAAKkkB,IAAIhb,EAAE,GAAI,IAAqB,IAAMA,EAAE,GAAK,IAAM0a,IAI3DpS,KAAI,CAAC7P,EAAOkN,IAAMlN,EAAQme,GAAMjR,IAC3C,EAEDqQ,QAAS,CACR,UAAW,CACVnC,OAAQ,CAAC,0BAA2B,gCAAiC,qCC5DxE,MAAMwjB,GAAyB,GAAnBvgC,KAAKkkB,IAAI,EAAG,IAAa,qDF8BtB,SAAuBsc,EAAYC,GAIjD,IAAIC,EACAtmB,EACAumB,EAGAC,EAAGta,EAAGhV,EARVmvB,EAAaphB,GAASohB,GACtBD,EAAanhB,GAASmhB,GAStBC,EAAalrB,GAAGkrB,EAAY,SAK3BG,EAAGta,EAAGhV,GAAKmvB,EAAW1jB,OACvB,IAAI8jB,EAAwB,SAAfR,GAAUO,GAAgC,SAAfP,GAAU/Z,GAAgC,QAAf+Z,GAAU/uB,GAE7EkvB,EAAajrB,GAAGirB,EAAY,SAC3BI,EAAGta,EAAGhV,GAAKkvB,EAAWzjB,OACvB,IAAI+jB,EAAuB,SAAfT,GAAUO,GAAgC,SAAfP,GAAU/Z,GAAgC,QAAf+Z,GAAU/uB,GAGxEyvB,EAAOZ,GAAOU,GACdG,EAAMb,GAAOW,GAGbG,EAAMD,EAAMD,EAgChB,OA3BI/gC,KAAKgT,IAAIguB,EAAMD,GAxDF,KAyDhB3mB,EAAI,EAGA6mB,GAEHP,EAAIM,GAvEQ,IAuEQD,GAtEP,IAuEb3mB,EA3Dc,KA2DVsmB,IAIJA,EAAIM,GAzEO,IAyEQD,GA1EP,IA2EZ3mB,EA9Dc,KA8DVsmB,GAILC,EADG3gC,KAAKgT,IAAIoH,GAxEC,GAyEN,EAECA,EAAI,EAGLA,EAxEW,KA2EXA,EA3EW,KA8EL,IAAPumB,CACR,mBEzFe,SAA2B9X,EAAQC,GACjDD,EAASxJ,GAASwJ,GAClBC,EAASzJ,GAASyJ,GAElB,IAAIoY,EAAQpgC,GAAI+nB,EAAQ,CAACyX,GAAS,MAC9Ba,EAAQrgC,GAAIgoB,EAAQ,CAACwX,GAAS,MAE9Bc,EAAephC,KAAKgT,IAAIhT,KAAKkkB,IAAIgd,EAAOX,IAAOvgC,KAAKkkB,IAAIid,EAAOZ,KAE/Dc,EAAWrhC,KAAKkkB,IAAIkd,EAAe,EAAIb,IAAQvgC,KAAKshC,MAAQ,GAEhE,OAAQD,EAAW,IAAO,EAAMA,CACjC,gBChBe,SAAwBxY,EAAQC,GAC9CD,EAASxJ,GAASwJ,GAClBC,EAASzJ,GAASyJ,GAElB,IAAIjD,EAAK/kB,GAAI+nB,EAAQ,CAAChF,GAAK,MACvBoC,EAAKnlB,GAAIgoB,EAAQ,CAACjF,GAAK,MAE3B,OAAO7jB,KAAKgT,IAAI6S,EAAKI,EACtB,oBCRe,SAA4B4C,EAAQC,GAClDD,EAASxJ,GAASwJ,GAClBC,EAASzJ,GAASyJ,GAElB,IAAIyY,EAAKvhC,KAAK0N,IAAIoyB,GAAajX,GAAS,GACpC2Y,EAAKxhC,KAAK0N,IAAIoyB,GAAahX,GAAS,GAEpC0Y,EAAKD,KACPA,EAAIC,GAAM,CAACA,EAAID,IAGjB,IAAIE,EAASF,EAAKC,EAClB,OAAiB,IAAVC,EAAc,GAAKF,EAAKC,GAAMC,CACtC,iBCde,SAAyB5Y,EAAQC,GAC/CD,EAASxJ,GAASwJ,GAClBC,EAASzJ,GAASyJ,GAElB,IAAIyY,EAAKvhC,KAAK0N,IAAIoyB,GAAajX,GAAS,GACpC2Y,EAAKxhC,KAAK0N,IAAIoyB,GAAahX,GAAS,GAMxC,OAJI0Y,EAAKD,KACPA,EAAIC,GAAM,CAACA,EAAID,KAGTA,EAAK,MAAQC,EAAK,IAC3B,gBCLe,SAAwB3Y,EAAQC,GAC9CD,EAASxJ,GAASwJ,GAClBC,EAASzJ,GAASyJ,GAElB,IAAIyY,EAAKvhC,KAAK0N,IAAIoyB,GAAajX,GAAS,GACpC2Y,EAAKxhC,KAAK0N,IAAIoyB,GAAahX,GAAS,GAMxC,OAJI0Y,EAAKD,KACPA,EAAIC,GAAM,CAACA,EAAID,IAGH,IAAPC,EAbI,KAacD,EAAKC,GAAMA,CACrC,ICtBO,SAASE,GAAI7jB,GAEnB,IAAK8jB,EAAGvB,EAAGwB,GAAKte,GAAOzF,EAAO0E,IAC1Bkf,EAAQE,EAAI,GAAKvB,EAAI,EAAIwB,EAC7B,MAAO,CAAC,EAAID,EAAIF,EAAO,EAAIrB,EAAIqB,EAChC,CAEO,SAASI,GAAIhkB,GAEnB,IAAK8jB,EAAGvB,EAAGwB,GAAKte,GAAOzF,EAAO0E,IACzBuf,EAAMH,EAAIvB,EAAIwB,EACnB,MAAO,CAACD,EAAIG,EAAK1B,EAAI0B,EACtB,+CAEO,SAAmB9B,GAGzBp/B,OAAOC,eAAem/B,EAAM3+B,UAAW,KAAM,CAC5CP,GAAAA,GACC,OAAO4gC,GAAGrhC,KACX,IAGDO,OAAOC,eAAem/B,EAAM3+B,UAAW,KAAM,CAC5CP,GAAAA,GACC,OAAO+gC,GAAGxhC,KACX,GAEF,gBC5Be,SAAS6W,GAAQiS,EAAIC,GAAY,IAARjX,EAAC3Q,UAAAuC,OAAA,QAAAN,IAAAjC,UAAA,GAAAA,UAAA,GAAG,CAAA,EACvCyQ,GAASE,KACZA,EAAI,CAACrO,OAAQqO,IAGd,IAAIrO,OAACA,EAASkT,GAASE,UAAW6qB,GAAQ5vB,EAE1C,IAAK,IAAIZ,KAAKmf,GACb,GAAI,SAAW5sB,EAAOsM,gBAAkBmB,EAAEnB,cACzC,OAAOsgB,GAAcnf,GAAG4X,EAAIC,EAAI2Y,GAIlC,MAAM,IAAIr/B,UAAW,0BAAyBoB,IAC/C,6CCTO,SAAiB+Z,GAAqB,IAAdmkB,EAAMxgC,UAAAuC,OAAA,QAAAN,IAAAjC,UAAA,GAAAA,UAAA,GAAG,IAGvC,OAAOoJ,GAAIiT,EADK,CADJO,GAAWtd,IAAI,QAAS,OACZ,MACKgjB,GAAKA,GAAK,EAAIke,IAC5C,UAVO,SAAkBnkB,GAAqB,IAAdmkB,EAAMxgC,UAAAuC,OAAA,QAAAN,IAAAjC,UAAA,GAAAA,UAAA,GAAG,IAGxC,OAAOoJ,GAAIiT,EADK,CADJO,GAAWtd,IAAI,QAAS,OACZ,MACKgjB,GAAKA,GAAK,EAAIke,IAC5C,ICmBO,SAASC,GAAK9Y,EAAIC,GAAoB,IAAhB3X,EAACjQ,UAAAuC,OAAA,QAAAN,IAAAjC,UAAA,GAAAA,UAAA,GAAG,GAAI2Q,EAAC3Q,UAAAuC,OAAA,QAAAN,IAAAjC,UAAA,GAAAA,UAAA,GAAG,CAAA,EAQxC,OAPC2nB,EAAIC,GAAM,CAAC/J,GAAS8J,GAAK9J,GAAS+J,IAEnB,WAAZ1d,GAAK+F,MACPA,EAAGU,GAAK,CAAC,GAAIV,IAGPkE,GAAMwT,EAAIC,EAAIjX,EACf2Q,CAAErR,EACV,CASO,SAASywB,GAAO/Y,EAAIC,GAAkB,IACxC+Y,EAD0B31B,EAAOhL,UAAAuC,OAAA,QAAAN,IAAAjC,UAAA,GAAAA,UAAA,GAAG,CAAA,EAGpC4gC,GAAQjZ,MAEVgZ,EAAY31B,GAAW,CAAC2c,EAAIC,IAC5BD,EAAIC,GAAM+Y,EAAWE,UAAUC,QAGjC,IAAIC,UACHA,EAASlQ,aAAEA,EAAY6P,MACvBA,EAAQ,EAACM,SAAEA,EAAW,OACnBC,GACAj2B,EAEC21B,KACHhZ,EAAIC,GAAM,CAAC/J,GAAS8J,GAAK9J,GAAS+J,IACnC+Y,EAAaxsB,GAAMwT,EAAIC,EAAIqZ,IAG5B,IAAIC,EAAaxrB,GAAOiS,EAAIC,GACxBuZ,EAAcJ,EAAY,EAAIviC,KAAK0N,IAAIw0B,EAAOliC,KAAKmN,KAAKu1B,EAAaH,GAAa,GAAKL,EACvFnwB,EAAM,GAMV,QAJiBtO,IAAb++B,IACHG,EAAc3iC,KAAK2N,IAAIg1B,EAAaH,IAGjB,IAAhBG,EACH5wB,EAAM,CAAC,CAACN,EAAG,GAAIoM,MAAOskB,EAAW,UAE7B,CACJ,IAAIS,EAAO,GAAKD,EAAc,GAC9B5wB,EAAMf,MAAMsE,KAAK,CAACvR,OAAQ4+B,IAAc,CAAChxB,EAAG9C,KAC3C,IAAI4C,EAAI5C,EAAI+zB,EACZ,MAAO,CAACnxB,IAAGoM,MAAOskB,EAAW1wB,GAAG,GAElC,CAEA,GAAI8wB,EAAY,EAAG,CAElB,IAAIM,EAAW9wB,EAAIkX,QAAO,CAACC,EAAK4Z,EAAKj0B,KACpC,GAAU,IAANA,EACH,OAAO,EAGR,IAAIk0B,EAAK7rB,GAAO4rB,EAAIjlB,MAAO9L,EAAIlD,EAAI,GAAGgP,MAAOwU,GAC7C,OAAOryB,KAAK0N,IAAIwb,EAAK6Z,EAAG,GACtB,GAEH,KAAOF,EAAWN,GAAW,CAG5BM,EAAW,EAEX,IAAK,IAAIh0B,EAAI,EAAIA,EAAIkD,EAAIhO,QAAYgO,EAAIhO,OAASy+B,EAAW3zB,IAAK,CACjE,IAAIm0B,EAAOjxB,EAAIlD,EAAI,GACfi0B,EAAM/wB,EAAIlD,GAEV4C,GAAKqxB,EAAIrxB,EAAIuxB,EAAKvxB,GAAK,EACvBoM,EAAQskB,EAAW1wB,GACvBoxB,EAAW7iC,KAAK0N,IAAIm1B,EAAU3rB,GAAO2G,EAAOmlB,EAAKnlB,OAAQ3G,GAAO2G,EAAOilB,EAAIjlB,QAC3E9L,EAAIkxB,OAAOp0B,EAAG,EAAG,CAAC4C,IAAGoM,MAAOskB,EAAW1wB,KACvC5C,GACD,CACD,CACD,CAIA,OAFAkD,EAAMA,EAAIP,KAAI5I,GAAKA,EAAEiV,QAEd9L,CACR,CASO,SAAS4D,GAAOkT,EAAQC,GAAsB,IAAdtc,EAAOhL,UAAAuC,OAAA,QAAAN,IAAAjC,UAAA,GAAAA,UAAA,GAAG,CAAA,EAChD,GAAI4gC,GAAQvZ,GAAS,CAEpB,IAAK/F,EAAGtW,GAAW,CAACqc,EAAQC,GAE5B,OAAOnT,MAASmN,EAAEuf,UAAUC,OAAQ,IAAIxf,EAAEuf,UAAU71B,WAAYA,GACjE,CAEA,IAAIqQ,MAACA,EAAKqmB,YAAEA,EAAWC,YAAEA,EAAWC,cAAEA,GAAiB52B,EAEvDqc,EAASxJ,GAASwJ,GAClBC,EAASzJ,GAASyJ,GAGlBD,EAASF,GAAME,GACfC,EAASH,GAAMG,GAEf,IAAIuZ,EAAY,CAACC,OAAQ,CAACzZ,EAAQC,GAAStc,WAoB3C,GAjBCqQ,EADGA,EACKuB,GAAWtd,IAAI+b,GAGfuB,GAAWW,SAAS/H,GAASqsB,qBAAuBxa,EAAOhM,MAGpEqmB,EAAcA,EAAc9kB,GAAWtd,IAAIoiC,GAAermB,EAE1DgM,EAAStT,GAAGsT,EAAQhM,GACpBiM,EAASvT,GAAGuT,EAAQjM,GAGpBgM,EAAS2J,GAAQ3J,GACjBC,EAAS0J,GAAQ1J,GAIbjM,EAAME,OAAOuH,GAA6B,UAAxBzH,EAAME,OAAOuH,EAAE5Y,KAAkB,CACtD,IAAI43B,EAAM92B,EAAQ+X,IAAM/X,EAAQ+X,KAAO,UAEnCA,EAAM,CAAC1H,EAAO,MACb0mB,EAAIC,GAAM,CAAC1iC,GAAI+nB,EAAQtE,GAAMzjB,GAAIgoB,EAAQvE,IAI1C5R,MAAM4wB,KAAQ5wB,MAAM6wB,GACvBD,EAAKC,EAEG7wB,MAAM6wB,KAAQ7wB,MAAM4wB,KAC5BC,EAAKD,IAELA,EAAIC,G5C3KA,SAAiBF,EAAKG,GAC5B,GAAY,QAARH,EACH,OAAOG,EAGR,IAAK3d,EAAII,GAAMud,EAAOjyB,IAAI2S,IAEtBuf,EAAYxd,EAAKJ,EA+BrB,MA7BY,eAARwd,EACCI,EAAY,IACfxd,GAAM,KAGS,eAARod,EACJI,EAAY,IACf5d,GAAM,KAGS,WAARwd,GACH,IAAMI,GAAaA,EAAY,MAC/BA,EAAY,EACf5d,GAAM,IAGNI,GAAM,KAIQ,YAARod,IACJI,EAAY,IACf5d,GAAM,IAEE4d,GAAa,MACrBxd,GAAM,MAID,CAACJ,EAAII,EACb,C4CoIaud,CAAcH,EAAK,CAACC,EAAIC,IACnC54B,GAAIie,EAAQtE,EAAKgf,GACjB34B,GAAIke,EAAQvE,EAAKif,EAClB,CAQA,OANIJ,IAEHva,EAAO9L,OAAS8L,EAAO9L,OAAOvL,KAAIQ,GAAKA,EAAI6W,EAAOlU,QAClDmU,EAAO/L,OAAS+L,EAAO/L,OAAOvL,KAAIQ,GAAKA,EAAI8W,EAAOnU,SAG5C/T,OAAO8d,QAAOjN,IACpBA,EAAI0xB,EAAcA,EAAY1xB,GAAKA,EACnC,IAAIsL,EAAS8L,EAAO9L,OAAOvL,KAAI,CAAC0D,EAAOrG,IAE/BoG,GAAYC,EADT4T,EAAO/L,OAAOlO,GACO4C,KAG5BkD,EAAQM,GAAY4T,EAAOlU,MAAOmU,EAAOnU,MAAOlD,GAChDM,EAAM,CAAC8K,QAAOE,SAAQpI,SAW1B,OATIyuB,IAEHrxB,EAAIgL,OAAShL,EAAIgL,OAAOvL,KAAIQ,GAAKA,EAAI2C,KAGlCuuB,IAAgBrmB,IACnB9K,EAAMwD,GAAGxD,EAAKmxB,IAGRnxB,CAAG,GACR,CACFswB,aAEF,CAEO,SAASD,GAASh8B,GACxB,MAAqB,aAAdsF,GAAKtF,MAAyBA,EAAIi8B,SAC1C,CAEArrB,GAASqsB,mBAAqB,+EAEvB,SAAmBrD,GACzBA,EAAM2D,eAAe,MAAO1B,GAAK,CAACxe,QAAS,UAC3Cuc,EAAM2D,eAAe,QAAShuB,GAAO,CAAC8N,QAAS,oBAC/Cuc,EAAM2D,eAAe,QAASzB,GAAO,CAACze,QAAS,gBAChD,aC1NemgB,GAAA,IAAIxlB,GAAW,CAC7B7W,GAAI,MACJU,KAAM,MACN8U,OAAQ,CACPuH,EAAG,CACF9G,SAAU,CAAC,EAAG,KACd9R,KAAM,QACNzD,KAAM,OAEPga,EAAG,CACFtM,MAAO,CAAC,EAAG,KACX1N,KAAM,cAEP6b,EAAG,CACFnO,MAAO,CAAC,EAAG,KACX1N,KAAM,cAIR+N,KAAM6oB,GAGNjf,SAAUuD,IACT,IAAIzV,EAAM1N,KAAK0N,OAAOyV,GAClBxV,EAAM3N,KAAK2N,OAAOwV,IACjBL,EAAGC,EAAGvH,GAAK2H,GACXmB,EAAGrC,EAAG6B,GAAK,CAACrP,IAAK,GAAI9G,EAAMD,GAAO,GACnCyI,EAAIzI,EAAMC,EAEd,GAAU,IAANwI,EAAS,CAGZ,OAFA8L,EAAW,IAAN6B,GAAiB,IAANA,EAAW,GAAKpW,EAAMoW,GAAK9jB,KAAK2N,IAAImW,EAAG,EAAIA,GAEnDpW,GACP,KAAKoV,EAAGwB,GAAKvB,EAAIvH,GAAKrF,GAAK4M,EAAIvH,EAAI,EAAI,GAAI,MAC3C,KAAKuH,EAAGuB,GAAK9I,EAAIsH,GAAK3M,EAAI,EAAG,MAC7B,KAAKqF,EAAG8I,GAAKxB,EAAIC,GAAK5M,EAAI,EAG3BmO,GAAQ,EACT,CAcA,OATIrC,EAAI,IACPqC,GAAK,IACLrC,EAAIjiB,KAAKgT,IAAIiP,IAGVqC,GAAK,MACRA,GAAK,KAGC,CAACA,EAAO,IAAJrC,EAAa,IAAJ6B,EAAQ,EAI7BjE,OAAQgkB,IACP,IAAKvf,EAAGrC,EAAG6B,GAAK+f,EAUhB,SAAS36B,EAAGqE,GACX,IAAIynB,GAAKznB,EAAI+W,EAAI,IAAM,GACnB1b,EAAIqZ,EAAIjiB,KAAK2N,IAAImW,EAAG,EAAIA,GAC5B,OAAOA,EAAIlb,EAAI5I,KAAK0N,KAAK,EAAG1N,KAAK2N,IAAIqnB,EAAI,EAAG,EAAIA,EAAG,GACpD,CAEA,OAfA1Q,GAAQ,IAEJA,EAAI,IACPA,GAAK,KAGNrC,GAAK,IACL6B,GAAK,IAQE,CAAC5a,EAAE,GAAIA,EAAE,GAAIA,EAAE,GAAG,EAG1BgW,QAAS,CACR2kB,IAAO,CACN9mB,OAAQ,CAAC,qBAAsB,eAAgB,iBAEhD+mB,KAAQ,CACP/mB,OAAQ,CAAC,qBAAsB,eAAgB,gBAC/CgY,QAAQ,EACR5V,WAAW,MC/EC4kB,GAAA,IAAI3lB,GAAW,CAC7B7W,GAAI,MACJU,KAAM,MACN8U,OAAQ,CACPuH,EAAG,CACF9G,SAAU,CAAC,EAAG,KACd9R,KAAM,QACNzD,KAAM,OAEPga,EAAG,CACFtM,MAAO,CAAC,EAAG,KACX1N,KAAM,cAEPqhB,EAAG,CACF3T,MAAO,CAAC,EAAG,KACX1N,KAAM,UAIR+N,KAAM4tB,GAENhkB,QAAAA,CAAUikB,GACT,IAAKvf,EAAGrC,EAAG6B,GAAK+f,EAChB5hB,GAAK,IACL6B,GAAK,IAEL,IAAIwF,EAAIxF,EAAI7B,EAAIjiB,KAAK2N,IAAImW,EAAG,EAAIA,GAEhC,MAAO,CACNQ,EACM,IAANgF,EAAU,EAAI,KAAO,EAAIxF,EAAIwF,GAC7B,IAAMA,EAEP,EAEDzJ,MAAAA,CAAQmkB,GACP,IAAK1f,EAAGrC,EAAGqH,GAAK0a,EAEhB/hB,GAAK,IACLqH,GAAK,IAEL,IAAIxF,EAAIwF,GAAK,EAAIrH,EAAI,GAErB,MAAO,CACNqC,EACO,IAANR,GAAiB,IAANA,EAAW,GAAMwF,EAAIxF,GAAK9jB,KAAK2N,IAAImW,EAAG,EAAIA,GAAM,IACxD,IAAJA,EAED,EAED5E,QAAS,CACRrB,MAAO,CACNtW,GAAI,QACJwV,OAAQ,CAAC,qBAAsB,0BAA2B,+BCrD9CknB,GAAA,IAAI7lB,GAAW,CAC7B7W,GAAI,MACJU,KAAM,MACN8U,OAAQ,CACPuH,EAAG,CACF9G,SAAU,CAAC,EAAG,KACd9R,KAAM,QACNzD,KAAM,OAEPi8B,EAAG,CACFvuB,MAAO,CAAC,EAAG,KACX1N,KAAM,aAEPuT,EAAG,CACF7F,MAAO,CAAC,EAAG,KACX1N,KAAM,cAIR+N,KAAM+tB,GACNnkB,QAAAA,CAAUokB,GACT,IAAK1f,EAAGrC,EAAGqH,GAAK0a,EAEhB,MAAO,CAAC1f,EAAGgF,GAAK,IAAMrH,GAAK,IAAK,IAAMqH,EACtC,EACDzJ,MAAAA,CAAQokB,GACP,IAAK3f,EAAG4f,EAAG1oB,GAAKyoB,EAGhBC,GAAK,IACL1oB,GAAK,IAGL,IAAIsmB,EAAMoC,EAAI1oB,EACd,GAAIsmB,GAAO,EAAG,CAEb,MAAO,CAACxd,EAAG,EAAU,KADV4f,EAAIpC,GAEhB,CAEA,IAAIxY,EAAK,EAAI9N,EAEb,MAAO,CAAC8I,EAAO,KADA,IAANgF,EAAW,EAAI,EAAI4a,EAAI5a,GACR,IAAJA,EACpB,EAEDpK,QAAS,CACR+kB,IAAO,CACNlnB,OAAQ,CAAC,qBAAsB,0BAA2B,+BClC7D,IAAeonB,GAAA,IAAIzhB,GAAc,CAChCnb,GAAI,gBACJ0X,MAAO,mBACPhX,KAAM,kCACN6X,MAAO,cAhBQ,CACf,CAAE,kBAAsB,kBAAsB,mBAC9C,CAAE,mBAAsB,kBAAsB,oBAC9C,CAAE,mBAAsB,mBAAsB,oBAe9CoD,UAZiB,CACjB,CAAG,oBAAwB,mBAAuB,oBAClD,EAAG,kBAAwB,mBAAuB,oBAClD,CAAG,qBAAwB,mBAAuB,uBCdpCkhB,GAAA,IAAI1hB,GAAc,CAChCnb,GAAI,SACJ0X,MAAO,UACPhX,KAAM,2BACN+N,KAAMmuB,GACNtkB,OAAQwV,GAAOA,EAAI7jB,KAAIpL,GAAOpG,KAAKkkB,IAAIlkB,KAAKgT,IAAI5M,GAAM,IAAM,KAAOpG,KAAK8V,KAAK1P,KAC7EwZ,SAAUyV,GAAOA,EAAI7jB,KAAIpL,GAAOpG,KAAKkkB,IAAIlkB,KAAKgT,IAAI5M,GAAM,IAAM,KAAOpG,KAAK8V,KAAK1P,OCUhF,IAAei+B,GAAA,IAAI3hB,GAAc,CAChCnb,GAAI,kBACJ0X,MAAO,wBACPhX,KAAM,kBACN6X,MAAO,MACP9J,KAAM0N,WAjBS,CACf,CAAE,kBAAsB,mBAAsB,mBAC9C,CAAE,kBAAsB,iBAAsB,mBAC9C,CAAE,EAAsB,EAAsB,oBAgB9CR,UAbiB,CACjB,CAAG,oBAAsB,oBAAsB,oBAC/C,EAAG,kBAAsB,mBAAsB,oBAC/C,CAAG,EAAsB,EAAsB,uBCVhD,IAAeohB,GAAA,IAAI5hB,GAAc,CAChCnb,GAAI,WACJ0X,MAAO,eACPhX,KAAM,WACN+N,KAAMquB,GACNxkB,OAAQwV,GAEAA,EAAI7jB,KAAI8X,GAAKA,EATV,OASoBA,EAAI,GAAKA,GAAK,MAE7C1J,SAAUyV,GACFA,EAAI7jB,KAAI8X,GAAKA,GAbX,WAaqBA,IAAM,EAAI,KAAO,GAAKA,MCZvCib,GAAA,IAAInmB,GAAW,CAC7B7W,GAAI,QACJU,KAAM,QACN8U,OAAQ,CACP+G,EAAG,CACFtG,SAAU,CAAC,EAAG,GACdvV,KAAM,aAEP+J,EAAG,CACFwL,SAAU,CAAC,EAAG,IACdvV,KAAM,UAEPqc,EAAG,CACF9G,SAAU,CAAC,EAAG,KACd9R,KAAM,QACNzD,KAAM,QAGR6X,MAAO,MAEP9J,KAAMoS,GACNxI,QAAAA,CAAU2I,GAET,IACIjE,GADCE,EAAG5b,EAAG4S,GAAK+M,EAEhB,MAAMjJ,EAAI,KASV,OANCgF,EADGtkB,KAAKgT,IAAIpK,GAAK0W,GAAKtf,KAAKgT,IAAIwI,GAAK8D,EAChC7K,IAGmB,IAAnBzU,KAAKykB,MAAMjJ,EAAG5S,GAAW5I,KAAK4T,GAG5B,CACN4Q,EACAxkB,KAAK0kB,KAAK9b,GAAK,EAAI4S,GAAK,GACxBmJ,GAAeL,GAEhB,EAEDzE,MAAAA,CAAQ0kB,GACP,IACI37B,EAAG4S,GADFgJ,EAAGpK,EAAGkK,GAAKigB,EAahB,OATI5xB,MAAM2R,IACT1b,EAAI,EACJ4S,EAAI,IAGJ5S,EAAIwR,EAAIpa,KAAKglB,IAAIV,EAAItkB,KAAK4T,GAAK,KAC/B4H,EAAIpB,EAAIpa,KAAKilB,IAAIX,EAAItkB,KAAK4T,GAAK,MAGzB,CAAE4Q,EAAG5b,EAAG4S,EACf,EAED0D,QAAS,CACRqlB,MAAS,CACRxnB,OAAQ,CAAC,0BAA2B,+BAAgC,0BC1DvE,IAAI+C,GAAQ7D,GAAOE,IAEnB,MACMyH,GAAI,MAAQ,IACX4gB,GAAeC,IAAiB/C,GAAG,CAAC7kB,MAAO0F,GAASxF,OAAQ+C,KAEnE,IAAe4kB,GAAA,IAAItmB,GAAW,CAC7B7W,GAAI,MACJU,KAAM,MACN8U,OAAQ,CACP+G,EAAG,CACFtG,SAAU,CAAC,EAAG,KACdvV,KAAM,aAGP08B,EAAG,CACFnnB,SAAU,EAAE,IAAK,MAElB8L,EAAG,CACF9L,SAAU,EAAE,IAAK,OAInBsC,MAAOA,GACP9J,KAAMuM,GAIN3C,QAAAA,CAAUpD,GACT,IAAI4G,EAAM,CAACvQ,GAAS2J,EAAI,IAAK3J,GAAS2J,EAAI,IAAK3J,GAAS2J,EAAI,KACxDgG,EAAIY,EAAI,IAEPwhB,EAAIC,GAAMnD,GAAG,CAAC7kB,MAAO0F,GAASxF,OAAQqG,IAG3C,IAAK1Q,OAAOoyB,SAASF,KAAQlyB,OAAOoyB,SAASD,GAC5C,MAAO,CAAC,EAAG,EAAG,GAGf,IAAIrgB,EAAIhC,GArCA,oBAqCSoB,GAAIpB,EAAI,IAAMxiB,KAAKgkB,KAAKxB,GAAK,GAC9C,MAAO,CACNgC,EACA,GAAKA,GAAKogB,EAAKJ,IACf,GAAKhgB,GAAKqgB,EAAKJ,IAEhB,EAID5kB,MAAAA,CAAQ6kB,GACP,IAAKlgB,EAAGmgB,EAAGrb,GAAKob,EAGhB,GAAU,IAANlgB,GAAWhS,GAAOgS,GACrB,MAAO,CAAC,EAAG,EAAG,GAGfmgB,EAAI9xB,GAAS8xB,GACbrb,EAAIzW,GAASyW,GAEb,IAAIsb,EAAMD,GAAK,GAAKngB,GAAMggB,GACtBK,EAAMvb,GAAK,GAAK9E,GAAMigB,GAEtBjiB,EAAIgC,GAAK,EAAIA,EAAIZ,GAAI5jB,KAAKkkB,KAAKM,EAAI,IAAM,IAAK,GAElD,MAAO,CACNhC,GAAM,EAAIoiB,GAAO,EAAIC,IACrBriB,EACAA,IAAM,GAAK,EAAIoiB,EAAK,GAAKC,IAAO,EAAIA,IAErC,EAED3lB,QAAS,CACRrB,MAAO,CACNtW,GAAI,QACJwV,OAAQ,CAAC,0BAA2B,gCAAiC,qCC7EzDgoB,GAAA,IAAI3mB,GAAW,CAC7B7W,GAAI,QACJU,KAAM,QACN8U,OAAQ,CACP+G,EAAG,CACFtG,SAAU,CAAC,EAAG,KACdvV,KAAM,aAEP+J,EAAG,CACFwL,SAAU,CAAC,EAAG,KACdvV,KAAM,UAEPqc,EAAG,CACF9G,SAAU,CAAC,EAAG,KACd9R,KAAM,QACNzD,KAAM,QAIR+N,KAAM0uB,GACN9kB,QAAAA,CAAU8kB,GAET,IACIngB,GADCC,EAAGmgB,EAAGrb,GAAKob,EAWhB,OANCngB,EADGvkB,KAAKgT,IAAI2xB,GAFH,KAEa3kC,KAAKgT,IAAIsW,GAFtB,IAGH7U,IAGmB,IAAnBzU,KAAKykB,MAAM6E,EAAGqb,GAAW3kC,KAAK4T,GAG9B,CACN4Q,EACAxkB,KAAK0kB,KAAKigB,GAAK,EAAIrb,GAAK,GACxB3E,GAAeJ,GAEhB,EACD1E,MAAAA,CAAQ+E,GAEP,IAAKC,EAAWC,EAAQC,GAAOH,EAS/B,OAPIE,EAAS,IACZA,EAAS,GAGNnS,MAAMoS,KACTA,EAAM,GAEA,CACNF,EACAC,EAAS9kB,KAAKglB,IAAID,EAAM/kB,KAAK4T,GAAK,KAClCkR,EAAS9kB,KAAKilB,IAAIF,EAAM/kB,KAAK4T,GAAK,KAEnC,EAEDsL,QAAS,CACRrB,MAAO,CACNtW,GAAI,UACJwV,OAAQ,CAAC,0BAA2B,0BAA2B,0BClClE,MAGMioB,GAAO9hB,GAAU,GAAG,GACpB+hB,GAAO/hB,GAAU,GAAG,GACpBgiB,GAAOhiB,GAAU,GAAG,GACpBiiB,GAAOjiB,GAAU,GAAG,GACpBkiB,GAAOliB,GAAU,GAAG,GACpBmiB,GAAOniB,GAAU,GAAG,GACpBoiB,GAAOpiB,GAAU,GAAG,GACpBqiB,GAAOriB,GAAU,GAAG,GACpBsiB,GAAOtiB,GAAU,GAAG,GAE1B,SAASuiB,GAAyBC,EAAOC,EAAWvhB,GACnD,MAAMjO,EAAIwvB,GAAa3lC,KAAKilB,IAAIb,GAASshB,EAAQ1lC,KAAKglB,IAAIZ,IAC1D,OAAOjO,EAAI,EAAIga,IAAWha,CAC3B,CAEO,SAASyvB,GAAwB9hB,GACvC,MAAM+hB,EAAO7lC,KAAKkkB,IAAIJ,EAAI,GAAI,GAAK,QAC7BgiB,EAAOD,EApBJ,oBAoBeA,EAAO/hB,EAnBtB,kBAoBHiiB,EAAMD,GAAQ,OAASd,GAAO,MAAQE,IACtCc,EAAMF,GAAQ,OAASZ,GAAO,OAASD,GAAO,OAASD,IACvDiB,EAAMH,GAAQ,OAASZ,GAAO,OAASD,IACvCiB,EAAMJ,GAAQ,OAASX,GAAO,MAAQE,IACtCc,EAAML,GAAQ,OAAST,GAAO,OAASD,GAAO,OAASD,IACvDiB,EAAMN,GAAQ,OAAST,GAAO,OAASD,IACvCiB,EAAMP,GAAQ,OAASR,GAAO,MAAQE,IACtCc,EAAMR,GAAQ,OAASN,GAAO,OAASD,GAAO,OAASD,IACvDiB,EAAMT,GAAQ,OAASN,GAAO,OAASD,IAE7C,MAAO,CACNiB,IAAKT,EAAME,EACXQ,IAAKT,EAAMliB,EAAImiB,EACfS,IAAKX,GAAOE,EAAM,QAClBU,KAAMX,EAAM,QAAUliB,GAAKmiB,EAAM,QACjCW,IAAKV,EAAME,EACXS,IAAKV,EAAMriB,EAAIsiB,EACfU,IAAKZ,GAAOE,EAAM,QAClBW,KAAMZ,EAAM,QAAUriB,GAAKsiB,EAAM,QACjCY,IAAKX,EAAME,EACXU,IAAKX,EAAMxiB,EAAIyiB,EACfW,IAAKb,GAAOE,EAAM,QAClBY,KAAMb,EAAM,QAAUxiB,GAAKyiB,EAAM,QAEnC,CAEA,SAASa,GAAoBC,EAAO/iB,GACnC,MAAMgjB,EAAShjB,EAAI,IAAMtkB,KAAK4T,GAAK,EAC7B2zB,EAAK9B,GAAwB4B,EAAMb,IAAKa,EAAMZ,IAAKa,GACnDE,EAAK/B,GAAwB4B,EAAMX,IAAKW,EAAMV,IAAKW,GACnDG,EAAKhC,GAAwB4B,EAAMT,IAAKS,EAAMR,IAAKS,GACnDI,EAAKjC,GAAwB4B,EAAMP,IAAKO,EAAMN,IAAKO,GACnDK,EAAKlC,GAAwB4B,EAAML,IAAKK,EAAMJ,IAAKK,GACnDvhB,EAAK0f,GAAwB4B,EAAMH,IAAKG,EAAMF,IAAKG,GAEzD,OAAOtnC,KAAK2N,IAAI45B,EAAIC,EAAIC,EAAIC,EAAIC,EAAI5hB,EACrC,CAEA,IAAe6hB,GAAA,IAAIxpB,GAAW,CAC7B7W,GAAI,QACJU,KAAM,QACN8U,OAAQ,CACPuH,EAAG,CACF9G,SAAU,CAAC,EAAG,KACd9R,KAAM,QACNzD,KAAM,OAEPga,EAAG,CACFtM,MAAO,CAAC,EAAG,KACX1N,KAAM,cAEP6b,EAAG,CACFnO,MAAO,CAAC,EAAG,KACX1N,KAAM,cAIR+N,KAAM+uB,GACN/kB,WAAY6e,GAGZjf,QAAAA,CAAUyE,GACT,IACIpC,GADC6B,EAAG9R,EAAGsS,GAAK,CAACzR,GAASwR,EAAI,IAAKxR,GAASwR,EAAI,IAAKxR,GAASwR,EAAI,KAGlE,GAAIP,EAAI,WACP7B,EAAI,EACJ6B,EAAI,SAEA,GAAIA,EAAI,KACZ7B,EAAI,EACJ6B,EAAI,MAEA,CAGJ7B,EAAIjQ,EADMo1B,GADExB,GAAuB9hB,GACCQ,GACtB,GACf,CAEA,MAAO,CAACA,EAAGrC,EAAG6B,EACd,EAGDjE,MAAAA,CAAQgkB,GACP,IACI7xB,GADCsS,EAAGrC,EAAG6B,GAAK,CAACjR,GAASgxB,EAAI,IAAKhxB,GAASgxB,EAAI,IAAKhxB,GAASgxB,EAAI,KAGlE,GAAI/f,EAAI,WACPA,EAAI,IACJ9R,EAAI,OAEA,GAAI8R,EAAI,KACZA,EAAI,EACJ9R,EAAI,MAEA,CAGJA,EADUo1B,GADExB,GAAuB9hB,GACCQ,GAC1B,IAAMrC,CACjB,CAEA,MAAO,CAAC6B,EAAG9R,EAAGsS,EACd,EAEDpF,QAAS,CACRrB,MAAO,CACNtW,GAAI,UACJwV,OAAQ,CAAC,qBAAsB,0BAA2B,+BCnH7D,SAAS8qB,GAAoBnC,EAAOC,GACnC,OAAO3lC,KAAKgT,IAAI2yB,GAAa3lC,KAAK0kB,KAAK1kB,KAAKkkB,IAAIwhB,EAAO,GAAK,EAC7D,CAEA,SAASoC,GAAoBT,GAC5B,IAAIE,EAAKM,GAAmBR,EAAMb,IAAKa,EAAMZ,KACzCe,EAAKK,GAAmBR,EAAMX,IAAKW,EAAMV,KACzCc,EAAKI,GAAmBR,EAAMT,IAAKS,EAAMR,KACzCa,EAAKG,GAAmBR,EAAMP,IAAKO,EAAMN,KACzCY,EAAKE,GAAmBR,EAAML,IAAKK,EAAMJ,KACzClhB,EAAK8hB,GAAmBR,EAAMH,IAAKG,EAAMF,KAE7C,OAAOnnC,KAAK2N,IAAI45B,EAAIC,EAAIC,EAAIC,EAAIC,EAAI5hB,EACrC,CAvBa7C,GAAU,GAAG,GACbA,GAAU,GAAG,GACbA,GAAU,GAAG,GACbA,GAAU,GAAG,GACbA,GAAU,GAAG,GACbA,GAAU,GAAG,GACbA,GAAU,GAAG,GACbA,GAAU,GAAG,GACbA,GAAU,GAAG,GAiB1B,IAAe6kB,GAAA,IAAI3pB,GAAW,CAC7B7W,GAAI,QACJU,KAAM,QACN8U,OAAQ,CACPuH,EAAG,CACF9G,SAAU,CAAC,EAAG,KACd9R,KAAM,QACNzD,KAAM,OAEPga,EAAG,CACFtM,MAAO,CAAC,EAAG,KACX1N,KAAM,cAEP6b,EAAG,CACFnO,MAAO,CAAC,EAAG,KACX1N,KAAM,cAIR+N,KAAM+uB,GACN/kB,WAAY,OAGZJ,QAAAA,CAAUyE,GACT,IACIpC,GADC6B,EAAG9R,EAAGsS,GAAK,CAACzR,GAASwR,EAAI,IAAKxR,GAASwR,EAAI,IAAKxR,GAASwR,EAAI,KAGlE,GAAIP,EAAI,WACP7B,EAAI,EACJ6B,EAAI,SAEA,GAAIA,EAAI,KACZ7B,EAAI,EACJ6B,EAAI,MAEA,CAGJ7B,EAAIjQ,EADM81B,GADElC,GAAuB9hB,IAErB,GACf,CACA,MAAO,CAACQ,EAAGrC,EAAG6B,EACd,EAGDjE,MAAAA,CAAQgkB,GACP,IACI7xB,GADCsS,EAAGrC,EAAG6B,GAAK,CAACjR,GAASgxB,EAAI,IAAKhxB,GAASgxB,EAAI,IAAKhxB,GAASgxB,EAAI,KAGlE,GAAI/f,EAAI,WACPA,EAAI,IACJ9R,EAAI,OAEA,GAAI8R,EAAI,KACZA,EAAI,EACJ9R,EAAI,MAEA,CAGJA,EADU81B,GADElC,GAAuB9hB,IAEzB,IAAM7B,CACjB,CAEA,MAAO,CAAC6B,EAAG9R,EAAGsS,EACd,EAEDpF,QAAS,CACRrB,MAAO,CACNtW,GAAI,UACJwV,OAAQ,CAAC,qBAAsB,0BAA2B,+BC3H7D,MACMxP,GAAI,KAAQ,MAGZy6B,GAAQ,GAAU,KAClB7e,GAAK,SACLC,GAAK,KAAQ,IACbI,GAAK,QAEX,IAAeye,GAAA,IAAIvlB,GAAc,CAChCnb,GAAI,YACJ0X,MAAO,aACPhX,KAAM,cACN+N,KAAMif,GACNpV,OAAQwV,GAGAA,EAAI7jB,KAAI,SAAUpL,GAExB,OAAY,KADFpG,KAAK0N,IAAMtH,GAAO4hC,GAAQ7e,GAAK,IAAMC,GAAMI,GAAMpjB,GAAO4hC,MAhBvD,kBAFH,GAoBT,IAEDpoB,SAAUyV,GAGFA,EAAI7jB,KAAI,SAAUpL,GACxB,IAAIkH,EAAItN,KAAK0N,IA1BL,IA0BStH,EAAW,IAAO,GAInC,QAHW+iB,GAAMC,GAAM9b,GAAKC,KACf,EAAKic,GAAMlc,GAAKC,MAzBtB,QA4BR,MC7BF,MAAM3E,GAAI,UACJ4S,GAAI,UACJxJ,GAAI,UAEJk2B,GAAQ,OAEd,IAAeC,GAAA,IAAIzlB,GAAc,CAChCnb,GAAI,aACJ0X,MAAO,cACPhX,KAAM,eACNmY,SAAU,QAEVpK,KAAMif,GACNpV,OAAQwV,GAGAA,EAAI7jB,KAAI,SAAUpL,GAKxB,OAAIA,GAAO,GACFA,GAAO,EAAK,EAAI8hC,IAEhBloC,KAAKiW,KAAK7P,EAAM4L,IAAKpJ,IAAK4S,IAAK,GAAM0sB,EAC/C,IAEDtoB,SAAUyV,GAIFA,EAAI7jB,KAAI,SAAUpL,GAMxB,OAJAA,GAAO8hC,KAII,EAAI,GACPloC,KAAK0kB,KAAK,EAAIte,GAEfwC,GAAI5I,KAAKwwB,IAAI,GAAKpqB,EAAMoV,IAAKxJ,EACrC,MC1CK,MAAMo2B,GAAO,CAAA,EAcb,SAASC,GAASh2B,GAA8B,IAA5B9K,GAACA,EAAE+gC,SAAEA,EAAQC,WAAEA,GAAWl2B,EAEpD+1B,GAAK7gC,GAAM/F,UAAU,EACtB,CAEO,SAAS6a,GAAOC,EAAIC,GAAqB,IAK3CzY,EAASskC,GALmB5mC,UAAAuC,OAAA,QAAAN,IAAAjC,UAAA,GAAAA,UAAA,GAAG,aAO9BgnC,EAAIC,EAAIC,GAAMt3B,GAAiBtN,EAAOwkC,SAAUhsB,IAChDqsB,EAAIC,EAAIC,GAAMz3B,GAAiBtN,EAAOwkC,SAAU/rB,GAUjDusB,EAAgB13B,GAPR,CACX,CAACu3B,EAAKH,EAAK,EAAU,GACrB,CAAC,EAAUI,EAAKH,EAAK,GACrB,CAAC,EAAU,EAAUI,EAAKH,IAIiB5kC,EAAOwkC,UAGnD,OAFcl3B,GAAiBtN,EAAOykC,WAAYO,EAGnD,CAvCAtyB,GAAMC,IAAI,8BAA8BK,IACnCA,EAAItK,QAAQ1I,SACfgT,EAAI2F,EAAIJ,GAAMvF,EAAIwF,GAAIxF,EAAIyF,GAAIzF,EAAItK,QAAQ1I,QAC3C,IAGD0S,GAAMC,IAAI,4BAA4BK,IAChCA,EAAI2F,IACR3F,EAAI2F,EAAIJ,GAAMvF,EAAIwF,GAAIxF,EAAIyF,GAAIzF,EAAItK,QAAQ1I,QAC3C,IAgCDukC,GAAU,CACT9gC,GAAI,YACJ+gC,SAAU,CACT,CAAG,OAAY,OAAY,QAC3B,EAAG,MAAY,QAAY,OAC3B,CAAG,EAAY,EAAY,SAE5BC,WAAY,CACX,CAAE,oBAAqB,mBAAsB,oBAC7C,CAAE,kBAAqB,mBAAsB,sBAC7C,CAAE,EAAqB,EAAsB,uBAI/CF,GAAU,CACT9gC,GAAI,WAGJ+gC,SAAU,CACT,CAAG,MAAY,OAAY,OAC3B,EAAG,MAAY,OAAY,OAC3B,CAAG,OAAY,MAAY,SAG5BC,WAAY,CACX,CAAG,mBAAqB,mBAAqB,oBAC7C,CAAG,kBAAqB,kBAAqB,qBAC7C,EAAG,mBAAqB,mBAAqB,oBAI/CF,GAAU,CACT9gC,GAAI,QAEJ+gC,SAAU,CACT,CAAG,MAAY,OAAY,OAC3B,EAAG,MAAY,OAAY,OAC3B,CAAG,KAAY,MAAY,QAE5BC,WAAY,CACX,CAAG,oBAAuB,mBAAqB,oBAC/C,CAAG,kBAAuB,kBAAqB,oBAC/C,EAAG,qBAAuB,mBAAqB,uBAIjDF,GAAU,CACT9gC,GAAI,QACJ+gC,SAAU,CACT,CAAG,QAAW,SAAW,SACzB,EAAG,QAAW,SAAW,SACzB,EAAG,QAAW,QAAW,UAG1BC,WAAY,CACX,CAAG,mBAAsB,mBAAqB,oBAC9C,CAAG,kBAAsB,mBAAqB,qBAC9C,EAAG,oBAAsB,mBAAqB,uBAIhD3nC,OAAO8d,OAAOzC,GAAQ,CAIrB5K,EAAK,CAAC,OAAS,EAAS,QAGxB+I,EAAK,CAAC,OAAS,EAAU,SAKzB2uB,IAAK,CAAC,OAAS,EAAS,QACxBC,IAAK,CAAC,OAAS,EAAS,SAGxBxV,EAAK,CAAC,EAAS,EAAS,GAGxByV,GAAK,CAAC,OAAS,EAAS,QACxBC,GAAK,CAAC,OAAS,EAAS,SACxBC,IAAK,CAAC,QAAS,EAAS,SCzHzBltB,GAAOmtB,KAAO,CAAC,OAAU,OAAS,EAAS,OAAgC,QAc3E,IAAeC,GAAA,IAAI3mB,GAAc,CAChCnb,GAAI,SACJ0X,MAAO,WACPhX,KAAM,SAKN8U,OAAQ,CACP+F,EAAG,CACFnN,MAAO,CAAC,EAAG,OACX1N,KAAM,OAEP8a,EAAG,CACFpN,MAAO,CAAC,EAAG,OACX1N,KAAM,SAEPuT,EAAG,CACF7F,MAAO,CAAC,EAAG,OACX1N,KAAM,SAIRmY,SAAU,QAEVN,MAAO7D,GAAOmtB,KAEdnmB,QAtCe,CACf,CAAG,kBAAsB,mBAAsB,mBAC/C,CAAG,mBAAsB,kBAAsB,oBAC/C,EAAG,oBAAsB,oBAAsB,qBAoC/CC,UAlCiB,CACjB,CAAG,oBAAuB,iBAAsB,oBAChD,EAAG,kBAAuB,mBAAsB,qBAChD,CAAG,qBAAuB,oBAAsB,sBCfjD,MAAM5D,GAAI,IAAM,GAIVgqB,IAAoB,UAGpBC,IAAevpC,KAAKwpC,KAAK,OAAS,MAAQ,MAEhD,IAAeC,GAAA,IAAI/mB,GAAc,CAChCnb,GAAI,SACJ0X,MAAO,WACPhX,KAAM,SASN8U,OAAQ,CACP+F,EAAG,CACFnN,MAAO,CAAC2zB,GAAkBC,IAC1BthC,KAAM,OAEP8a,EAAG,CACFpN,MAAO,CAAC2zB,GAAkBC,IAC1BthC,KAAM,SAEPuT,EAAG,CACF7F,MAAO,CAAC2zB,GAAkBC,IAC1BthC,KAAM,SAGRmY,SAAU,QAEVpK,KAAMqzB,GAENxpB,OAAQwV,GAGAA,EAAI7jB,KAAI,SAAUpL,GACxB,OAAIA,IAHO,kBAIiC,GAAnC,IAAa,MAANA,EAAe,MAAQkZ,IAE9BlZ,EAAMmjC,GACP,IAAa,MAANnjC,EAAe,MAGtB,KAET,IAIDwZ,SAAUyV,GACFA,EAAI7jB,KAAI,SAAUpL,GACxB,OAAIA,GAAO,GACFpG,KAAKwpC,KAAKlqB,IAAK,MAAQ,MAEvBlZ,EAAMkZ,IACLtf,KAAKwpC,KAAKlqB,GAAU,GAANlZ,GAAa,MAAQ,OAGnCpG,KAAKwpC,KAAKpjC,GAAO,MAAQ,KAEpC,uZC3Ca,MAAM45B,GAUpBpzB,WAAAA,GACC,IAAIiR,EAMAhB,EAAOE,EAAQpI,EANT,IAAA,IAAA4M,EAAA/f,UAAAuC,OADKqQ,EAAIpD,IAAAA,MAAAuQ,GAAAE,EAAA,EAAAA,EAAAF,EAAAE,IAAJrN,EAAIqN,GAAAjgB,UAAAigB,GAGC,IAAhBrN,EAAKrQ,SACR8Z,EAAQwB,GAASjL,EAAK,KAKnByJ,GACHhB,EAAQgB,EAAMhB,OAASgB,EAAMe,QAC7B7B,EAASc,EAAMd,OACfpI,EAAQkJ,EAAMlJ,QAIbkI,EAAOE,EAAQpI,GAASP,EAG1BxT,OAAOC,eAAeR,KAAM,QAAS,CACpCsB,MAAOyc,GAAWtd,IAAI+b,GACtB/a,UAAU,EACVF,YAAY,EACZC,cAAc,IAGfxB,KAAK0c,OAASA,EAASA,EAAOza,QAAU,CAAC,EAAG,EAAG,GAG/CjC,KAAKsU,MAAQA,EAAQ,QAAelR,IAAVkR,EAAsB,EAAKA,EAAQ,EAAI,EAAIA,EAGrE,IAAK,IAAI9F,EAAI,EAAGA,EAAIxO,KAAK0c,OAAOhZ,OAAQ8K,IAChB,QAAnBxO,KAAK0c,OAAOlO,KACfxO,KAAK0c,OAAOlO,GAAK4F,KAKnB,IAAK,IAAIlN,KAAMlH,KAAKwc,MAAME,OACzBnc,OAAOC,eAAeR,KAAMkH,EAAI,CAC/BzG,IAAKA,IAAMT,KAAKS,IAAIyG,GACpBqD,IAAKjJ,GAAStB,KAAKuK,IAAIrD,EAAI5F,IAG9B,CAEA,WAAIid,GACH,OAAOve,KAAKwc,MAAMtV,EACnB,CAEAohB,KAAAA,GACC,OAAO,IAAIqX,GAAM3/B,KAAKwc,MAAOxc,KAAK0c,OAAQ1c,KAAKsU,MAChD,CAEA+0B,MAAAA,GACC,MAAO,CACN9qB,QAASve,KAAKue,QACd7B,OAAQ1c,KAAK0c,OACbpI,MAAOtU,KAAKsU,MAEd,CAEAg1B,OAAAA,GAAkB,IAAA,IAAAC,EAAApoC,UAAAuC,OAANqQ,EAAIpD,IAAAA,MAAA44B,GAAAC,EAAA,EAAAA,EAAAD,EAAAC,IAAJz1B,EAAIy1B,GAAAroC,UAAAqoC,GACf,IAAI93B,E9B7DS,SAAkB8L,GAA0D,IAAnDhB,MAACA,EAAQ7F,GAAS2oB,iBAAkBnzB,GAAQhL,UAAAuC,OAAAvC,QAAAiC,IAAAjC,UAAAiC,GAAAjC,UAAG,GAAA,GAClFuQ,EAAMuiB,GAAUzW,EAAOrR,GAE3B,GAAmB,oBAARozB,KAAuBA,IAAIC,SAAS,QAAS9tB,KAASiF,GAAS2oB,cACzE5tB,EAAM,IAAIxN,OAAOwN,GACjBA,EAAI8L,MAAQA,MAER,CAEJ,IAAIisB,EAAgBjsB,EAKP,IAAAksB,EAAb,IAFclsB,EAAMd,OAAOitB,KAAKx3B,KAAWA,GAAOqL,EAAMlJ,mBAInDo1B,EAAErK,UAAY,IAAAqK,EAAAA,EAAZrK,GAAiBE,IAAIC,SAAS,QAAS,wBAE5CiK,EAAgBnhB,GAAM9K,GACtBisB,EAAc/sB,OAAS+sB,EAAc/sB,OAAOvL,IAAIqB,IAChDi3B,EAAcn1B,MAAQ9B,GAASi3B,EAAcn1B,OAE7C5C,EAAMuiB,GAAUwV,EAAet9B,GAE3BozB,IAAIC,SAAS,QAAS9tB,IAIzB,OAFAA,EAAM,IAAIxN,OAAOwN,GACjBA,EAAI8L,MAAQisB,EACL/3B,EAOV+3B,EAAgBv0B,GAAGu0B,EAAejtB,GAClC9K,EAAM,IAAIxN,OAAO+vB,GAAUwV,EAAet9B,IAC1CuF,EAAI8L,MAAQisB,CACb,CAEA,OAAO/3B,CACR,C8BoBY43B,CAAQtpC,QAAS+T,GAK3B,OAFArC,EAAI8L,MAAQ,IAAImiB,GAAMjuB,EAAI8L,OAEnB9L,CACR,CAMA,UAAOjR,CAAK+c,GACX,GAAIA,aAAiBmiB,GACpB,OAAOniB,EACP,IAAAosB,IAAAA,EAAAzoC,UAAAuC,OAHoBqQ,MAAIpD,MAAAi5B,EAAAA,EAAAA,OAAAC,EAAA,EAAAA,EAAAD,EAAAC,IAAJ91B,EAAI81B,EAAA1oC,GAAAA,UAAA0oC,GAKzB,OAAO,IAAIlK,GAAMniB,KAAUzJ,EAC5B,CAEA,qBAAOuvB,CAAgB17B,EAAMkiC,GAAgB,IAAVh4B,EAAC3Q,UAAAuC,OAAA,QAAAN,IAAAjC,UAAA,GAAAA,UAAA,GAAG2oC,GAClCC,SAACA,GAAW,EAAI3mB,QAAEA,GAAWtR,EAE7BnM,EAAO,WACV,IAAI+L,EAAMo4B,KAAK3oC,WAEf,GAAgB,UAAZiiB,EACH1R,EAAMiuB,GAAMl/B,IAAIiR,QAEZ,GAAgB,oBAAZ0R,EAA+B,CACvC,IAAIva,EAAI6I,EACRA,EAAM,WACL,IAAIA,EAAM7I,KAAE1H,WACZ,OAAOw+B,GAAMl/B,IAAIiR,IAGlBnR,OAAO8d,OAAO3M,EAAK7I,EACpB,KACqB,iBAAZua,IACR1R,EAAMA,EAAIP,KAAIQ,GAAKguB,GAAMl/B,IAAIkR,MAG9B,OAAOD,GAGF9J,KAAQ+3B,KACbA,GAAM/3B,GAAQjC,GAGXokC,IACHpK,GAAM3+B,UAAU4G,GAAQ,WAAmB,IAAA,IAAAoiC,EAAA7oC,UAAAuC,OAANqQ,EAAIpD,IAAAA,MAAAq5B,GAAAC,EAAA,EAAAA,EAAAD,EAAAC,IAAJl2B,EAAIk2B,GAAA9oC,UAAA8oC,GACxC,OAAOtkC,EAAK3F,QAAS+T,IAGxB,CAEA,sBAAOm2B,CAAiBp4B,GACvB,IAAK,IAAIlK,KAAQkK,EAChB6tB,GAAM2D,eAAe17B,EAAMkK,EAAElK,GAAOkK,EAAElK,GAExC,CAEA,aAAOuiC,CAAQ3jC,GACd,GAAIA,EAAQwa,SACXxa,EAAQwa,SAAS2e,SAIjB,IAAK,IAAI/3B,KAAQpB,EAChBm5B,GAAM2D,eAAe17B,EAAMpB,EAAQoB,GAGtC,EAGD+3B,GAAMuK,gBAAgB,CACrBzpC,OACAwiB,UACA1Y,OACA4Y,UACAjO,MACAiL,OCrLc,SAAiBqI,EAAQC,GAIvC,OAHAD,EAASxJ,GAASwJ,GAClBC,EAASzJ,GAASyJ,GAEXD,EAAOhM,QAAUiM,EAAOjM,OACrBgM,EAAOlU,QAAUmU,EAAOnU,OACxBkU,EAAO9L,OAAO2D,OAAM,CAAC1O,EAAGnD,IAAMmD,IAAM8W,EAAO/L,OAAOlO,IAC7D,ED+KCsR,WACAqS,WACA5J,YACAxmB,SAAUkyB,KAGX1zB,OAAO8d,OAAOshB,GAAO,CACpBviB,QACAjH,SACAyF,UACAwuB,MAAOrsB,GACPssB,OAAQtsB,GAAWW,SACnBrB,SAGA1G,cElMD,IAAK,IAAIxQ,KAAO5F,OAAO6J,KAAKigC,IAC3BtsB,GAAWiD,SAASqpB,GAAOlkC,0DCL5B,IAAI3F,EAAiBH,KAA+CwI,EAChE5B,EAASzE,KAGTgW,EAFkB9V,IAEFiF,CAAgB,sBAEpC2iC,GAAiB,SAAUl7B,EAAQm7B,EAAKj6B,GAClClB,IAAWkB,IAAQlB,EAASA,EAAOpO,WACnCoO,IAAWnI,EAAOmI,EAAQoJ,IAC5BhY,EAAe4O,EAAQoJ,EAAe,CAAEhX,cAAc,EAAMF,MAAOipC,sCCTvE,IAAI13B,EAAIxS,KACJT,EAAS4C,IACT8nC,EAAiB5nC,KAErBmQ,EAAE,CAAEjT,QAAQ,GAAQ,CAAE0b,QAAS,CAAE,IAIjCgvB,EAAe1qC,EAAO0b,QAAS,WAAW,MCA1C,IAAK,IAAIpU,KAAM6W,GAAWW,SACzB8rB,GAAkBtjC,EAAI6W,GAAWW,SAASxX,IAW3C,SAASsjC,GAAmBtjC,EAAIsV,GAC/B,IAAIiuB,EAASvjC,EAAG2E,QAAQ,KAAM,KAE9BtL,OAAOC,eAAem/B,GAAM3+B,UAAWypC,EAAQ,CAI9ChqC,GAAAA,GACC,IAAIiR,EAAM1R,KAAKijB,OAAO/b,GAEtB,MAAqB,oBAAVwjC,MAEHh5B,EAID,IAAIg5B,MAAMh5B,EAAK,CACrBlH,IAAKA,CAACqD,EAAK88B,KACV,IAEC,OADA5sB,GAAWsD,aAAa,CAAC7E,EAAOmuB,KACzB,CACR,CACA,MAAO3e,GAAI,CAEX,OAAO1Q,QAAQ9Q,IAAIqD,EAAK88B,EAAS,EAElClqC,IAAKA,CAACoN,EAAK88B,EAAUC,KACpB,GAAID,GAAgC,iBAAbA,KAA2BA,KAAY98B,GAAM,CACnE,IAAIL,MAACA,GAASuQ,GAAWsD,aAAa,CAAC7E,EAAOmuB,IAE9C,GAAIn9B,GAAS,EACZ,OAAOK,EAAIL,EAEb,CAEA,OAAO8N,QAAQ7a,IAAIoN,EAAK88B,EAAUC,EAAS,EAE5CrgC,IAAKA,CAACsD,EAAK88B,EAAUrpC,EAAOspC,KAC3B,GAAID,GAAgC,iBAAbA,KAA2BA,KAAY98B,IAAQ88B,GAAY,EAAG,CACpF,IAAIn9B,MAACA,GAASuQ,GAAWsD,aAAa,CAAC7E,EAAOmuB,IAE9C,GAAIn9B,GAAS,EAMZ,OALAK,EAAIL,GAASlM,EAGbtB,KAAKmjB,OAAOjc,EAAI2G,IAET,CAET,CAEA,OAAOyN,QAAQ/Q,IAAIsD,EAAK88B,EAAUrpC,EAAOspC,EAAS,GAGpD,EAIDrgC,GAAAA,CAAKmS,GACJ1c,KAAKmjB,OAAOjc,EAAIwV,EAChB,EACDlb,cAAc,EACdD,YAAY,GAEd,CAvEA4U,GAAMC,IAAI,uBAAuBoG,IAAS,IAAAquB,EACzCL,GAAkBhuB,EAAMtV,GAAIsV,GACf,QAAbquB,EAAAruB,EAAM8C,eAAO,IAAAurB,GAAbA,EAAet0B,SAAQ0K,IACtBupB,GAAkBvpB,EAAOzE,EAAM,GAC9B,ICRHmjB,GAAMwK,OAAO9Z,IACbsP,GAAMwK,OAAO,CAACtzB,YACdtW,OAAO8d,OAAOshB,GAAO,CAACtP,mBAItBsP,GAAMwK,OAAOW,IAGbnL,GAAMwK,OAAO,CAACnJ,SCdC,SAAmBb,EAAYC,GAAoB,IAARtuB,EAAC3Q,UAAAuC,OAAA,QAAAN,IAAAjC,UAAA,GAAAA,UAAA,GAAG,CAAA,EACzDyQ,GAASE,KACZA,EAAI,CAACi5B,UAAWj5B,IAGjB,IAAIi5B,UAACA,KAAcrJ,GAAQ5vB,EAE3B,IAAKi5B,EAAW,CACf,IAAIC,EAAazqC,OAAO6J,KAAK6gC,IAAoB95B,KAAI5I,GAAKA,EAAEsD,QAAQ,YAAa,MAAKC,KAAK,MAC3F,MAAM,IAAIzJ,UAAW,0EAAyE2oC,IAC/F,CAEA7K,EAAanhB,GAASmhB,GACtBC,EAAaphB,GAASohB,GAEtB,IAAK,IAAI73B,KAAK0iC,GACb,GAAI,WAAaF,EAAUh7B,gBAAkBxH,EAAEwH,cAC9C,OAAOk7B,GAAmB1iC,GAAG43B,EAAYC,EAAYsB,GAIvD,MAAM,IAAIr/B,UAAW,+BAA8B0oC,IACpD,IDLApL,GAAMwK,OAAOe,IAGbvL,GAAMwK,OAAOgB,IAGbxL,GAAMwK,OAAOiB,IAGbzL,GAAMwK,OAAOkB", "x_google_ignoreList": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 70, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 124, 126, 167, 168]}