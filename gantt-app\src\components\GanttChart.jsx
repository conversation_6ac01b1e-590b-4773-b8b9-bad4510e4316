import React, { useEffect, useRef, useState } from 'react'
import { Button, Space, Select, message } from 'antd'
import {
  PlusOutlined,
  SaveOutlined,
  ImportOutlined,
  ExportOutlined,
  UndoOutlined,
  RedoOutlined,
  ZoomInOutlined,
  ZoomOutOutlined,
  CalendarOutlined
} from '@ant-design/icons'
import Gantt from 'frappe-gantt'
import TaskModal from './TaskModal'
import { useGanttStore } from '../store/ganttStore'
import './GanttChart.css'

const { Option } = Select

const GanttChart = () => {
  const ganttRef = useRef(null)
  const ganttInstance = useRef(null)
  const [isModalVisible, setIsModalVisible] = useState(false)
  const [editingTask, setEditingTask] = useState(null)
  const [viewMode, setViewMode] = useState('Day')

  const {
    tasks,
    addTask,
    updateTask,
    deleteTask,
    undo,
    redo,
    canUndo,
    canRedo,
    exportData,
    importData
  } = useGanttStore()

  // 初始化甘特图
  useEffect(() => {
    if (ganttRef.current && tasks.length > 0) {
      try {
        ganttInstance.current = new Gantt(ganttRef.current, tasks, {
          view_mode: viewMode,
          date_format: 'YYYY-MM-DD',
          language: 'zh',
          bar_height: 30,
          bar_corner_radius: 3,
          arrow_curve: 5,
          padding: 18,
          view_mode_select: false,
          today_button: true,
          scroll_to: 'today',
          on_click: (task) => {
            setEditingTask(task)
            setIsModalVisible(true)
          },
          on_date_change: (task, start, end) => {
            updateTask(task.id, {
              start: start.toISOString().split('T')[0],
              end: end.toISOString().split('T')[0]
            })
          },
          on_progress_change: (task, progress) => {
            updateTask(task.id, { progress })
          }
        })
      } catch (error) {
        console.error('甘特图初始化失败:', error)
        message.error('甘特图初始化失败')
      }
    }
  }, [tasks, viewMode, updateTask])

  // 处理新建任务
  const handleAddTask = () => {
    setEditingTask(null)
    setIsModalVisible(true)
  }

  // 处理保存任务
  const handleSaveTask = (taskData) => {
    if (editingTask) {
      updateTask(editingTask.id, taskData)
    } else {
      addTask(taskData)
    }
    setIsModalVisible(false)
    setEditingTask(null)
  }

  // 处理视图模式切换
  const handleViewModeChange = (mode) => {
    setViewMode(mode)
    if (ganttInstance.current) {
      ganttInstance.current.change_view_mode(mode)
    }
  }

  // 处理导出
  const handleExport = () => {
    try {
      const data = exportData()
      const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' })
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `gantt-project-${new Date().toISOString().split('T')[0]}.json`
      a.click()
      URL.revokeObjectURL(url)
      message.success('项目数据导出成功')
    } catch (error) {
      message.error('导出失败')
    }
  }

  // 处理导入
  const handleImport = () => {
    const input = document.createElement('input')
    input.type = 'file'
    input.accept = '.json'
    input.onchange = (e) => {
      const file = e.target.files[0]
      if (file) {
        const reader = new FileReader()
        reader.onload = (e) => {
          try {
            const data = JSON.parse(e.target.result)
            importData(data)
            message.success('项目数据导入成功')
          } catch (error) {
            message.error('导入失败，请检查文件格式')
          }
        }
        reader.readAsText(file)
      }
    }
    input.click()
  }

  return (
    <div className="gantt-container">
      {/* 工具栏 */}
      <div className="gantt-toolbar">
        <Space>
          <Button 
            type="primary" 
            icon={<PlusOutlined />} 
            onClick={handleAddTask}
          >
            新建任务
          </Button>
          <Button 
            icon={<UndoOutlined />} 
            onClick={undo}
            disabled={!canUndo}
          >
            撤销
          </Button>
          <Button 
            icon={<RedoOutlined />} 
            onClick={redo}
            disabled={!canRedo}
          >
            重做
          </Button>
          <Button 
            icon={<ImportOutlined />} 
            onClick={handleImport}
          >
            导入
          </Button>
          <Button 
            icon={<ExportOutlined />} 
            onClick={handleExport}
          >
            导出
          </Button>
        </Space>
        
        <Space>
          <span>视图模式：</span>
          <Select 
            value={viewMode} 
            onChange={handleViewModeChange}
            style={{ width: 120 }}
          >
            <Option value="Day">日视图</Option>
            <Option value="Week">周视图</Option>
            <Option value="Month">月视图</Option>
            <Option value="Year">年视图</Option>
          </Select>
        </Space>
      </div>

      {/* 甘特图容器 */}
      <div className="gantt-chart-wrapper">
        <div ref={ganttRef} className="gantt-chart"></div>
      </div>

      {/* 任务编辑模态框 */}
      <TaskModal
        visible={isModalVisible}
        task={editingTask}
        onSave={handleSaveTask}
        onCancel={() => {
          setIsModalVisible(false)
          setEditingTask(null)
        }}
      />
    </div>
  )
}

export default GanttChart
