# 甘特图项目管理系统 - 部署说明

## 📋 系统简介

这是一个基于Web的甘特图项目管理系统，具有以下特性：
- ✅ 纯前端应用，无需数据库
- ✅ 支持任务创建、编辑、删除
- ✅ 拖拽调整任务时间和进度
- ✅ 任务依赖关系管理
- ✅ 撤销/重做功能
- ✅ 数据导入/导出（JSON格式）
- ✅ 本地存储，数据持久化
- ✅ 响应式设计，支持桌面和平板

## 🚀 部署步骤

### 1. 准备文件
将 `dist` 目录中的所有文件上传到您的PHP共享服务器：

```
您的网站根目录/
├── index.html          # 主页面
├── vite.svg            # 网站图标
└── assets/             # 资源文件夹
    ├── index-B09NZgKm.js    # JavaScript文件
    └── index-DbDPwpx3.css   # CSS样式文件
```

### 2. FTP上传
1. 使用FTP客户端（如FileZilla）连接到您的服务器
2. 将 `dist` 目录中的所有文件上传到网站根目录
3. 确保文件权限设置正确（一般为644）

### 3. 访问应用
上传完成后，直接访问您的域名即可使用：
```
http://您的域名.com/
```

## 💡 使用说明

### 基本操作
1. **新建任务**：点击工具栏"新建任务"按钮
2. **编辑任务**：双击任务条或右键菜单选择编辑
3. **删除任务**：在编辑对话框中点击删除按钮
4. **拖拽调整**：直接拖拽任务条调整开始时间和持续时间

### 高级功能
1. **设置依赖**：在任务编辑对话框中选择前置任务
2. **调整进度**：拖拽任务条上的进度指示器
3. **视图切换**：使用工具栏右侧的视图选择器（日/周/月/年）
4. **撤销重做**：使用工具栏的撤销/重做按钮

### 数据管理
1. **自动保存**：所有更改自动保存到浏览器本地存储
2. **导出数据**：点击"导出"按钮下载JSON文件
3. **导入数据**：点击"导入"按钮选择JSON文件

## 🔧 技术要求

### 服务器要求
- ✅ 支持静态文件托管的Web服务器
- ✅ 无需PHP、数据库或其他后端技术
- ✅ 支持HTTPS（推荐，但非必需）

### 浏览器要求
- ✅ Chrome 60+
- ✅ Firefox 55+
- ✅ Safari 12+
- ✅ Edge 79+

## 📱 响应式支持

应用支持以下设备：
- 🖥️ 桌面电脑（推荐分辨率：1920x1080及以上）
- 💻 笔记本电脑（最小分辨率：1366x768）
- 📱 平板设备（最小宽度：768px）

## 🛠️ 故障排除

### 常见问题

**Q: 页面显示空白**
A: 检查文件路径是否正确，确保所有文件都已上传

**Q: 样式显示异常**
A: 确保CSS文件已正确上传且可访问

**Q: 功能不工作**
A: 检查浏览器控制台是否有JavaScript错误

**Q: 数据丢失**
A: 数据存储在浏览器本地，清除浏览器数据会导致丢失。建议定期导出备份

### 文件完整性检查
确保以下文件存在且可访问：
- ✅ index.html
- ✅ assets/index-B09NZgKm.js
- ✅ assets/index-DbDPwpx3.css
- ✅ vite.svg

## 📞 技术支持

如果遇到问题，请检查：
1. 文件是否完整上传
2. 服务器是否支持静态文件
3. 浏览器是否支持现代JavaScript

## 🔄 更新说明

当需要更新应用时：
1. 重新构建项目（如有源码）
2. 替换服务器上的所有文件
3. 清除浏览器缓存以加载新版本

---

**版本**: 1.0.0  
**构建时间**: 2024年  
**技术栈**: React + Vite + Ant Design + Frappe Gantt
