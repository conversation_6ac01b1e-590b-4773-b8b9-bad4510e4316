<?php
// 甘特图系统 - 服务器环境测试
header('Content-Type: text/html; charset=utf-8');
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>服务器环境测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }
        .container { background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .success { color: #52c41a; }
        .error { color: #ff4d4f; }
        .info { color: #1890ff; }
        h1 { color: #333; border-bottom: 2px solid #1890ff; padding-bottom: 10px; }
        .test-item { margin: 15px 0; padding: 10px; background: #fafafa; border-radius: 4px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 甘特图系统 - 服务器环境测试</h1>
        
        <div class="test-item">
            <strong>PHP版本:</strong> 
            <span class="<?php echo version_compare(PHP_VERSION, '7.0.0', '>=') ? 'success' : 'error'; ?>">
                <?php echo PHP_VERSION; ?>
                <?php echo version_compare(PHP_VERSION, '7.0.0', '>=') ? ' ✅' : ' ❌ (建议7.0+)'; ?>
            </span>
        </div>

        <div class="test-item">
            <strong>服务器软件:</strong> 
            <span class="info"><?php echo $_SERVER['SERVER_SOFTWARE'] ?? '未知'; ?></span>
        </div>

        <div class="test-item">
            <strong>当前时间:</strong> 
            <span class="info"><?php echo date('Y-m-d H:i:s'); ?></span>
        </div>

        <div class="test-item">
            <strong>文档根目录:</strong> 
            <span class="info"><?php echo $_SERVER['DOCUMENT_ROOT'] ?? '未知'; ?></span>
        </div>

        <div class="test-item">
            <strong>静态文件测试:</strong>
            <div style="margin-top: 10px;">
                <?php
                $files = ['index.html', 'vite.svg', 'assets/index-B09NZgKm.js', 'assets/index-DbDPwpx3.css'];
                foreach ($files as $file) {
                    $exists = file_exists($file);
                    echo "<div style='margin: 5px 0;'>";
                    echo "<span class='" . ($exists ? 'success' : 'error') . "'>";
                    echo $file . ($exists ? ' ✅' : ' ❌');
                    echo "</span></div>";
                }
                ?>
            </div>
        </div>

        <div class="test-item">
            <strong>建议:</strong>
            <ul style="margin: 10px 0; padding-left: 20px;">
                <li>✅ 甘特图系统是纯前端应用，无需PHP支持</li>
                <li>✅ 只需要Web服务器能够提供静态文件即可</li>
                <li>✅ 如果所有文件显示 ✅，说明部署成功</li>
                <li>🔗 <a href="index.html" style="color: #1890ff;">点击这里访问甘特图系统</a></li>
            </ul>
        </div>

        <div class="test-item">
            <strong>故障排除:</strong>
            <ul style="margin: 10px 0; padding-left: 20px;">
                <li>如果文件显示 ❌，请检查FTP上传是否完整</li>
                <li>确保文件权限设置正确（一般为644）</li>
                <li>检查文件路径和文件名是否正确</li>
            </ul>
        </div>
    </div>
</body>
</html>
