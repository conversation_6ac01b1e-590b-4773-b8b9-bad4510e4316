# 甘特图项目管理系统 - Apache配置文件

# 启用压缩以提高加载速度
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>

# 设置缓存策略
<IfModule mod_expires.c>
    ExpiresActive on
    
    # 静态资源缓存1年
    ExpiresByType text/css "access plus 1 year"
    ExpiresByType application/javascript "access plus 1 year"
    ExpiresByType image/png "access plus 1 year"
    ExpiresByType image/svg+xml "access plus 1 year"
    
    # HTML文件缓存1小时
    ExpiresByType text/html "access plus 1 hour"
</IfModule>

# 设置MIME类型
<IfModule mod_mime.c>
    AddType application/javascript .js
    AddType text/css .css
    AddType image/svg+xml .svg
</IfModule>

# 安全设置
<IfModule mod_headers.c>
    # 防止点击劫持
    Header always append X-Frame-Options SAMEORIGIN
    
    # 防止MIME类型嗅探
    Header set X-Content-Type-Options nosniff
    
    # 启用XSS保护
    Header set X-XSS-Protection "1; mode=block"
</IfModule>

# 错误页面
ErrorDocument 404 /index.html

# 禁止访问敏感文件
<Files ".htaccess">
    Order allow,deny
    Deny from all
</Files>

<Files "*.md">
    Order allow,deny
    Deny from all
</Files>

# 默认首页
DirectoryIndex index.html index.php
