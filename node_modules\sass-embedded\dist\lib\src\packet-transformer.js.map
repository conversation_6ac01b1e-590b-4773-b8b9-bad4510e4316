{"version": 3, "file": "packet-transformer.js", "sourceRoot": "", "sources": ["../../../lib/src/packet-transformer.ts"], "names": [], "mappings": ";AAAA,uEAAuE;AACvE,gEAAgE;AAChE,uCAAuC;;;AAEvC,+BAAyC;AACzC,8CAAwC;AACxC,gDAAiD;AAEjD;;;;;;;;;;;;;GAaG;AACH,MAAa,iBAAiB;IAeT;IACA;IAfnB,gEAAgE;IACxD,MAAM,GAAG,IAAI,MAAM,EAAE,CAAC;IAE9B,4EAA4E;IAC5E,4BAA4B;IACX,0BAA0B,GAAG,IAAI,cAAO,EAAU,CAAC;IAEpE;;;OAGG;IACM,kBAAkB,GAAG,IAAI,CAAC,0BAA0B,CAAC,IAAI,EAAE,CAAC;IAErE,YACmB,gBAAwC,EACxC,kBAA4C;QAD5C,qBAAgB,GAAhB,gBAAgB,CAAwB;QACxC,uBAAkB,GAAlB,kBAAkB,CAA0B;QAE7D,IAAI,CAAC,gBAAgB;aAClB,IAAI,CAAC,IAAA,oBAAQ,EAAC,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC;aAC7C,SAAS,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC;IAChD,CAAC;IAED;;;OAGG;IACH,oBAAoB,CAAC,QAAoB;QACvC,IAAI,CAAC;YACH,IAAI,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC;YAC7B,IAAI,MAAM,KAAK,CAAC,EAAE,CAAC;gBACjB,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;gBACzC,OAAO;YACT,CAAC;YAED,yEAAyE;YACzE,eAAe;YACf,MAAM,MAAM,GAAG,IAAI,aAAa,CAAC,CAAC,CAAC,CAAC;YACpC,OAAO,MAAM,GAAG,CAAC,EAAE,CAAC;gBAClB,sEAAsE;gBACtE,mEAAmE;gBACnE,SAAS;gBACT,MAAM,CAAC,WAAW,CAAC,CAAC,MAAM,GAAG,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,MAAM,GAAG,IAAI,CAAC,CAAC,CAAC;gBACjE,MAAM,KAAK,CAAC,CAAC;YACf,CAAC;YAED,MAAM,MAAM,GAAG,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC;YAC7D,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YACpB,MAAM,CAAC,GAAG,CAAC,QAAQ,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC;YACpC,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC;QAClC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,0BAA0B,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;QAC/C,CAAC;IACH,CAAC;IAED,0EAA0E;IAC1E,sCAAsC;IAC9B,MAAM,CAAC,MAAkB;QAC/B,MAAM,QAAQ,GAAa,EAAE,CAAC;QAC9B,IAAI,YAAY,GAAG,CAAC,CAAC;QACrB,OAAO,YAAY,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC;YACpC,YAAY,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,CAAC;YAC9D,IAAI,IAAI,CAAC,MAAM,CAAC,UAAU,IAAI,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;gBAClD,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;gBACnC,IAAI,CAAC,MAAM,GAAG,IAAI,MAAM,EAAE,CAAC;YAC7B,CAAC;QACH,CAAC;QACD,OAAO,QAAQ,CAAC;IAClB,CAAC;CACF;AArED,8CAqEC;AAED,mEAAmE;AACnE,MAAM,MAAM;IACV,wEAAwE;IAChE,iBAAiB,GAAG,CAAC,CAAC;IAE9B,4CAA4C;IAC5C,EAAE;IACF,2EAA2E;IAC3E,sCAAsC;IACtC,EAAE;IACF,iFAAiF;IACzE,aAAa,GAAG,CAAC,CAAC;IAE1B;;;OAGG;IACH,OAAO,CAAU;IAEjB,4EAA4E;IACpE,aAAa,GAAG,CAAC,CAAC;IAE1B,mDAAmD;IACnD,IAAI,UAAU;QACZ,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,aAAa,CAAC,CAAC;IACtE,CAAC;IAED;;;;;OAKG;IACH,KAAK,CAAC,MAAkB;QACtB,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;YACpB,MAAM,KAAK,CAAC,qCAAqC,CAAC,CAAC;QACrD,CAAC;QAED,0EAA0E;QAC1E,+DAA+D;QAC/D,IAAI,CAAC,GAAG,CAAC,CAAC;QAEV,uCAAuC;QACvC,EAAE;QACF,4EAA4E;QAC5E,kEAAkE;QAClE,EAAE;QACF,6DAA6D;QAC7D,2EAA2E;QAC3E,cAAc;QACd,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;YAClB,SAAS,CAAC;gBACR,MAAM,IAAI,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;gBAEvB,wEAAwE;gBACxE,qCAAqC;gBACrC,IAAI,CAAC,aAAa,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,IAAI,CAAC,iBAAiB,CAAC;gBAC9D,IAAI,CAAC,iBAAiB,IAAI,CAAC,CAAC;gBAC5B,CAAC,EAAE,CAAC;gBAEJ,IAAI,IAAI,IAAI,IAAI,EAAE,CAAC;oBACjB,mEAAmE;oBACnE,qEAAqE;oBACrE,6BAA6B;oBAC7B,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;oBAChD,MAAM;gBACR,CAAC;qBAAM,IAAI,CAAC,KAAK,MAAM,CAAC,MAAM,EAAE,CAAC;oBAC/B,oEAAoE;oBACpE,8DAA8D;oBAC9D,mBAAmB;oBACnB,OAAO,CAAC,CAAC;gBACX,CAAC;qBAAM,CAAC;oBACN,oEAAoE;oBACpE,2BAA2B;gBAC7B,CAAC;YACH,CAAC;QACH,CAAC;QAED,2EAA2E;QAC3E,2EAA2E;QAC3E,2EAA2E;QAC3E,wDAAwD;QACxD,MAAM,YAAY,GAAG,IAAI,CAAC,GAAG,CAC3B,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,IAAI,CAAC,aAAa,EACxC,MAAM,CAAC,MAAM,GAAG,CAAC,CAClB,CAAC;QACF,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,GAAG,YAAY,CAAC,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;QAC3E,IAAI,CAAC,aAAa,IAAI,YAAY,CAAC;QAEnC,OAAO,CAAC,GAAG,YAAY,CAAC;IAC1B,CAAC;CACF"}