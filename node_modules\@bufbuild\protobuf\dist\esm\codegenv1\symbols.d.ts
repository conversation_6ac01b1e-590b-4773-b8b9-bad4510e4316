/**
 * @private
 */
export declare const packageName = "@bufbuild/protobuf";
/**
 * @private
 */
export declare const wktPublicImportPaths: Readonly<Record<string, string>>;
/**
 * @private
 */
export declare const symbols: {
    readonly codegen: {
        readonly boot: {
            readonly typeOnly: false;
            readonly bootstrapWktFrom: "../../codegenv1/boot.js";
            readonly from: string;
        };
        readonly fileDesc: {
            readonly typeOnly: false;
            readonly bootstrapWktFrom: "../../codegenv1/file.js";
            readonly from: string;
        };
        readonly enumDesc: {
            readonly typeOnly: false;
            readonly bootstrapWktFrom: "../../codegenv1/enum.js";
            readonly from: string;
        };
        readonly extDesc: {
            readonly typeOnly: false;
            readonly bootstrapWktFrom: "../../codegenv1/extension.js";
            readonly from: string;
        };
        readonly messageDesc: {
            readonly typeOnly: false;
            readonly bootstrapWktFrom: "../../codegenv1/message.js";
            readonly from: string;
        };
        readonly serviceDesc: {
            readonly typeOnly: false;
            readonly bootstrapWktFrom: "../../codegenv1/service.js";
            readonly from: string;
        };
        readonly tsEnum: {
            readonly typeOnly: false;
            readonly bootstrapWktFrom: "../../codegenv1/enum.js";
            readonly from: string;
        };
        readonly GenFile: {
            readonly typeOnly: true;
            readonly bootstrapWktFrom: "../../codegenv1/types.js";
            readonly from: string;
        };
        readonly GenEnum: {
            readonly typeOnly: true;
            readonly bootstrapWktFrom: "../../codegenv1/types.js";
            readonly from: string;
        };
        readonly GenExtension: {
            readonly typeOnly: true;
            readonly bootstrapWktFrom: "../../codegenv1/types.js";
            readonly from: string;
        };
        readonly GenMessage: {
            readonly typeOnly: true;
            readonly bootstrapWktFrom: "../../codegenv1/types.js";
            readonly from: string;
        };
        readonly GenService: {
            readonly typeOnly: true;
            readonly bootstrapWktFrom: "../../codegenv1/types.js";
            readonly from: string;
        };
    };
    readonly isMessage: {
        readonly typeOnly: false;
        readonly bootstrapWktFrom: "../../is-message.js";
        readonly from: "@bufbuild/protobuf";
    };
    readonly Message: {
        readonly typeOnly: true;
        readonly bootstrapWktFrom: "../../types.js";
        readonly from: "@bufbuild/protobuf";
    };
    readonly create: {
        readonly typeOnly: false;
        readonly bootstrapWktFrom: "../../create.js";
        readonly from: "@bufbuild/protobuf";
    };
    readonly fromJson: {
        readonly typeOnly: false;
        readonly bootstrapWktFrom: "../../from-json.js";
        readonly from: "@bufbuild/protobuf";
    };
    readonly fromJsonString: {
        readonly typeOnly: false;
        readonly bootstrapWktFrom: "../../from-json.js";
        readonly from: "@bufbuild/protobuf";
    };
    readonly fromBinary: {
        readonly typeOnly: false;
        readonly bootstrapWktFrom: "../../from-binary.js";
        readonly from: "@bufbuild/protobuf";
    };
    readonly toBinary: {
        readonly typeOnly: false;
        readonly bootstrapWktFrom: "../../to-binary.js";
        readonly from: "@bufbuild/protobuf";
    };
    readonly toJson: {
        readonly typeOnly: false;
        readonly bootstrapWktFrom: "../../to-json.js";
        readonly from: "@bufbuild/protobuf";
    };
    readonly toJsonString: {
        readonly typeOnly: false;
        readonly bootstrapWktFrom: "../../to-json.js";
        readonly from: "@bufbuild/protobuf";
    };
    readonly protoInt64: {
        readonly typeOnly: false;
        readonly bootstrapWktFrom: "../../proto-int64.js";
        readonly from: "@bufbuild/protobuf";
    };
    readonly JsonValue: {
        readonly typeOnly: true;
        readonly bootstrapWktFrom: "../../json-value.js";
        readonly from: "@bufbuild/protobuf";
    };
    readonly JsonObject: {
        readonly typeOnly: true;
        readonly bootstrapWktFrom: "../../json-value.js";
        readonly from: "@bufbuild/protobuf";
    };
};
