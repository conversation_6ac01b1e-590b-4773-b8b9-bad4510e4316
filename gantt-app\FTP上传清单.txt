📋 甘特图系统 - FTP上传清单

🎯 需要上传的文件（从 dist 目录）：

必需文件：
✅ index.html                    # 主页面文件
✅ vite.svg                      # 网站图标
✅ assets/index-B09NZgKm.js      # JavaScript主文件
✅ assets/index-DbDPwpx3.css     # CSS样式文件

可选文件：
⚙️ .htaccess                     # Apache服务器配置（推荐）
🔧 test.php                      # 服务器环境测试文件

📁 上传后的目录结构：
您的网站根目录/
├── index.html
├── vite.svg
├── .htaccess
├── test.php
└── assets/
    ├── index-B09NZgKm.js
    └── index-DbDPwpx3.css

🚀 上传步骤：
1. 打开FTP客户端（如FileZilla）
2. 连接到您的服务器
3. 进入网站根目录（通常是 public_html 或 www）
4. 上传 dist 目录中的所有文件
5. 保持目录结构不变

✅ 验证步骤：
1. 访问 http://您的域名.com/test.php 检查环境
2. 访问 http://您的域名.com/ 使用甘特图系统
3. 测试创建、编辑任务功能
4. 测试数据导入/导出功能

⚠️ 注意事项：
- 确保文件权限设置为 644
- 目录权限设置为 755
- 保持文件名大小写一致
- 不要修改 assets 目录中的文件名

🔧 故障排除：
- 如果页面空白：检查 JavaScript 文件是否正确上传
- 如果样式异常：检查 CSS 文件是否正确上传
- 如果功能不工作：检查浏览器控制台错误信息

📞 技术支持：
- 这是纯前端应用，无需PHP或数据库支持
- 只要服务器能提供静态文件即可正常运行
- 数据存储在用户浏览器本地，无需服务器存储
